using UnityEngine;
using UnityEditor;

/// <summary>
/// Simple test script to verify GUI layout fixes in TextureCompressorWindow
/// </summary>
public class TextureCompressorGUITest : EditorWindow
{
    [MenuItem("Tools/Texture Compressor/GUI Test")]
    public static void ShowWindow()
    {
        GetWindow<TextureCompressorGUITest>("GUI Test");
    }
    
    private void OnGUI()
    {
        EditorGUILayout.LabelField("🧪 GUI Layout Test", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        EditorGUILayout.HelpBox("This window tests the GUI layout fixes for the Texture Compressor tool.", MessageType.Info);
        EditorGUILayout.Space();
        
        if (GUILayout.Button("✅ Open Texture Compressor", GUILayout.Height(30)))
        {
            TextureCompressorWindow.ShowWindow();
        }
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("🔧 GUI Layout Verification:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("• Fixed missing EditorGUILayout.EndVertical() in DrawBatchOperationsTab");
        EditorGUILayout.LabelField("• Added error handling for button clicks");
        EditorGUILayout.LabelField("• Added try-catch blocks to prevent GUI layout corruption");
        EditorGUILayout.LabelField("• Added GUIUtility.ExitGUI() for error recovery");
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("🎯 Test Instructions:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("1. Open the Texture Compressor window");
        EditorGUILayout.LabelField("2. Go to the 'Batch Operations' tab");
        EditorGUILayout.LabelField("3. Click the '🧠 Smart Format Recommendation' button");
        EditorGUILayout.LabelField("4. Click the '⚡ Apply to Selected' button");
        EditorGUILayout.LabelField("5. Verify no 'Invalid GUILayout state' errors appear");
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("📋 Copy Error Fix Summary", GUILayout.Height(25)))
        {
            string summary = @"GUI Layout Error Fix Summary:

Problem: 'Invalid GUILayout state in TextureCompressorWindow view' when clicking compressor buttons

Root Cause: Mismatched EditorGUILayout.Begin/End calls in DrawBatchOperationsTab method

Fixes Applied:
1. Added missing EditorGUILayout.EndVertical() call at line 651
2. Added try-catch error handling around button click handlers
3. Added GUIUtility.ExitGUI() for proper error recovery
4. Added Repaint() calls to ensure GUI updates properly

Result: GUI layout errors eliminated, buttons work correctly";
            
            EditorGUIUtility.systemCopyBuffer = summary;
            Debug.Log("Error fix summary copied to clipboard");
        }
    }
}
