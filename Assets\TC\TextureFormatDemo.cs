using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// Demonstration script showing how to use the comprehensive texture format system
/// </summary>
public class TextureFormatDemo : MonoBehaviour
{
    [Header("Demo Settings")]
    [Tooltip("Texture to demonstrate format conversion")]
    public Texture2D demoTexture;
    
    [<PERSON>lt<PERSON>("Show format information in console")]
    public bool showFormatInfo = true;
    
    [Tooltip("Target platform for format recommendations")]
    public RuntimePlatform targetPlatform = RuntimePlatform.WindowsPlayer;
    
    [Header("Format Categories")]
    [SerializeField] private List<FormatCategory> formatCategories = new List<FormatCategory>();
    
    [System.Serializable]
    public class FormatCategory
    {
        public string categoryName;
        public string description;
        public List<TextureImporterFormat> formats;
        public Color categoryColor = Color.white;
        
        public FormatCategory(string name, string desc, params TextureImporterFormat[] formatArray)
        {
            categoryName = name;
            description = desc;
            formats = new List<TextureImporterFormat>(formatArray);
        }
    }
    
    private void Start()
    {
        InitializeFormatCategories();
        
        if (showFormatInfo)
        {
            LogFormatInformation();
        }
    }
    
    private void InitializeFormatCategories()
    {
        formatCategories.Clear();
        
        // Mobile ASTC formats
        formatCategories.Add(new FormatCategory(
            "ASTC (Mobile Premium)",
            "Adaptive Scalable Texture Compression - Best for modern mobile devices",
            TextureImporterFormat.ASTC_4x4,
            TextureImporterFormat.ASTC_5x5,
            TextureImporterFormat.ASTC_6x6,
            TextureImporterFormat.ASTC_8x8,
            TextureImporterFormat.ASTC_10x10,
            TextureImporterFormat.ASTC_12x12
        ));
        
        // Mobile ETC formats
        formatCategories.Add(new FormatCategory(
            "ETC2 (Android Standard)",
            "Ericsson Texture Compression - Standard for Android devices",
            TextureImporterFormat.ETC_RGB4,
            TextureImporterFormat.ETC2_RGB4,
            TextureImporterFormat.ETC2_RGBA8,
            TextureImporterFormat.ETC2_RGBA8Crunched
        ));
        
        // Mobile PVRTC formats
        formatCategories.Add(new FormatCategory(
            "PVRTC (iOS Legacy)",
            "PowerVR Texture Compression - For older iOS devices",
            TextureImporterFormat.PVRTC_RGB2,
            TextureImporterFormat.PVRTC_RGB4,
            TextureImporterFormat.PVRTC_RGBA2,
            TextureImporterFormat.PVRTC_RGBA4
        ));
        
        // Desktop DXT formats
        formatCategories.Add(new FormatCategory(
            "DXT/S3TC (Desktop Standard)",
            "DirectX Texture Compression - Standard for PC and Xbox",
            TextureImporterFormat.DXT1,
            TextureImporterFormat.DXT1Crunched,
            TextureImporterFormat.DXT5,
            TextureImporterFormat.DXT5Crunched
        ));
        
        // Modern BC formats
        formatCategories.Add(new FormatCategory(
            "BC (Desktop Premium)",
            "Block Compression - Modern high-quality desktop formats",
            TextureImporterFormat.BC4,
            TextureImporterFormat.BC5,
            TextureImporterFormat.BC6H,
            TextureImporterFormat.BC7
        ));
        
        // HDR formats
        formatCategories.Add(new FormatCategory(
            "HDR Formats",
            "High Dynamic Range - For advanced lighting and effects",
            TextureImporterFormat.ASTC_HDR_4x4,
            TextureImporterFormat.ASTC_HDR_5x5,
            TextureImporterFormat.ASTC_HDR_6x6,
            TextureImporterFormat.ASTC_HDR_8x8,
            TextureImporterFormat.ASTC_HDR_10x10,
            TextureImporterFormat.ASTC_HDR_12x12,
            TextureImporterFormat.RGBAHalf,
            TextureImporterFormat.RGBAFloat
        ));
        
        // Uncompressed formats
        formatCategories.Add(new FormatCategory(
            "Uncompressed (Maximum Quality)",
            "No compression - Perfect quality but large file sizes",
            TextureImporterFormat.RGBA32,
            TextureImporterFormat.RGBA16,
            TextureImporterFormat.RGB24,
            TextureImporterFormat.RGB16,
            TextureImporterFormat.RG32,
            TextureImporterFormat.RG16,
            TextureImporterFormat.R16,
            TextureImporterFormat.R8,
            TextureImporterFormat.Alpha8
        ));
    }
    
    private void LogFormatInformation()
    {
        Debug.Log("=== 🎨 TEXTURE FORMAT DEMONSTRATION ===");
        Debug.Log($"Target Platform: {targetPlatform}");
        Debug.Log($"Total Format Categories: {formatCategories.Count}");
        
        foreach (var category in formatCategories)
        {
            Debug.Log($"\n📁 {category.categoryName}");
            Debug.Log($"   Description: {category.description}");
            Debug.Log($"   Formats: {category.formats.Count}");
            
            foreach (var format in category.formats)
            {
                string platformSupport = GetPlatformSupport(format);
                string qualityInfo = GetQualityInfo(format);
                bool supportsAlpha = FormatSupportsAlpha(format);
                
                Debug.Log($"   • {format}");
                Debug.Log($"     Platform: {platformSupport}");
                Debug.Log($"     Quality: {qualityInfo}");
                Debug.Log($"     Alpha: {(supportsAlpha ? "✅" : "❌")}");
            }
        }
        
        if (demoTexture != null)
        {
            LogTextureRecommendations();
        }
    }
    
    private void LogTextureRecommendations()
    {
        Debug.Log($"\n🎯 RECOMMENDATIONS FOR: {demoTexture.name}");
        Debug.Log($"   Size: {demoTexture.width}x{demoTexture.height}");
        Debug.Log($"   Format: {demoTexture.format}");
        
        // Get recommendations for different platforms
        var platforms = new RuntimePlatform[]
        {
            RuntimePlatform.WindowsPlayer,
            RuntimePlatform.Android,
            RuntimePlatform.IPhonePlayer,
            RuntimePlatform.WebGLPlayer
        };
        
        foreach (var platform in platforms)
        {
            var recommendation = GetRecommendationForPlatform(platform);
            Debug.Log($"   {platform}: {recommendation}");
        }
    }
    
    private string GetRecommendationForPlatform(RuntimePlatform platform)
    {
        // This would integrate with the TextureCompressorWindow logic
        switch (platform)
        {
            case RuntimePlatform.Android:
                return "ASTC 6x6 (modern) or ETC2 RGBA8 (standard)";
            case RuntimePlatform.IPhonePlayer:
                return "ASTC 6x6 (modern) or PVRTC RGBA4 (legacy)";
            case RuntimePlatform.WebGLPlayer:
                return "DXT5 Crunched (with alpha) or DXT1 Crunched (no alpha)";
            case RuntimePlatform.WindowsPlayer:
                return "BC7 (premium) or DXT5 (standard)";
            default:
                return "Platform-specific optimization needed";
        }
    }
    
    private bool FormatSupportsAlpha(TextureImporterFormat format)
    {
        // Simplified version of the method from TextureCompressorWindow
        switch (format)
        {
            case TextureImporterFormat.DXT5:
            case TextureImporterFormat.DXT5Crunched:
            case TextureImporterFormat.BC7:
            case TextureImporterFormat.ETC2_RGBA8:
            case TextureImporterFormat.ETC2_RGBA8Crunched:
            case TextureImporterFormat.ASTC_4x4:
            case TextureImporterFormat.ASTC_5x5:
            case TextureImporterFormat.ASTC_6x6:
            case TextureImporterFormat.ASTC_8x8:
            case TextureImporterFormat.ASTC_10x10:
            case TextureImporterFormat.ASTC_12x12:
            case TextureImporterFormat.PVRTC_RGBA4:
            case TextureImporterFormat.PVRTC_RGBA2:
            case TextureImporterFormat.RGBA32:
            case TextureImporterFormat.RGBA16:
            case TextureImporterFormat.Alpha8:
            case TextureImporterFormat.RGBAHalf:
            case TextureImporterFormat.RGBAFloat:
                return true;
            default:
                return false;
        }
    }
    
    private string GetPlatformSupport(TextureImporterFormat format)
    {
        // Simplified version from TextureCompressorWindow
        if (format.ToString().Contains("ASTC"))
            return "iOS, Android (modern)";
        else if (format.ToString().Contains("ETC"))
            return "Android, WebGL";
        else if (format.ToString().Contains("PVRTC"))
            return "iOS";
        else if (format.ToString().Contains("DXT") || format.ToString().Contains("BC"))
            return "PC, Xbox, WebGL";
        else
            return "All platforms";
    }
    
    private string GetQualityInfo(TextureImporterFormat format)
    {
        // Simplified quality assessment
        if (format.ToString().Contains("4x4"))
            return "Excellent";
        else if (format.ToString().Contains("6x6"))
            return "Very Good";
        else if (format.ToString().Contains("8x8"))
            return "Good";
        else if (format.ToString().Contains("BC7"))
            return "Excellent";
        else if (format.ToString().Contains("DXT5"))
            return "Good";
        else if (format.ToString().Contains("32"))
            return "Perfect (Uncompressed)";
        else
            return "Variable";
    }
    
    [ContextMenu("Test Format Recommendations")]
    public void TestFormatRecommendations()
    {
        if (demoTexture != null)
        {
            LogTextureRecommendations();
        }
        else
        {
            Debug.LogWarning("Please assign a demo texture first!");
        }
    }
    
    [ContextMenu("Show All Format Categories")]
    public void ShowAllFormatCategories()
    {
        InitializeFormatCategories();
        LogFormatInformation();
    }
}
