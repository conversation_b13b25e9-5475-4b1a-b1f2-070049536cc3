using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;
using System.IO;
using System.Linq;

namespace TextureCompressorTool
{
    /// <summary>
    /// Advanced optimization profiles and machine learning recommendations
    /// </summary>
    [System.Serializable]
    public class OptimizationProfile
    {
        public string name;
        public string description;
        public BuildTarget targetPlatform;
        public TextureAnalyzer.OptimizationPriority priority;
        public Dictionary<TextureAnalyzer.TextureContentType, FormatSettings> formatSettings;
        public QualitySettings qualitySettings;
        public PerformanceSettings performanceSettings;
        public bool isCustom;
        public DateTime createdDate;
        public string version;

        [System.Serializable]
        public class FormatSettings
        {
            public TextureImporterFormat preferredFormat;
            public TextureImporterFormat fallbackFormat;
            public int maxSize;
            public int minSize;
            public int qualityLevel;
            public bool useCrunchCompression;
            public bool generateMipmaps;
            public FilterMode filterMode;
            public TextureWrapMode wrapMode;
        }

        [System.Serializable]
        public class QualitySettings
        {
            public float qualityThreshold = 0.8f;
            public float compressionTolerance = 0.3f;
            public bool preserveAlpha = true;
            public bool maintainAspectRatio = true;
            public bool allowNonPowerOfTwo = false;
        }

        [System.Serializable]
        public class PerformanceSettings
        {
            public bool enableStreamingMipmaps = false;
            public int streamingMipmapsPriority = 0;
            public bool optimizeForMemory = true;
            public bool optimizeForLoadTime = false;
            public int maxConcurrentOperations = 4;
        }

        public OptimizationProfile()
        {
            formatSettings = new Dictionary<TextureAnalyzer.TextureContentType, FormatSettings>();
            qualitySettings = new QualitySettings();
            performanceSettings = new PerformanceSettings();
            createdDate = DateTime.Now;
            version = "1.0";
        }
    }

    public static class TextureOptimizationProfiles
    {
        private static List<OptimizationProfile> profiles;
        private static string profilesPath = "Assets/TC/Editor/OptimizationProfiles.json";
        
        // Machine Learning Data
        private static Dictionary<string, OptimizationStats> optimizationHistory = new Dictionary<string, OptimizationStats>();
        
        [System.Serializable]
        public class OptimizationStats
        {
            public string textureGuid;
            public TextureAnalyzer.TextureContentType contentType;
            public List<OptimizationAttempt> attempts = new List<OptimizationAttempt>();
            public float averageQualityScore;
            public float averageCompressionRatio;
            public DateTime lastOptimized;
        }

        [System.Serializable]
        public class OptimizationAttempt
        {
            public TextureImporterFormat format;
            public int size;
            public int quality;
            public float resultingQualityScore;
            public float compressionRatio;
            public float userRating; // User feedback on quality
            public DateTime timestamp;
        }

        static TextureOptimizationProfiles()
        {
            LoadProfiles();
            LoadOptimizationHistory();
        }

        /// <summary>
        /// Get all available optimization profiles
        /// </summary>
        public static List<OptimizationProfile> GetProfiles()
        {
            if (profiles == null)
            {
                InitializeDefaultProfiles();
            }
            return profiles;
        }

        /// <summary>
        /// Get profile by name
        /// </summary>
        public static OptimizationProfile GetProfile(string name)
        {
            return GetProfiles().Find(p => p.name == name);
        }

        /// <summary>
        /// Create a new custom profile
        /// </summary>
        public static OptimizationProfile CreateCustomProfile(string name, string description, BuildTarget platform)
        {
            var profile = new OptimizationProfile
            {
                name = name,
                description = description,
                targetPlatform = platform,
                isCustom = true,
                priority = TextureAnalyzer.OptimizationPriority.Balanced
            };

            InitializeProfileSettings(profile);
            profiles.Add(profile);
            SaveProfiles();
            
            return profile;
        }

        /// <summary>
        /// Get AI-powered recommendations based on texture analysis and historical data
        /// </summary>
        public static OptimizationProfile.FormatSettings GetAIRecommendation(Texture2D texture, TextureAnalyzer.AnalysisResult analysis, BuildTarget platform)
        {
            var recommendation = new OptimizationProfile.FormatSettings();
            
            // Get base recommendation from analysis
            recommendation.preferredFormat = analysis.recommendedFormat;
            recommendation.maxSize = analysis.recommendedSize;
            recommendation.qualityLevel = 50;
            
            // Apply machine learning insights
            var textureGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(texture));
            if (optimizationHistory.ContainsKey(textureGuid))
            {
                var history = optimizationHistory[textureGuid];
                ApplyHistoricalLearning(recommendation, history, analysis);
            }
            
            // Apply platform-specific optimizations
            ApplyPlatformOptimizations(recommendation, platform, analysis);
            
            // Apply content-type specific optimizations
            ApplyContentTypeOptimizations(recommendation, analysis.contentType, analysis);
            
            return recommendation;
        }

        /// <summary>
        /// Record optimization attempt for machine learning
        /// </summary>
        public static void RecordOptimizationAttempt(Texture2D texture, TextureImporterFormat format, int size, int quality, 
            float qualityScore, float compressionRatio, float userRating = 0f)
        {
            var textureGuid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(texture));
            
            if (!optimizationHistory.ContainsKey(textureGuid))
            {
                optimizationHistory[textureGuid] = new OptimizationStats
                {
                    textureGuid = textureGuid,
                    contentType = TextureAnalyzer.TextureContentType.Artwork // Default, should be set properly
                };
            }
            
            var stats = optimizationHistory[textureGuid];
            stats.attempts.Add(new OptimizationAttempt
            {
                format = format,
                size = size,
                quality = quality,
                resultingQualityScore = qualityScore,
                compressionRatio = compressionRatio,
                userRating = userRating,
                timestamp = DateTime.Now
            });
            
            // Update averages
            UpdateOptimizationStats(stats);
            
            SaveOptimizationHistory();
        }

        /// <summary>
        /// Get optimization statistics for analytics
        /// </summary>
        public static Dictionary<string, object> GetOptimizationAnalytics()
        {
            var analytics = new Dictionary<string, object>();
            
            var totalAttempts = 0;
            var totalQualityScore = 0f;
            var totalCompressionRatio = 0f;
            var formatUsage = new Dictionary<TextureImporterFormat, int>();
            var contentTypeStats = new Dictionary<TextureAnalyzer.TextureContentType, int>();
            
            foreach (var stats in optimizationHistory.Values)
            {
                totalAttempts += stats.attempts.Count;
                totalQualityScore += stats.averageQualityScore * stats.attempts.Count;
                totalCompressionRatio += stats.averageCompressionRatio * stats.attempts.Count;
                
                if (!contentTypeStats.ContainsKey(stats.contentType))
                    contentTypeStats[stats.contentType] = 0;
                contentTypeStats[stats.contentType]++;
                
                foreach (var attempt in stats.attempts)
                {
                    if (!formatUsage.ContainsKey(attempt.format))
                        formatUsage[attempt.format] = 0;
                    formatUsage[attempt.format]++;
                }
            }
            
            analytics["totalOptimizations"] = totalAttempts;
            analytics["averageQualityScore"] = totalAttempts > 0 ? totalQualityScore / totalAttempts : 0f;
            analytics["averageCompressionRatio"] = totalAttempts > 0 ? totalCompressionRatio / totalAttempts : 0f;
            analytics["formatUsage"] = formatUsage;
            analytics["contentTypeDistribution"] = contentTypeStats;
            analytics["totalTexturesOptimized"] = optimizationHistory.Count;
            
            return analytics;
        }

        private static void InitializeDefaultProfiles()
        {
            profiles = new List<OptimizationProfile>();
            
            // Mobile Profile
            var mobileProfile = new OptimizationProfile
            {
                name = "Mobile Optimized",
                description = "Optimized for mobile devices with limited memory and bandwidth",
                targetPlatform = BuildTarget.Android,
                priority = TextureAnalyzer.OptimizationPriority.Size
            };
            InitializeProfileSettings(mobileProfile);
            SetMobileOptimizations(mobileProfile);
            profiles.Add(mobileProfile);
            
            // Console Profile
            var consoleProfile = new OptimizationProfile
            {
                name = "Console High Quality",
                description = "High quality settings for console platforms",
                targetPlatform = BuildTarget.PS4,
                priority = TextureAnalyzer.OptimizationPriority.Quality
            };
            InitializeProfileSettings(consoleProfile);
            SetConsoleOptimizations(consoleProfile);
            profiles.Add(consoleProfile);
            
            // PC Profile
            var pcProfile = new OptimizationProfile
            {
                name = "PC Balanced",
                description = "Balanced quality and performance for PC platforms",
                targetPlatform = BuildTarget.StandaloneWindows64,
                priority = TextureAnalyzer.OptimizationPriority.Balanced
            };
            InitializeProfileSettings(pcProfile);
            SetPCOptimizations(pcProfile);
            profiles.Add(pcProfile);
            
            // WebGL Profile
            var webProfile = new OptimizationProfile
            {
                name = "WebGL Compressed",
                description = "Highly compressed for web deployment",
                targetPlatform = BuildTarget.WebGL,
                priority = TextureAnalyzer.OptimizationPriority.Size
            };
            InitializeProfileSettings(webProfile);
            SetWebGLOptimizations(webProfile);
            profiles.Add(webProfile);
            
            // VR Profile
            var vrProfile = new OptimizationProfile
            {
                name = "VR Optimized",
                description = "Optimized for VR with performance priority",
                targetPlatform = BuildTarget.StandaloneWindows64,
                priority = TextureAnalyzer.OptimizationPriority.Performance
            };
            InitializeProfileSettings(vrProfile);
            SetVROptimizations(vrProfile);
            profiles.Add(vrProfile);
        }

        private static void InitializeProfileSettings(OptimizationProfile profile)
        {
            foreach (TextureAnalyzer.TextureContentType contentType in Enum.GetValues(typeof(TextureAnalyzer.TextureContentType)))
            {
                profile.formatSettings[contentType] = new OptimizationProfile.FormatSettings
                {
                    preferredFormat = TextureImporterFormat.DXT5,
                    fallbackFormat = TextureImporterFormat.RGBA32,
                    maxSize = 1024,
                    minSize = 32,
                    qualityLevel = 50,
                    useCrunchCompression = true,
                    generateMipmaps = true,
                    filterMode = FilterMode.Bilinear,
                    wrapMode = TextureWrapMode.Repeat
                };
            }
        }

        private static void SetMobileOptimizations(OptimizationProfile profile)
        {
            // Aggressive compression for mobile
            foreach (var setting in profile.formatSettings.Values)
            {
                setting.maxSize = 512;
                setting.qualityLevel = 25;
                setting.useCrunchCompression = true;
            }
            
            // Special settings for different content types
            profile.formatSettings[TextureAnalyzer.TextureContentType.UI].maxSize = 256;
            profile.formatSettings[TextureAnalyzer.TextureContentType.UI].qualityLevel = 40;
            profile.formatSettings[TextureAnalyzer.TextureContentType.Icon].maxSize = 128;
            
            profile.performanceSettings.optimizeForMemory = true;
            profile.performanceSettings.enableStreamingMipmaps = true;
        }

        private static void SetConsoleOptimizations(OptimizationProfile profile)
        {
            // High quality for consoles
            foreach (var setting in profile.formatSettings.Values)
            {
                setting.maxSize = 2048;
                setting.qualityLevel = 80;
                setting.useCrunchCompression = false;
            }
            
            profile.qualitySettings.qualityThreshold = 0.9f;
            profile.performanceSettings.optimizeForMemory = false;
        }

        private static void SetPCOptimizations(OptimizationProfile profile)
        {
            // Balanced settings for PC
            foreach (var setting in profile.formatSettings.Values)
            {
                setting.maxSize = 1024;
                setting.qualityLevel = 50;
                setting.useCrunchCompression = true;
            }
            
            profile.formatSettings[TextureAnalyzer.TextureContentType.Photo].maxSize = 2048;
            profile.formatSettings[TextureAnalyzer.TextureContentType.Photo].qualityLevel = 70;
        }

        private static void SetWebGLOptimizations(OptimizationProfile profile)
        {
            // Aggressive compression for web
            foreach (var setting in profile.formatSettings.Values)
            {
                setting.maxSize = 512;
                setting.qualityLevel = 30;
                setting.useCrunchCompression = true;
            }
            
            profile.performanceSettings.optimizeForLoadTime = true;
            profile.qualitySettings.compressionTolerance = 0.5f;
        }

        private static void SetVROptimizations(OptimizationProfile profile)
        {
            // Performance-focused for VR
            foreach (var setting in profile.formatSettings.Values)
            {
                setting.maxSize = 1024;
                setting.qualityLevel = 60;
                setting.generateMipmaps = true;
            }
            
            profile.performanceSettings.optimizeForMemory = true;
            profile.performanceSettings.enableStreamingMipmaps = true;
            profile.performanceSettings.streamingMipmapsPriority = 100;
        }

        private static void ApplyHistoricalLearning(OptimizationProfile.FormatSettings recommendation, OptimizationStats history, TextureAnalyzer.AnalysisResult analysis)
        {
            if (history.attempts.Count == 0) return;
            
            // Find the best performing settings from history
            var bestAttempt = history.attempts.OrderByDescending(a => a.resultingQualityScore - a.compressionRatio * 0.3f).FirstOrDefault();
            
            if (bestAttempt != null && bestAttempt.resultingQualityScore > 0.7f)
            {
                // Use successful historical settings as base
                recommendation.preferredFormat = bestAttempt.format;
                recommendation.qualityLevel = bestAttempt.quality;
                recommendation.maxSize = bestAttempt.size;
            }
            
            // Adjust based on user ratings
            var ratedAttempts = history.attempts.Where(a => a.userRating > 0).ToList();
            if (ratedAttempts.Count > 0)
            {
                var avgRating = ratedAttempts.Average(a => a.userRating);
                if (avgRating < 3.0f) // Poor ratings, try higher quality
                {
                    recommendation.qualityLevel = Mathf.Min(100, recommendation.qualityLevel + 20);
                }
            }
        }

        private static void ApplyPlatformOptimizations(OptimizationProfile.FormatSettings recommendation, BuildTarget platform, TextureAnalyzer.AnalysisResult analysis)
        {
            switch (platform)
            {
                case BuildTarget.Android:
                case BuildTarget.iOS:
                    // Mobile optimizations
                    recommendation.maxSize = Mathf.Min(recommendation.maxSize, 1024);
                    recommendation.useCrunchCompression = true;
                    if (analysis.contentType == TextureAnalyzer.TextureContentType.UI)
                        recommendation.maxSize = Mathf.Min(recommendation.maxSize, 512);
                    break;
                    
                case BuildTarget.WebGL:
                    // Web optimizations
                    recommendation.maxSize = Mathf.Min(recommendation.maxSize, 512);
                    recommendation.useCrunchCompression = true;
                    recommendation.qualityLevel = Mathf.Min(recommendation.qualityLevel, 40);
                    break;
                    
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneOSX:
                    // Desktop optimizations
                    if (analysis.complexityScore > 0.7f)
                        recommendation.maxSize = Mathf.Min(recommendation.maxSize, 2048);
                    break;
            }
        }

        private static void ApplyContentTypeOptimizations(OptimizationProfile.FormatSettings recommendation, TextureAnalyzer.TextureContentType contentType, TextureAnalyzer.AnalysisResult analysis)
        {
            switch (contentType)
            {
                case TextureAnalyzer.TextureContentType.UI:
                case TextureAnalyzer.TextureContentType.Icon:
                    recommendation.preferredFormat = analysis.hasTransparency ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                    recommendation.generateMipmaps = false;
                    recommendation.filterMode = FilterMode.Point;
                    break;
                    
                case TextureAnalyzer.TextureContentType.Normal:
                    recommendation.preferredFormat = TextureImporterFormat.DXT5;
                    recommendation.generateMipmaps = true;
                    break;
                    
                case TextureAnalyzer.TextureContentType.Mask:
                    recommendation.preferredFormat = analysis.isMonochrome ? TextureImporterFormat.Alpha8 : TextureImporterFormat.DXT1;
                    break;
                    
                case TextureAnalyzer.TextureContentType.Photo:
                    recommendation.preferredFormat = analysis.hasTransparency ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                    recommendation.qualityLevel = Mathf.Max(recommendation.qualityLevel, 60);
                    break;
            }
        }

        private static void UpdateOptimizationStats(OptimizationStats stats)
        {
            if (stats.attempts.Count == 0) return;
            
            stats.averageQualityScore = stats.attempts.Average(a => a.resultingQualityScore);
            stats.averageCompressionRatio = stats.attempts.Average(a => a.compressionRatio);
            stats.lastOptimized = stats.attempts.Max(a => a.timestamp);
        }

        private static void LoadProfiles()
        {
            try
            {
                if (File.Exists(profilesPath))
                {
                    var json = File.ReadAllText(profilesPath);
                    profiles = JsonUtility.FromJson<List<OptimizationProfile>>(json) ?? new List<OptimizationProfile>();
                }
                else
                {
                    profiles = new List<OptimizationProfile>();
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to load optimization profiles: {e.Message}");
                profiles = new List<OptimizationProfile>();
            }
            
            if (profiles.Count == 0)
            {
                InitializeDefaultProfiles();
            }
        }

        private static void SaveProfiles()
        {
            try
            {
                var directory = Path.GetDirectoryName(profilesPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                var json = JsonUtility.ToJson(profiles, true);
                File.WriteAllText(profilesPath, json);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save optimization profiles: {e.Message}");
            }
        }

        private static void LoadOptimizationHistory()
        {
            try
            {
                var historyPath = "Assets/TC/Editor/OptimizationHistory.json";
                if (File.Exists(historyPath))
                {
                    var json = File.ReadAllText(historyPath);
                    var historyList = JsonUtility.FromJson<List<OptimizationStats>>(json) ?? new List<OptimizationStats>();
                    
                    optimizationHistory.Clear();
                    foreach (var stats in historyList)
                    {
                        optimizationHistory[stats.textureGuid] = stats;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to load optimization history: {e.Message}");
            }
        }

        private static void SaveOptimizationHistory()
        {
            try
            {
                var historyPath = "Assets/TC/Editor/OptimizationHistory.json";
                var directory = Path.GetDirectoryName(historyPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                var historyList = optimizationHistory.Values.ToList();
                var json = JsonUtility.ToJson(historyList, true);
                File.WriteAllText(historyPath, json);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save optimization history: {e.Message}");
            }
        }
    }
}