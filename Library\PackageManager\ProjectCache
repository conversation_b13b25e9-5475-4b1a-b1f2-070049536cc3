m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: D:/My Project/Test Project/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638868594758245291
    m_Hash: 594644489
  m_LockFileStatus:
    m_FilePath: D:/My Project/Test Project/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638868594763480573
    m_Hash: 2189495314
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: D:/My Project/Test Project/Packages
m_EditorVersion: 6000.0.30f1 (62b05ba0686a)
m_ResolvedPackages:
- packageId: com.unity.collab-proxy@2.8.2
  testable: 0
  isDirectDependency: 1
  version: 2.8.2
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.collab-proxy
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: 
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    compatible:
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    recommended: 2.5.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  - uvcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638822079721750000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: c1fbc35e6bbae1ac0e9a3481e441dfdb3602b5ef
    path: 
  unityLifecycle:
    version: 2.5.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed false positive error showing in console if
    creating a workspace from the Hub with a version of the Unity Editor shipping
    with a default Version Control package older than version 2.7.1."}'
  assetStore:
    productId: 
  fingerprint: c854d1f7d97fbe1905f3e3591ded6fe77d96e654
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.2
    minimumUnityVersion: 2021.3.0f1
- packageId: com.unity.feature.development@1.0.2
  testable: 0
  isDirectDependency: 1
  version: 1.0.2
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.feature.development
  assetPath: Packages/com.unity.feature.development
  name: com.unity.feature.development
  displayName: Engineering
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: "Optimize your development experience in Unity with the Dev Tools
    feature set. Enable support for multiple integrated development environments
    (IDE) for editing your Unity code. Get access to development tools to help you
    test and analyze your project\u2019s performance."
  errors: []
  versions:
    all:
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies:
  - name: com.unity.ide.visualstudio
    version: default
  - name: com.unity.ide.rider
    version: default
  - name: com.unity.editorcoroutines
    version: default
  - name: com.unity.performance.profile-analyzer
    version: default
  - name: com.unity.test-framework
    version: default
  - name: com.unity.testtools.codecoverage
    version: default
  resolvedDependencies:
  - name: com.unity.ide.visualstudio
    version: 2.0.22
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ide.rider
    version: 3.0.31
  - name: com.unity.editorcoroutines
    version: 1.0.0
  - name: com.unity.performance.profile-analyzer
    version: 1.2.2
  - name: com.unity.testtools.codecoverage
    version: 1.2.6
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/DeveloperToolsFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 767aadbc6eb72681a4ca807c8fa248e0230a0cef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 
- packageId: com.unity.inputsystem@1.12.0
  testable: 0
  isDirectDependency: 1
  version: 1.12.0
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.inputsystem
  assetPath: Packages/com.unity.inputsystem
  name: com.unity.inputsystem
  displayName: Input System
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A new input system which can be used as a more extensible and customizable
    alternative to Unity's classic input system in UnityEngine.Input.
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.6-preview
    - 0.2.8-preview
    - 0.2.10-preview
    - 0.9.0-preview
    - 0.9.1-preview
    - 0.9.2-preview
    - 0.9.3-preview
    - 0.9.4-preview
    - 0.9.5-preview
    - 0.9.6-preview
    - 1.0.0-preview
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.6
    - 1.0.0-preview.7
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.1.0-pre.5
    - 1.1.0-pre.6
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.1
    - 1.2.0
    - 1.3.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.5.0
    - 1.5.1
    - 1.6.1
    - 1.6.3
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    compatible:
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    recommended: 1.11.2
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - input
  - events
  - keyboard
  - mouse
  - gamepad
  - touch
  - vr
  - xr
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638726197023440000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.12/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/InputSystem.git
    revision: 98646c95066dabdc4cb605460a32213c2c078e46
    path: 
  unityLifecycle:
    version: 1.11.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an issue causing the Action context
    menu to not show on right click when right clicking an action in the Input Action
    Editor [ISXB-1134](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1134).\n-
    Reverted changes from 0ddd534d8 (ISXB-746) which introduced a regression [ISXB-1127](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1127).\n-
    Fixed `ArgumentNullException: Value cannot be null.` during the migration of
    Project-wide Input Actions from `InputManager.asset` to `InputSystem_Actions.inputactions`
    asset which lead do the lost of the configuration [ISXB-1105](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1105).\n-
    Fixed pointerId staying the same when simultaneously releasing and then pressing
    in the same frame on mobile using touch. [ISXB-1006](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-845).\n-
    Fixed ISubmitHandler.OnSubmit event processing when operating in Manual Update
    mode (ISXB-1141).\n- Fixed Rename mode is not entered and name is autocompleted
    to default when creating a new Action Map on 2022.3. [ISXB-1151](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1151).\n-
    Fixed unexpected control scheme switch when using `OnScreenControl` and pointer
    based schemes which registed \"Cancel\" event on every frame.[ISXB-656](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-656).\n-
    Fixed an issue with The \"Add Control Scheme...\" popup window so that it now
    persists until any changes are explicitly Saved or Cancelled [case ISXB-1131](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1131).\n-
    Fixed missing documentation for source generated Input Action Assets. This is
    now generated as part of the source code generation step when \"Generate C# Class\"
    is checked in the importer inspector settings.\n- Fixed pasting into an empty
    map list raising an exception. [ISXB-1150](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1150)\n-
    Fixed pasting bindings into empty Input Action asset. [ISXB-1180](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1180)\n-
    Fixed missing ''&'' symbol in Control Scheme dropdown on Windows platform. [ISXB-1109](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1109)\n-
    Fixed icon scaling in Input Actions window.\n- Fixed an issue where removing
    the InputSystem package could lead to invalid input handling settings.\n- Fixed
    `ArgumentOutOfRangeException` when adding a new Control Scheme with any Device
    selected. [ISXB-1129](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1129)\n-
    Fixed a CS0105 compiler warning due to duplicate using statement in test source
    code (ISXB-1247).\n- Fixed tooltip support in the UI Toolkit version of the Input
    Actions Asset editor.\n- Fixed documentation to clarify bindings with modifiers
    `overrideModifiersNeedToBePressedFirst` configuration [ISXB-806](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-806).\n-
    Fixed an issue in `Samples/Visualizers/GamepadVisualizer.unity` sample where
    the visualization wouldn''t handle device disconnects or current device changes
    properly (ISXB-1243).\n- Fixed an issue when displaying Serialized InputAction''s
    Processor properties inside the Inspector window. [ISXB-1269](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1269)\n-
    Fixed an issue with default device selection when adding new Control Scheme.\n-
    Fixed an issue where action map delegates were not updated when the asset already
    assigned to the PlayerInput component were changed [ISXB-711](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-711).\n-
    Fixed Action properties edition in the UI Toolkit version of the Input Actions
    Asset editor. [ISXB-1277](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1277)\n-
    Fixed an editor crash caused by input debugger device state window reusing cached
    state when reconnecting Stadia controller. [ISXB-658](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-658)\n-
    Fixed an issue where batch jobs would fail with \"Error:"}'
  assetStore:
    productId: 
  fingerprint: 920b46832575a5beecb201e0a4570b4984b7ec34
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.2
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.multiplayer.center@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.multiplayer.center
  assetPath: Packages/com.unity.multiplayer.center
  name: com.unity.multiplayer.center
  displayName: Multiplayer Center
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The multiplayer center provides a starting point to create multiplayer
    games. It will recommend specific packages and enable you to easily access integrations,
    samples and documentation.
  errors: []
  versions:
    all:
    - 0.2.1
    - 0.3.0
    - 0.4.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - Multiplayer
  - Netcode
  - Services
  - Tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f502d8ac613fa076192423e73892fbd89eb4049b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.timeline@1.8.7
  testable: 0
  isDirectDependency: 1
  version: 1.8.7
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.timeline
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    compatible:
    - 1.8.7
    - 1.8.8
    recommended: 1.8.8
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638524833900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: d6432ca638481c3d0e4c01f87f9cdbe3c4b1d529
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Released ronl-workflow-custom-marker.md
    Added a new workflow to the Timeline Workflows documentation:\n- Released ronl-workflow-custom-marker.md
    The `Create a custom Notes marker` workflow demonstrates how to create a custom
    marker for adding notes to Timeline instances. This workflow also demonstrates
    how to change the default appearance of a custom marker with scripting and a
    Unity Style Sheet (USS).\n\n### Fixed\n\n- Fixed bug where using , and . (<>)
    to step frames in the Animation Window while the Timeline Window was linked would
    sometimes not work. [IN-56667](https://unity3d.atlassian.net/servicedesk/customer/portal/2/IN-56667)\n-
    When the Timeline and Animation windows are linked and the Timeline Window is
    active, moving the playhead in the Timeline Window will cause the animation window
    to repaint immediately."}'
  assetStore:
    productId: 
  fingerprint: c58b4ee65782ad38338e29f7ee67787cb6998f04
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.ugui
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    recommended: 2.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: fd5da3b4bec8d043b01c30177bd62059c5fa36d0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.visualscripting@1.9.5
  testable: 0
  isDirectDependency: 1
  version: 1.9.5
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.visualscripting
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638659692270900000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 3621ef0207747a605f195118ba99d1a8bdb19433
    path: 
  unityLifecycle:
    version: 1.9.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed \"NullReferenceException\" error
    when returning to the State Graph from the Script Graph. [UVSB-1905](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-1905)\n-
    Fixed compilation error when a graph contains a reference to a method with an
    \"in\" parameter. [UVSB-2544](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2487)\n-
    Added missing truncate function to Formula node [UVSB-2526](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2525)\n-
    Fixed an error when creating a Script Graph asset in an empty project\n\n###
    Changed\n- Updated deprecated EditorAnalytics APIs to new ones"}'
  assetStore:
    productId: 
  fingerprint: 1b53f46e931bea668e53f1feb0ac9138170c9455
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.9.5
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.ide.visualstudio@2.0.22
  testable: 0
  isDirectDependency: 0
  version: 2.0.22
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.ide.visualstudio
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.22
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638326061910000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 700b44077345e97d37d464ff25507638983aed64
    path: 
  unityLifecycle:
    version: 2.0.22
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Add support for `XDG_DATA_DIRS` and
    `.desktop` files on Linux for `VS Code` discovery.\n- Use compile-time platform-specifics
    instead of using runtime conditions.\n\nProject generation:\n\n- Suppress `USG0001`
    warnings.\n- Mark referenced assemblies as private (to not copy extra files to
    output directory when building).\n- Add Unity capability to SDK-Style projects.\n-
    Prevent circular dependency errors with SDK-Style projects."}'
  assetStore:
    productId: 
  fingerprint: 8140e851d83e922ca2021b04a89519de94ebe38c
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.22
    minimumUnityVersion: 2019.4.25f1
- packageId: com.unity.ide.rider@3.0.31
  testable: 0
  isDirectDependency: 0
  version: 3.0.31
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.ide.rider
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638548308080000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: ccd778a2275ff09f1a83bc924cfb2fb4d5b63566
    path: 
  unityLifecycle:
    version: 3.0.31
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"fix RIDER-104519 Rider is reporting errors in scripts
    that work fine in Unity when utilizing DOTS - when Player project, by generating
    projects for all assemblies in \"com.unity.entities\", \"com.unity.collections\"\nfix
    RIDER-111622 Unity Rider package is not compatible with Rider Dev builds"}'
  assetStore:
    productId: 
  fingerprint: 7921be93db40ec070fcb01ed82d1c3df1bbdddcd
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.31
    minimumUnityVersion: 2019.2.6f1
- packageId: com.unity.editorcoroutines@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.editorcoroutines
  assetPath: Packages/com.unity.editorcoroutines
  name: com.unity.editorcoroutines
  displayName: Editor Coroutines
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'The editor coroutines package allows developers to start constructs
    similar to Unity''s monobehaviour based coroutines within the editor using abitrary
    objects. '
  errors: []
  versions:
    all:
    - 0.0.1-preview.3
    - 0.0.1-preview.4
    - 0.0.1-preview.5
    - 0.0.2-preview.1
    - 0.1.0-preview.1
    - 0.1.0-preview.2
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - coroutine
  - coroutines
  - editor
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637232611380000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.editorcoroutines.git
    revision: f67fc9992bbc7a553b17375de53a8b2db136528e
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2018.1.0a1
- packageId: com.unity.performance.profile-analyzer@1.2.2
  testable: 0
  isDirectDependency: 0
  version: 1.2.2
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.performance.profile-analyzer
  assetPath: Packages/com.unity.performance.profile-analyzer
  name: com.unity.performance.profile-analyzer
  displayName: Profile Analyzer
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: "The Profile Analyzer tool supports the standard Unity Profiler. You
    can use it to analyze multiple frames and multiple data sets of the CPU data
    in the Profiler.\n\nMain features: \n\u25AA Multi-frame analysis of a single
    set of Profiler CPU data \n\u25AA Comparison of two multi-frame profile scans
    \n\n"
  errors: []
  versions:
    all:
    - 0.4.0-preview.3
    - 0.4.0-preview.5
    - 0.4.0-preview.6
    - 0.5.0-preview.1
    - 0.6.0-preview.1
    - 0.7.0-preview.4
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.1.0-pre.2
    - 1.1.0
    - 1.1.1
    - 1.2.2
    - 1.2.3
    compatible:
    - 1.2.2
    - 1.2.3
    recommended: 1.2.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638108624620000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git
    revision: 2c63f931588caa60a1fef33b1b250b20d952dc58
    path: 
  unityLifecycle:
    version: 1.2.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n* Fixed exception thrown when removing
    marker."}'
  assetStore:
    productId: 
  fingerprint: fe7acfd2717a98ded6ddc33be6b2ebe4459a2add
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.2
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.test-framework@1.4.5
  testable: 0
  isDirectDependency: 0
  version: 1.4.5
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.test-framework
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: assets
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.4.6
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575054880000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework@1.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.test-framework.git
    revision: e50747c90fb18ab9f4f350b60d0fcdcae19bdebc
    path: 
  unityLifecycle:
    version: 1.4.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed an issue where batchmode test runs would never
    finish if a test yielded WaitForEndOfFrame (DSTR-1009).\n- Fixed an issue where
    the location prompt was required when using the Install all tests in ''build''
    folder option during test builds.\n- Canceling a PlayMode test run now correctly
    restores the scene setup, instead of leaving the editor in the test scene.\n-
    Fixed an issue where UnitySetUp did not fail when nested coroutines threw an
    exception (DSTR-1007).\n- When selecting multiple tests and running them, the
    test runner now correctly updates the details of the first selected test (UTF-602).\n-
    The interaction mode and application idle time settings is now being changes
    when running tests, resulting in faster test runs if not already in use (applies
    to 2020.3 and later) (DSTR-690).\n- Fixed an issue where some NUnit attributes
    caused errors and stopped async test runs (DSTR-1040).\n- Added support for the
    MaxTime attribute on async and UnityTest methods (DSTR-1040).\n- Fixed a memory
    leak issue where a large number of domain reloads within the same test could
    crash the editor (DSTR-1023).\n- Changed to use a progress bar inside the test
    runner window when running tests. This ensures that the progress bar is not fighting
    to display when e.g. compiling scripts and also makes it easier to cancel a run.
    This progress bar is displayed for all types of runs (EditMode, PlayMode and
    Player). (UTF-596).\n- Fixed an issue where ignored tests with an attributes
    did not display the ignore reason in the test runner UI.\n- Having multiple tests
    with the same unique id no longer gives a error with ''An item with the same
    key has already been added'', but instead logs an warning about the duplicate
    id.\n- The result icons for test suites should no longer flicker when running
    tests.\n- Ensured that test results ignored in the editor but run on a player
    are not overwritten with ignore status when shown in the UI (DSTR-1042).\n- Fixed
    an issue where the RunStarted event was not invoked correctly during a test run
    (DSTR-1046).\n- Fixed an issue where TestStarted and TestFinished events were
    repeated for ignored test fixtures after a domain reload (DSTR-986)."}'
  assetStore:
    productId: 
  fingerprint: 0a21eb82d95cd331643a1e0ce4e8e9a5f18954c8
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.4.5
    minimumUnityVersion: 2019.4.0a10
- packageId: com.unity.testtools.codecoverage@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.testtools.codecoverage
  assetPath: Packages/com.unity.testtools.codecoverage
  name: com.unity.testtools.codecoverage
  displayName: Code Coverage
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use this package to export code coverage data and reports from your
    automated tests. Additionally, the Code Coverage package offers a Coverage Recording
    feature which allows capturing coverage data on demand, for manual testing or
    when there are no automated tests in the project.
  errors: []
  versions:
    all:
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.4.2-preview
    - 0.4.3-preview
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.2.0-exp.1
    - 1.2.0-exp.2
    - 1.2.0-exp.3
    - 1.2.0-exp.4
    - 1.2.0-exp.5
    - 1.2.0-exp.6
    - 1.2.0-exp.7
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    compatible:
    - 1.2.6
    recommended: 1.2.6
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.0.16
  - name: com.unity.settings-manager
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords:
  - test
  - coverage
  - testing
  - opencover
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575857900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.testtools.codecoverage.git
    revision: 959db8ff97eedf9b211ad5cf320b87ac01f1e90f
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes\n- Documentation: Fixed formatting in [Using
    Code Coverage in batchmode](https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/CoverageBatchmode.html)
    page (case [COV-40](https://issuetracker.unity3d.com/issues/docs-formatting-in-using-code-coverage-in-batchmode-docs-page-is-incorrect)).\n-
    Removed the references to the deprecated FindObjectOfType method in the *Asteroids
    sample project* (case [COV-42](https://issuetracker.unity3d.com/issues/sample-project-is-using-obsolete-findobjectoftype-method-which-causes-multiple-warnings-in-console-when-it-is-imported)).\n-
    Added missing logs for the ReportGenerator (case [COV-46](https://issuetracker.unity3d.com/issues/code-coverage-package-does-not-report-some-of-the-internal-reportgenerator-errors))."}'
  assetStore:
    productId: 
  fingerprint: 205a02cbcb39584f20b51c49b853047aceb3a3a7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.6
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.settings-manager@2.0.1
  testable: 0
  isDirectDependency: 0
  version: 2.0.1
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.settings-manager
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.0.1
    - 2.1.0
    recommended: 2.0.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637744099390000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 9b7c12d04ef880168151c127fd6b37b5931197dc
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 56a930affa1e4462d3595b5c19a1c2a4bf2015d2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.1
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 1
  resolvedPath: D:\My Project\Test Project\Library\PackageCache\com.unity.ext.nunit
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: assets
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638236642220000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
    revision: 469fe0d46bfc26fbb32a53885ca4d12abcdae4ae
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixing bug for InstanceID as struct changes"}'
  assetStore:
    productId: 
  fingerprint: 60ef35ffd3cd5e2f5c8887a4a4ca6148854cd092
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
m_BuiltInPackagesHash: f059cafdb442bef7ad05beca6deab6b7fe27fbb8
