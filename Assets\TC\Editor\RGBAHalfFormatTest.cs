using UnityEngine;
using UnityEditor;
using TextureCompressorTool;

/// <summary>
/// Test specifically for RGBA Half format functionality
/// </summary>
public static class RGBAHalfFormatTest
{
    [MenuItem("Tools/Texture Compressor/🧪 Test RGBA Half Format")]
    public static void TestRGBAHalfFormat()
    {
        Debug.Log("🧪 Testing RGBA Half Format...");
        
        // Check if RGBA Half is in the available formats
        var window = EditorWindow.GetWindow<TextureCompressorWindow>();
        
        // Find RGBA Half in the format list
        bool foundRGBAHalf = false;
        int rgbaHalfIndex = -1;
        
        // Access the format arrays through reflection since they're private
        var formatField = typeof(TextureCompressorWindow).GetField("allTextureFormats", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var correspondingField = typeof(TextureCompressorWindow).GetField("correspondingFormats", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
        if (formatField != null && correspondingField != null)
        {
            string[] allFormats = (string[])formatField.GetValue(window);
            TextureImporterFormat[] correspondingFormats = (TextureImporterFormat[])correspondingField.GetValue(window);
            
            for (int i = 0; i < allFormats.Length; i++)
            {
                if (allFormats[i] == "RGBA Half")
                {
                    foundRGBAHalf = true;
                    rgbaHalfIndex = i;
                    
                    Debug.Log($"✅ Found 'RGBA Half' at index {i}");
                    
                    if (i < correspondingFormats.Length)
                    {
                        var correspondingFormat = correspondingFormats[i];
                        Debug.Log($"✅ Mapped to: {correspondingFormat}");
                        
                        if (correspondingFormat == TextureImporterFormat.RGBAHalf)
                        {
                            Debug.Log("✅ Correctly mapped to TextureImporterFormat.RGBAHalf");
                        }
                        else
                        {
                            Debug.LogError($"❌ Incorrectly mapped to {correspondingFormat}, should be RGBAHalf");
                        }
                    }
                    else
                    {
                        Debug.LogError("❌ Index out of range in correspondingFormats array");
                    }
                    break;
                }
            }
        }
        
        if (!foundRGBAHalf)
        {
            Debug.LogError("❌ 'RGBA Half' format not found in allTextureFormats array");
        }
        
        // Test format properties
        TestRGBAHalfProperties();
        
        // Show result dialog
        string message = foundRGBAHalf ? 
            "✅ RGBA Half format is available and properly configured!\n\n" +
            "You can find it in the format dropdown as 'RGBA Half'.\n" +
            "It maps to TextureImporterFormat.RGBAHalf." :
            "❌ RGBA Half format is not properly configured.\n\n" +
            "Please check the console for details.";
            
        EditorUtility.DisplayDialog("RGBA Half Format Test", message, "OK");
    }
    
    private static void TestRGBAHalfProperties()
    {
        Debug.Log("📋 RGBA Half Format Properties:");
        Debug.Log("• Format: TextureImporterFormat.RGBAHalf");
        Debug.Log("• Channels: Red, Green, Blue, Alpha");
        Debug.Log("• Bit Depth: 16-bit per channel (64-bit total)");
        Debug.Log("• Use Case: HDR textures, high precision color data");
        Debug.Log("• Platform Support: Modern platforms (PC, Console, Mobile with HDR support)");
        Debug.Log("• Memory Usage: 8 bytes per pixel (64 bits)");
        Debug.Log("• Quality: High precision floating point");
    }
    
    [MenuItem("Tools/Texture Compressor/📋 Show RGBA Half Info")]
    public static void ShowRGBAHalfInfo()
    {
        string info = @"🎨 RGBA Half Format Information

FORMAT DETAILS:
• Name: RGBA Half
• Unity Format: TextureImporterFormat.RGBAHalf
• Bit Depth: 16-bit per channel (64-bit total)
• Channels: Red, Green, Blue, Alpha
• Type: High Dynamic Range (HDR)

CHARACTERISTICS:
• Memory: 8 bytes per pixel
• Quality: High precision floating point
• Compression: Uncompressed
• Alpha Support: ✅ Yes
• HDR Support: ✅ Yes

BEST USED FOR:
• HDR textures and lighting
• High precision color data
• Render targets requiring floating point precision
• Post-processing effects
• Color grading lookup tables

PLATFORM SUPPORT:
• PC/Desktop: ✅ Full support
• Console: ✅ Full support  
• Mobile: ⚠️ Requires HDR-capable devices
• WebGL: ⚠️ Limited support

HOW TO USE:
1. Open Texture Compressor Tool
2. Go to Batch Operations tab
3. Enable 'Override Format'
4. Select 'RGBA Half' from dropdown
5. Click compress button on textures";

        Debug.Log(info);
        EditorGUIUtility.systemCopyBuffer = info;
        
        EditorUtility.DisplayDialog("RGBA Half Format Info", 
            "Complete RGBA Half format information has been logged to console and copied to clipboard!", 
            "OK");
    }
}
