Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker0.log
-srvPort
52612
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19480]  Target information:

Player connection [19480]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1597443158 [EditorId] 1597443158 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19480]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1597443158 [EditorId] 1597443158 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19480] Host joined multi-casting on [***********:54997]...
Player connection [19480] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56736
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006625 seconds.
- Loaded All Assemblies, in  0.699 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 388 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.992 seconds
Domain Reload Profiling: 1687ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (282ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (276ms)
			TypeCache.Refresh (274ms)
				TypeCache.ScanAssembly (250ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (992ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (923ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (538ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (188ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.217 seconds
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 2142ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (769ms)
		LoadAssemblies (585ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (362ms)
			TypeCache.Refresh (286ms)
				TypeCache.ScanAssembly (259ms)
			BuildScriptInfoCaches (63ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (736ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (526ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (1.0 MB). Loaded Objects now: 4192.
Memory consumption went from 83.0 MB to 82.0 MB.
Total: 10.342200 ms (FindLiveObjects: 0.812200 ms CreateObjectMapping: 0.519600 ms MarkObjects: 7.049300 ms  DeleteObjects: 1.957500 ms)

========================================================================
Received Import Request.
  Time since last request: 9257.300206 seconds.
  path: Assets/TC/Editor/TextureCompressorBuildProcessor.cs
  artifactKey: Guid(edd2b6a7ab205f94e9ee5ea53029a00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/Editor/TextureCompressorBuildProcessor.cs using Guid(edd2b6a7ab205f94e9ee5ea53029a00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8676703f657cffd1226be2ecdce4b69f') in 0.0099423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3698 unused Assets / (0.9 MB). Loaded Objects now: 4195.
Memory consumption went from 81.1 MB to 80.2 MB.
Total: 9.997900 ms (FindLiveObjects: 1.110300 ms CreateObjectMapping: 0.607700 ms MarkObjects: 6.756000 ms  DeleteObjects: 1.521800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3699 unused Assets / (0.9 MB). Loaded Objects now: 4196.
Memory consumption went from 81.1 MB to 80.2 MB.
Total: 10.162700 ms (FindLiveObjects: 0.682400 ms CreateObjectMapping: 0.441700 ms MarkObjects: 7.399200 ms  DeleteObjects: 1.637500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 320.121136 seconds.
  path: Assets/TC
  artifactKey: Guid(552eef61074811241961dceb1fcc2b6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC using Guid(552eef61074811241961dceb1fcc2b6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd330d662c1f7ed76ee700091b5a0ed08') in 0.0016837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3695 unused Assets / (0.8 MB). Loaded Objects now: 4192.
Memory consumption went from 81.1 MB to 80.3 MB.
Total: 10.168900 ms (FindLiveObjects: 0.623600 ms CreateObjectMapping: 0.404200 ms MarkObjects: 7.567100 ms  DeleteObjects: 1.571600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.066 seconds
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.907 seconds
Domain Reload Profiling: 1971ms
	BeginReloadAssembly (343ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (578ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (907ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.6 MB to 81.7 MB.
Total: 8.270500 ms (FindLiveObjects: 0.567400 ms CreateObjectMapping: 0.361500 ms MarkObjects: 5.945800 ms  DeleteObjects: 1.394600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 32.257025 seconds.
  path: Assets/Editor
  artifactKey: Guid(08e6dd4f2ee492f45a61f620b8a16124) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Editor using Guid(08e6dd4f2ee492f45a61f620b8a16124) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '930ac6b778596f22bae2ff58e9dbee3e') in 0.0026033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3697 unused Assets / (0.8 MB). Loaded Objects now: 4195.
Memory consumption went from 82.7 MB to 82.0 MB.
Total: 9.564200 ms (FindLiveObjects: 0.659700 ms CreateObjectMapping: 0.405700 ms MarkObjects: 7.012600 ms  DeleteObjects: 1.485100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3697 unused Assets / (0.8 MB). Loaded Objects now: 4195.
Memory consumption went from 82.7 MB to 81.9 MB.
Total: 10.646600 ms (FindLiveObjects: 0.656600 ms CreateObjectMapping: 0.406300 ms MarkObjects: 8.082000 ms  DeleteObjects: 1.499700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.033 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.942 seconds
Domain Reload Profiling: 1973ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (571ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (942ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (726ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (483ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.9 MB). Loaded Objects now: 4196.
Memory consumption went from 82.6 MB to 81.7 MB.
Total: 8.466400 ms (FindLiveObjects: 0.569500 ms CreateObjectMapping: 0.356800 ms MarkObjects: 6.229900 ms  DeleteObjects: 1.309100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1267.234753 seconds.
  path: Assets/TC/Editor/TextureCompressorBuildProcessor.cs
  artifactKey: Guid(edd2b6a7ab205f94e9ee5ea53029a00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/Editor/TextureCompressorBuildProcessor.cs using Guid(edd2b6a7ab205f94e9ee5ea53029a00c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd114482c57b6600034af406500ec56b8') in 0.0031361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.578617 seconds.
  path: Assets/TC/Editor/TextureCompressorSettings.cs
  artifactKey: Guid(a123221cf4d7ece43aacc5294d40fc70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/Editor/TextureCompressorSettings.cs using Guid(a123221cf4d7ece43aacc5294d40fc70) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '359ec1d40d1bb9c709e3afdbea6bed37') in 0.0006605 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.748903 seconds.
  path: Assets/TC/TexturecompressorTool.cs
  artifactKey: Guid(a078b841401eda34a8ae0c16d71a23f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/TexturecompressorTool.cs using Guid(a078b841401eda34a8ae0c16d71a23f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b4e012f949043ce5c118888b75fcb241') in 0.0006621 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.047 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.915 seconds
Domain Reload Profiling: 1961ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (559ms)
		LoadAssemblies (483ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (916ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (719ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (482ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.8 MB). Loaded Objects now: 4196.
Memory consumption went from 82.6 MB to 81.7 MB.
Total: 12.637400 ms (FindLiveObjects: 0.553200 ms CreateObjectMapping: 0.362700 ms MarkObjects: 10.413200 ms  DeleteObjects: 1.306500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 71.521787 seconds.
  path: Assets/TC/README.md
  artifactKey: Guid(e2fc6154ed6d87f4694c3c7b933bbd52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/README.md using Guid(e2fc6154ed6d87f4694c3c7b933bbd52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f34f9f2d71cc88b33f3d1733d1b3f5b') in 0.0321589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3699 unused Assets / (0.9 MB). Loaded Objects now: 4198.
Memory consumption went from 82.8 MB to 81.9 MB.
Total: 13.567300 ms (FindLiveObjects: 1.127000 ms CreateObjectMapping: 0.583800 ms MarkObjects: 10.073500 ms  DeleteObjects: 1.780400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.076 seconds
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.967 seconds
Domain Reload Profiling: 2038ms
	BeginReloadAssembly (317ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (619ms)
		LoadAssemblies (522ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (968ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (740ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (486ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (0.9 MB). Loaded Objects now: 4198.
Memory consumption went from 82.6 MB to 81.8 MB.
Total: 8.134100 ms (FindLiveObjects: 0.584600 ms CreateObjectMapping: 0.401100 ms MarkObjects: 5.720300 ms  DeleteObjects: 1.427100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.117 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.989 seconds
Domain Reload Profiling: 2104ms
	BeginReloadAssembly (357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (527ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (297ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (271ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (991ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (746ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (516ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (1.1 MB). Loaded Objects now: 4198.
Memory consumption went from 82.6 MB to 81.5 MB.
Total: 17.187800 ms (FindLiveObjects: 1.512300 ms CreateObjectMapping: 0.644600 ms MarkObjects: 12.790200 ms  DeleteObjects: 2.237800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.017 seconds
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.913 seconds
Domain Reload Profiling: 1928ms
	BeginReloadAssembly (310ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (569ms)
		LoadAssemblies (479ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (914ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (695ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (0.8 MB). Loaded Objects now: 4198.
Memory consumption went from 82.6 MB to 81.8 MB.
Total: 8.483600 ms (FindLiveObjects: 0.575600 ms CreateObjectMapping: 0.360900 ms MarkObjects: 6.301400 ms  DeleteObjects: 1.244000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1650.995289 seconds.
  path: Assets/water 0342.jpg
  artifactKey: Guid(9325da55f99b49e419f5f8c3edfa5d38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/water 0342.jpg using Guid(9325da55f99b49e419f5f8c3edfa5d38) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd0cd8cd5ad6d782a37ff1dc126fe54e') in 0.1508993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 77.952648 seconds.
  path: Assets/water 0342.jpg
  artifactKey: Guid(9325da55f99b49e419f5f8c3edfa5d38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/water 0342.jpg using Guid(9325da55f99b49e419f5f8c3edfa5d38) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f5d4f71dd84c7d72a200425cc8cbd0f') in 0.0515954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 159.255024 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(289c1b55c9541489481df5cc06664110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(289c1b55c9541489481df5cc06664110) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7e9e64a58fecfdcde77304e1331e7f3') in 0.0087232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 4.939410 seconds.
  path: Assets/grass_seamless_texture_1408.jpg
  artifactKey: Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/grass_seamless_texture_1408.jpg using Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7607ad27964af76f621d2e3fb628726') in 0.0223925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 34.668976 seconds.
  path: Assets/grass_seamless_texture_1408.jpg
  artifactKey: Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/grass_seamless_texture_1408.jpg using Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee713e1b05402cabdde51b0af71be003') in 0.0974772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3701 unused Assets / (0.8 MB). Loaded Objects now: 4202.
Memory consumption went from 82.9 MB to 82.1 MB.
Total: 12.003200 ms (FindLiveObjects: 0.699500 ms CreateObjectMapping: 0.437300 ms MarkObjects: 9.014700 ms  DeleteObjects: 1.849400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.8 MB). Loaded Objects now: 4203.
Memory consumption went from 82.9 MB to 82.1 MB.
Total: 15.286800 ms (FindLiveObjects: 1.344800 ms CreateObjectMapping: 0.695200 ms MarkObjects: 11.558000 ms  DeleteObjects: 1.686800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3703 unused Assets / (1.0 MB). Loaded Objects now: 4204.
Memory consumption went from 82.9 MB to 82.0 MB.
Total: 17.026500 ms (FindLiveObjects: 0.857700 ms CreateObjectMapping: 0.462600 ms MarkObjects: 13.156900 ms  DeleteObjects: 2.546300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.077 seconds
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.923 seconds
Domain Reload Profiling: 1999ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (601ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (278ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (924ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (704ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (483ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3710 unused Assets / (0.6 MB). Loaded Objects now: 4204.
Memory consumption went from 82.7 MB to 82.1 MB.
Total: 7.798700 ms (FindLiveObjects: 0.574700 ms CreateObjectMapping: 0.363100 ms MarkObjects: 5.613200 ms  DeleteObjects: 1.246700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.7 MB). Loaded Objects now: 4206.
Memory consumption went from 82.8 MB to 82.2 MB.
Total: 10.469900 ms (FindLiveObjects: 0.679800 ms CreateObjectMapping: 0.387900 ms MarkObjects: 7.949700 ms  DeleteObjects: 1.450200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.8 MB). Loaded Objects now: 4206.
Memory consumption went from 82.8 MB to 82.0 MB.
Total: 8.672100 ms (FindLiveObjects: 0.724100 ms CreateObjectMapping: 0.405000 ms MarkObjects: 6.072200 ms  DeleteObjects: 1.469400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.072 seconds
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.920 seconds
Domain Reload Profiling: 1990ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (576ms)
		LoadAssemblies (508ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (921ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (687ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.8 MB). Loaded Objects now: 4206.
Memory consumption went from 82.7 MB to 81.9 MB.
Total: 9.194300 ms (FindLiveObjects: 0.951700 ms CreateObjectMapping: 0.702500 ms MarkObjects: 6.248100 ms  DeleteObjects: 1.288900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1672.586383 seconds.
  path: Assets/grass_seamless_texture_1408.jpg
  artifactKey: Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/grass_seamless_texture_1408.jpg using Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80f38b90e8aef35e6a38d274b39d47e6') in 0.1087882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 4206.
Memory consumption went from 80.2 MB to 80.2 MB.
Total: 8.811500 ms (FindLiveObjects: 0.415700 ms CreateObjectMapping: 0.168400 ms MarkObjects: 8.206500 ms  DeleteObjects: 0.018400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 4206.
Memory consumption went from 80.2 MB to 80.2 MB.
Total: 7.919500 ms (FindLiveObjects: 0.364000 ms CreateObjectMapping: 0.176400 ms MarkObjects: 7.363300 ms  DeleteObjects: 0.014500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 110.628949 seconds.
  path: Assets/grass_seamless_texture_1408.jpg
  artifactKey: Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/grass_seamless_texture_1408.jpg using Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56c63643924c49dab92ce09a654bb421') in 0.089828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.060 seconds
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.899 seconds
Domain Reload Profiling: 1959ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (606ms)
		LoadAssemblies (473ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (130ms)
				TypeCache.ScanAssembly (113ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (900ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (711ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.5 MB). Loaded Objects now: 4206.
Memory consumption went from 82.7 MB to 82.1 MB.
Total: 7.915100 ms (FindLiveObjects: 0.591600 ms CreateObjectMapping: 0.368000 ms MarkObjects: 5.722300 ms  DeleteObjects: 1.232000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 65.346572 seconds.
  path: Assets/grass_seamless_texture_1408.jpg
  artifactKey: Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/grass_seamless_texture_1408.jpg using Guid(0f741a8113ac5164caf53a85c3c1d3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd82ba375b2ba967770767f3a7cd5a184') in 0.1220642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.8 MB). Loaded Objects now: 4206.
Memory consumption went from 82.9 MB to 82.1 MB.
Total: 10.905200 ms (FindLiveObjects: 0.626700 ms CreateObjectMapping: 0.413000 ms MarkObjects: 8.479300 ms  DeleteObjects: 1.384600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.9 MB). Loaded Objects now: 4206.
Memory consumption went from 82.9 MB to 82.0 MB.
Total: 11.467600 ms (FindLiveObjects: 0.676400 ms CreateObjectMapping: 0.425500 ms MarkObjects: 8.822500 ms  DeleteObjects: 1.541300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.080 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.904 seconds
Domain Reload Profiling: 1982ms
	BeginReloadAssembly (357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (576ms)
		LoadAssemblies (515ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (235ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (905ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (692ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.5 MB). Loaded Objects now: 4206.
Memory consumption went from 82.7 MB to 82.1 MB.
Total: 7.842900 ms (FindLiveObjects: 0.599300 ms CreateObjectMapping: 0.380200 ms MarkObjects: 5.657600 ms  DeleteObjects: 1.204800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1495.903629 seconds.
  path: Assets/TC/Editor/TextureCompressorWindow.cs
  artifactKey: Guid(34ed49800b0a8864b929c4e21f7bb94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/Editor/TextureCompressorWindow.cs using Guid(34ed49800b0a8864b929c4e21f7bb94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '814aebf99b12bcf36c7c94947bd2de11') in 0.004152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

