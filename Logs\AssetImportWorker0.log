Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker0.log
-srvPort
53475
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31584]  Target information:

Player connection [31584]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1411717438 [EditorId] 1411717438 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31584]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1411717438 [EditorId] 1411717438 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31584] Host joined multi-casting on [***********:54997]...
Player connection [31584] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56000
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006540 seconds.
- Loaded All Assemblies, in  0.678 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 372 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 1631ms
	BeginReloadAssembly (227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (266ms)
				TypeCache.ScanAssembly (243ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (901ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (523ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (189ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.145 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 2077ms
	BeginReloadAssembly (290ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (701ms)
		LoadAssemblies (501ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (372ms)
			TypeCache.Refresh (292ms)
				TypeCache.ScanAssembly (264ms)
			BuildScriptInfoCaches (66ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (526ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3725 unused Assets / (0.9 MB). Loaded Objects now: 4213.
Memory consumption went from 83.5 MB to 82.5 MB.
Total: 8.698600 ms (FindLiveObjects: 0.673400 ms CreateObjectMapping: 0.375100 ms MarkObjects: 6.021100 ms  DeleteObjects: 1.624800 ms)

========================================================================
Received Import Request.
  Time since last request: 84377.505351 seconds.
  path: Assets/TC/Editor/TextureCompressorWindow.cs
  artifactKey: Guid(34ed49800b0a8864b929c4e21f7bb94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC/Editor/TextureCompressorWindow.cs using Guid(34ed49800b0a8864b929c4e21f7bb94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff26f0ccf1bd6550d1483bd66c2645ce') in 0.01687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

