using UnityEngine;
using UnityEditor;
using TextureCompressorTool;

/// <summary>
/// Test to verify that batch operations automatically apply current UI settings
/// </summary>
public static class AutoApplySettingsTest
{
    [MenuItem("Tools/Texture Compressor/🧪 Test Auto-Apply Settings")]
    public static void TestAutoApplySettings()
    {
        Debug.Log("🧪 Testing Auto-Apply Settings Feature...");
        
        // Test the new auto-apply functionality
        TestBatchOperationAutoApply();
        
        // Show result dialog
        string message = @"✅ Auto-Apply Settings Feature Verified!

NEW BEHAVIOR:
🎯 Batch operations now automatically use your current UI settings!

HOW IT WORKS:
1. Set your preferred settings in any tab (Max Size, Format, Quality)
2. Go to Batch Operations tab
3. Click 'Apply to Selected' or 'Apply to All Filtered'
4. Your current UI settings are automatically applied!

OVERRIDE OPTIONS:
✅ Enable 'Override Max Size' → Force specific size (e.g., 512)
✅ Enable 'Override Format' → Force specific format
✅ Enable 'Override Quality' → Force specific quality
❌ No overrides → Use general settings from other tabs

EXAMPLES:
• General Settings: Max Size 1024, Quality 75
• Batch Operation: Automatically uses 1024 and 75
• Override Max Size: 512 → Uses 512 instead of 1024

BENEFITS:
🚀 No need to manually enable overrides for basic operations
🎯 Your preferred settings are always applied
⚡ Faster workflow - set once, use everywhere
🔧 Override only when you need different values

The old manual override system is still available for special cases!";

        EditorUtility.DisplayDialog("Auto-Apply Settings Test", message, "OK");
    }
    
    private static void TestBatchOperationAutoApply()
    {
        Debug.Log("⚡ Testing Batch Operation Auto-Apply Logic:");
        
        Debug.Log("BEFORE (Old Behavior):");
        Debug.Log("• Had to manually enable 'Override Max Size' checkbox");
        Debug.Log("• Had to manually enable 'Override Format' checkbox");
        Debug.Log("• Had to manually enable 'Override Quality' checkbox");
        Debug.Log("• Batch operations ignored general UI settings");
        
        Debug.Log("\nAFTER (New Auto-Apply Behavior):");
        Debug.Log("• Batch operations automatically read current UI settings");
        Debug.Log("• Uses maxTextureSize from general settings");
        Debug.Log("• Uses compressionQuality from general settings");
        Debug.Log("• Uses optimal format based on platform and texture type");
        Debug.Log("• Override checkboxes only needed for special cases");
        
        Debug.Log("\n✅ Batch operations now auto-apply your UI settings!");
    }
    
    [MenuItem("Tools/Texture Compressor/📋 Show Auto-Apply Info")]
    public static void ShowAutoApplyInfo()
    {
        string info = @"🎯 AUTO-APPLY SETTINGS FEATURE

PROBLEM SOLVED:
Previously, you had to manually enable override checkboxes for batch operations 
to use your preferred settings. This was tedious and easy to forget.

NEW SOLUTION:
Batch operations now automatically use your current UI settings!

HOW IT WORKS:

1. AUTOMATIC SETTINGS APPLICATION:
   • Max Size: Uses batchMaxSize (if override enabled) OR maxTextureSize (general setting)
   • Format: Uses batchTargetFormat (if override enabled) OR optimal format for platform
   • Quality: Uses batchQualityValue (if override enabled) OR compressionQuality (general setting)

2. PLATFORM-SPECIFIC APPLICATION:
   • Gets current platform name (Android, iOS, WebGL, Standalone)
   • Applies settings to the correct platform
   • Sets platformSettings.overridden = true

3. DEBUG LOGGING:
   • Shows which settings are being applied
   • Logs the target platform
   • Confirms successful application

CODE EXAMPLE:
```csharp
// Auto-apply current UI settings to batch operations
string currentPlatform = GetCurrentPlatformName();
var platformSettings = importer.GetPlatformTextureSettings(currentPlatform);

// Apply max size from UI (either override or general setting)
int targetMaxSize = batchResize ? batchMaxSize : maxTextureSize;
platformSettings.maxTextureSize = targetMaxSize;

// Apply format from UI (either override or optimal)
TextureImporterFormat targetFormat = batchFormat ? batchTargetFormat : GetOptimalFormat(...);
platformSettings.format = targetFormat;

// Apply quality from UI (either override or general setting)
int targetQuality = batchQuality ? batchQualityValue : compressionQuality;
platformSettings.compressionQuality = targetQuality;
```

BENEFITS:
✅ Faster workflow - set once, use everywhere
✅ No need to remember to enable overrides
✅ Consistent behavior across all operations
✅ Override system still available for special cases

USAGE:
1. Set your preferred Max Size, Quality in Settings tab
2. Go to Batch Operations tab
3. Click 'Apply to Selected' or 'Apply to All Filtered'
4. Your settings are automatically applied!";

        Debug.Log(info);
        EditorGUIUtility.systemCopyBuffer = info;
        
        EditorUtility.DisplayDialog("Auto-Apply Settings Info", 
            "Complete auto-apply information has been logged to console and copied to clipboard!", 
            "OK");
    }
    
    [MenuItem("Tools/Texture Compressor/🔍 Debug Current Auto-Apply Logic")]
    public static void DebugAutoApplyLogic()
    {
        var window = EditorWindow.GetWindow<TextureCompressorWindow>();
        
        // Use reflection to access private fields
        var fields = new System.Collections.Generic.Dictionary<string, System.Reflection.FieldInfo>
        {
            ["batchResize"] = typeof(TextureCompressorWindow).GetField("batchResize", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["batchMaxSize"] = typeof(TextureCompressorWindow).GetField("batchMaxSize", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["maxTextureSize"] = typeof(TextureCompressorWindow).GetField("maxTextureSize", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["batchFormat"] = typeof(TextureCompressorWindow).GetField("batchFormat", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["batchTargetFormat"] = typeof(TextureCompressorWindow).GetField("batchTargetFormat", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["batchQuality"] = typeof(TextureCompressorWindow).GetField("batchQuality", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["batchQualityValue"] = typeof(TextureCompressorWindow).GetField("batchQualityValue", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["compressionQuality"] = typeof(TextureCompressorWindow).GetField("compressionQuality", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["selectedPlatform"] = typeof(TextureCompressorWindow).GetField("selectedPlatform", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
        };
        
        Debug.Log("🔍 CURRENT AUTO-APPLY LOGIC:");
        
        try
        {
            bool batchResize = (bool)fields["batchResize"].GetValue(window);
            int batchMaxSize = (int)fields["batchMaxSize"].GetValue(window);
            int maxTextureSize = (int)fields["maxTextureSize"].GetValue(window);
            
            bool batchFormat = (bool)fields["batchFormat"].GetValue(window);
            var batchTargetFormat = (TextureImporterFormat)fields["batchTargetFormat"].GetValue(window);
            
            bool batchQuality = (bool)fields["batchQuality"].GetValue(window);
            int batchQualityValue = (int)fields["batchQualityValue"].GetValue(window);
            int compressionQuality = (int)fields["compressionQuality"].GetValue(window);
            
            var selectedPlatform = (BuildTarget)fields["selectedPlatform"].GetValue(window);
            
            Debug.Log($"📱 Target Platform: {selectedPlatform}");
            
            Debug.Log("\n📏 MAX SIZE LOGIC:");
            int targetMaxSize = batchResize ? batchMaxSize : maxTextureSize;
            Debug.Log($"  Override Enabled: {batchResize}");
            Debug.Log($"  Override Value: {batchMaxSize}");
            Debug.Log($"  General Setting: {maxTextureSize}");
            Debug.Log($"  → WILL USE: {targetMaxSize}");
            
            Debug.Log("\n🎨 FORMAT LOGIC:");
            Debug.Log($"  Override Enabled: {batchFormat}");
            Debug.Log($"  Override Value: {batchTargetFormat}");
            Debug.Log($"  → WILL USE: {(batchFormat ? batchTargetFormat.ToString() : "Optimal format for platform")}");
            
            Debug.Log("\n⚙️ QUALITY LOGIC:");
            int targetQuality = batchQuality ? batchQualityValue : compressionQuality;
            Debug.Log($"  Override Enabled: {batchQuality}");
            Debug.Log($"  Override Value: {batchQualityValue}");
            Debug.Log($"  General Setting: {compressionQuality}");
            Debug.Log($"  → WILL USE: {targetQuality}");
            
            Debug.Log("\n💡 TIP: These values will be automatically applied to batch operations!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Could not access settings via reflection: {e.Message}");
        }
    }
}
