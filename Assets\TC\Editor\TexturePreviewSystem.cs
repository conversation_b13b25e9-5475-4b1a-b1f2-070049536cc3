using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;

namespace TextureCompressorTool
{
    /// <summary>
    /// Real-time texture preview and comparison system
    /// </summary>
    public class TexturePreviewSystem
    {
        private static Dictionary<Texture2D, PreviewData> previewCache = new Dictionary<Texture2D, PreviewData>();
        private static RenderTexture previewRenderTexture;
        private static Material previewMaterial;
        
        public class PreviewData
        {
            public Texture2D originalTexture;
            public Texture2D previewTexture;
            public TextureImporterFormat previewFormat;
            public int previewSize;
            public long originalMemorySize;
            public long previewMemorySize;
            public float compressionRatio;
            public bool isValid;
            public DateTime createdTime;
        }

        public class ComparisonView
        {
            public enum ViewMode
            {
                SideBySide,
                SplitView,
                Overlay,
                Difference
            }
            
            public ViewMode mode = ViewMode.SideBySide;
            public float splitPosition = 0.5f;
            public bool showGrid = true;
            public bool showPixelPerfect = false;
            public float zoomLevel = 1.0f;
            public Vector2 panOffset = Vector2.zero;
        }

        static TexturePreviewSystem()
        {
            InitializePreviewSystem();
        }

        private static void InitializePreviewSystem()
        {
            // Create preview render texture
            if (previewRenderTexture == null)
            {
                previewRenderTexture = new RenderTexture(512, 512, 0, RenderTextureFormat.ARGB32);
                previewRenderTexture.Create();
            }

            // Create preview material
            if (previewMaterial == null)
            {
                var shader = Shader.Find("Hidden/Internal-GUITexture");
                if (shader != null)
                {
                    previewMaterial = new Material(shader);
                }
            }
        }

        /// <summary>
        /// Generate a real-time preview of how the texture will look after compression
        /// </summary>
        public static PreviewData GeneratePreview(Texture2D originalTexture, TextureImporterFormat targetFormat, int targetSize, int quality = 50)
        {
            if (originalTexture == null) return null;

            var cacheKey = originalTexture;
            
            // Check cache first
            if (previewCache.ContainsKey(cacheKey))
            {
                var cached = previewCache[cacheKey];
                if (cached.previewFormat == targetFormat && cached.previewSize == targetSize && 
                    (DateTime.Now - cached.createdTime).TotalMinutes < 5) // Cache for 5 minutes
                {
                    return cached;
                }
            }

            try
            {
                var previewData = new PreviewData
                {
                    originalTexture = originalTexture,
                    previewFormat = targetFormat,
                    previewSize = targetSize,
                    createdTime = DateTime.Now
                };

                // Create temporary texture with target settings
                var tempTexture = CreateCompressedPreview(originalTexture, targetFormat, targetSize, quality);
                
                if (tempTexture != null)
                {
                    previewData.previewTexture = tempTexture;
                    previewData.originalMemorySize = CalculateTextureMemorySize(originalTexture);
                    previewData.previewMemorySize = CalculateTextureMemorySize(tempTexture, targetFormat);
                    previewData.compressionRatio = previewData.originalMemorySize > 0 ? 
                        (float)previewData.previewMemorySize / previewData.originalMemorySize : 1f;
                    previewData.isValid = true;
                }

                // Cache the result
                previewCache[cacheKey] = previewData;
                
                // Clean old cache entries
                CleanPreviewCache();

                return previewData;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to generate preview for {originalTexture.name}: {e.Message}");
                return null;
            }
        }

        private static Texture2D CreateCompressedPreview(Texture2D original, TextureImporterFormat format, int size, int quality)
        {
            try
            {
                // Create a copy of the original texture
                var originalPath = AssetDatabase.GetAssetPath(original);
                var tempPath = "Assets/Temp_Preview_" + Guid.NewGuid().ToString("N")[..8] + ".png";
                
                // Copy the original file
                AssetDatabase.CopyAsset(originalPath, tempPath);
                
                // Get the importer for the temp texture
                var tempImporter = AssetImporter.GetAtPath(tempPath) as TextureImporter;
                if (tempImporter == null) return null;

                // Apply preview settings
                tempImporter.maxTextureSize = size;
                tempImporter.textureCompression = TextureImporterCompression.Compressed;
                
                var platformSettings = tempImporter.GetDefaultPlatformTextureSettings();
                platformSettings.format = format;
                platformSettings.maxTextureSize = size;
                platformSettings.compressionQuality = quality;
                
                tempImporter.SetPlatformTextureSettings(platformSettings);
                tempImporter.SaveAndReimport();

                // Load the compressed texture
                var compressedTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(tempPath);
                
                // Create a readable copy
                Texture2D readableTexture = null;
                if (compressedTexture != null)
                {
                    readableTexture = new Texture2D(compressedTexture.width, compressedTexture.height, TextureFormat.RGBA32, false);
                    
                    // Copy pixels using Graphics.CopyTexture or RenderTexture
                    var rt = RenderTexture.GetTemporary(compressedTexture.width, compressedTexture.height);
                    Graphics.Blit(compressedTexture, rt);
                    
                    var previous = RenderTexture.active;
                    RenderTexture.active = rt;
                    readableTexture.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
                    readableTexture.Apply();
                    RenderTexture.active = previous;
                    
                    RenderTexture.ReleaseTemporary(rt);
                }

                // Clean up temp file
                AssetDatabase.DeleteAsset(tempPath);
                
                return readableTexture;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to create compressed preview: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Draw a comparison view between original and preview textures
        /// </summary>
        public static void DrawComparisonView(PreviewData previewData, ComparisonView viewSettings, Rect viewRect)
        {
            if (previewData?.originalTexture == null || previewData?.previewTexture == null) return;

            switch (viewSettings.mode)
            {
                case ComparisonView.ViewMode.SideBySide:
                    DrawSideBySideView(previewData, viewSettings, viewRect);
                    break;
                case ComparisonView.ViewMode.SplitView:
                    DrawSplitView(previewData, viewSettings, viewRect);
                    break;
                case ComparisonView.ViewMode.Overlay:
                    DrawOverlayView(previewData, viewSettings, viewRect);
                    break;
                case ComparisonView.ViewMode.Difference:
                    DrawDifferenceView(previewData, viewSettings, viewRect);
                    break;
            }

            // Draw grid if enabled
            if (viewSettings.showGrid)
            {
                DrawGrid(viewRect);
            }

            // Draw comparison info
            DrawComparisonInfo(previewData, viewRect);
        }

        private static void DrawSideBySideView(PreviewData previewData, ComparisonView viewSettings, Rect viewRect)
        {
            var leftRect = new Rect(viewRect.x, viewRect.y, viewRect.width * 0.5f, viewRect.height);
            var rightRect = new Rect(viewRect.x + viewRect.width * 0.5f, viewRect.y, viewRect.width * 0.5f, viewRect.height);

            // Draw original on left
            GUI.DrawTexture(leftRect, previewData.originalTexture, ScaleMode.ScaleToFit);
            GUI.Label(new Rect(leftRect.x + 5, leftRect.y + 5, 100, 20), "Original", EditorStyles.whiteLabel);

            // Draw preview on right
            GUI.DrawTexture(rightRect, previewData.previewTexture, ScaleMode.ScaleToFit);
            GUI.Label(new Rect(rightRect.x + 5, rightRect.y + 5, 100, 20), "Preview", EditorStyles.whiteLabel);

            // Draw separator line
            var separatorRect = new Rect(viewRect.x + viewRect.width * 0.5f - 1, viewRect.y, 2, viewRect.height);
            EditorGUI.DrawRect(separatorRect, Color.white);
        }

        private static void DrawSplitView(PreviewData previewData, ComparisonView viewSettings, Rect viewRect)
        {
            var splitX = viewRect.x + viewRect.width * viewSettings.splitPosition;
            
            // Draw original texture
            var originalRect = new Rect(viewRect.x, viewRect.y, splitX - viewRect.x, viewRect.height);
            if (originalRect.width > 0)
            {
                GUI.DrawTexture(originalRect, previewData.originalTexture, ScaleMode.ScaleToFit);
            }

            // Draw preview texture
            var previewRect = new Rect(splitX, viewRect.y, viewRect.xMax - splitX, viewRect.height);
            if (previewRect.width > 0)
            {
                GUI.DrawTexture(previewRect, previewData.previewTexture, ScaleMode.ScaleToFit);
            }

            // Draw split line
            var lineRect = new Rect(splitX - 1, viewRect.y, 2, viewRect.height);
            EditorGUI.DrawRect(lineRect, Color.yellow);

            // Handle split dragging
            var handleRect = new Rect(splitX - 5, viewRect.y, 10, viewRect.height);
            EditorGUIUtility.AddCursorRect(handleRect, MouseCursor.ResizeHorizontal);
            
            if (Event.current.type == EventType.MouseDown && handleRect.Contains(Event.current.mousePosition))
            {
                GUIUtility.hotControl = GUIUtility.GetControlID(FocusType.Passive);
            }
            
            if (GUIUtility.hotControl != 0 && Event.current.type == EventType.MouseDrag)
            {
                viewSettings.splitPosition = Mathf.Clamp01((Event.current.mousePosition.x - viewRect.x) / viewRect.width);
                Event.current.Use();
            }
        }

        private static void DrawOverlayView(PreviewData previewData, ComparisonView viewSettings, Rect viewRect)
        {
            // Draw original as base
            GUI.DrawTexture(viewRect, previewData.originalTexture, ScaleMode.ScaleToFit);
            
            // Draw preview with transparency
            var oldColor = GUI.color;
            GUI.color = new Color(1, 1, 1, 0.5f);
            GUI.DrawTexture(viewRect, previewData.previewTexture, ScaleMode.ScaleToFit);
            GUI.color = oldColor;
        }

        private static void DrawDifferenceView(PreviewData previewData, ComparisonView viewSettings, Rect viewRect)
        {
            // This would require a custom shader to show pixel differences
            // For now, show side by side with difference highlighting
            DrawSideBySideView(previewData, viewSettings, viewRect);
            
            // Add difference indicators
            GUI.Label(new Rect(viewRect.x + 5, viewRect.yMax - 25, 200, 20), 
                "Difference visualization", EditorStyles.helpBox);
        }

        private static void DrawGrid(Rect viewRect)
        {
            var gridColor = new Color(1, 1, 1, 0.1f);
            var gridSize = 32f;
            
            // Vertical lines
            for (float x = viewRect.x; x < viewRect.xMax; x += gridSize)
            {
                var lineRect = new Rect(x, viewRect.y, 1, viewRect.height);
                EditorGUI.DrawRect(lineRect, gridColor);
            }
            
            // Horizontal lines
            for (float y = viewRect.y; y < viewRect.yMax; y += gridSize)
            {
                var lineRect = new Rect(viewRect.x, y, viewRect.width, 1);
                EditorGUI.DrawRect(lineRect, gridColor);
            }
        }

        private static void DrawComparisonInfo(PreviewData previewData, Rect viewRect)
        {
            var infoRect = new Rect(viewRect.x, viewRect.yMax - 60, viewRect.width, 60);
            GUI.Box(infoRect, "", EditorStyles.helpBox);
            
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleLeft };
            
            var line1 = new Rect(infoRect.x + 5, infoRect.y + 5, infoRect.width - 10, 15);
            var line2 = new Rect(infoRect.x + 5, infoRect.y + 20, infoRect.width - 10, 15);
            var line3 = new Rect(infoRect.x + 5, infoRect.y + 35, infoRect.width - 10, 15);
            
            GUI.Label(line1, $"Original: {previewData.originalTexture.width}x{previewData.originalTexture.height} | " +
                            $"Preview: {previewData.previewTexture.width}x{previewData.previewTexture.height}", labelStyle);
            
            GUI.Label(line2, $"Memory: {FormatBytes(previewData.originalMemorySize)} → {FormatBytes(previewData.previewMemorySize)}", labelStyle);
            
            var savings = (1f - previewData.compressionRatio) * 100f;
            var savingsColor = savings > 50f ? "green" : savings > 25f ? "yellow" : "red";
            GUI.Label(line3, $"<color={savingsColor}>Compression: {previewData.compressionRatio:P1} | Savings: {savings:F1}%</color>", 
                new GUIStyle(labelStyle) { richText = true });
        }

        private static long CalculateTextureMemorySize(Texture2D texture, TextureImporterFormat? format = null)
        {
            if (texture == null) return 0;
            
            var actualFormat = format ?? GetTextureFormat(texture);
            return CalculateTextureSizeByFormat(texture.width, texture.height, actualFormat);
        }

        private static TextureImporterFormat GetTextureFormat(Texture2D texture)
        {
            var path = AssetDatabase.GetAssetPath(texture);
            var importer = AssetImporter.GetAtPath(path) as TextureImporter;
            return importer?.GetDefaultPlatformTextureSettings().format ?? TextureImporterFormat.RGBA32;
        }

        private static long CalculateTextureSizeByFormat(int width, int height, TextureImporterFormat format)
        {
            long baseSize = 0;
            
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                    baseSize = (width * height) / 2;
                    break;
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                    baseSize = width * height;
                    break;
                case TextureImporterFormat.RGBA32:
                case TextureImporterFormat.ARGB32:
                    baseSize = width * height * 4;
                    break;
                case TextureImporterFormat.RGB24:
                    baseSize = width * height * 3;
                    break;
                case TextureImporterFormat.Alpha8:
                    baseSize = width * height;
                    break;
                default:
                    baseSize = width * height * 4;
                    break;
            }
            
            return baseSize;
        }

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024f:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024f * 1024f):F1} MB";
            return $"{bytes / (1024f * 1024f * 1024f):F1} GB";
        }

        private static void CleanPreviewCache()
        {
            var keysToRemove = new List<Texture2D>();
            var cutoffTime = DateTime.Now.AddMinutes(-10); // Remove entries older than 10 minutes
            
            foreach (var kvp in previewCache)
            {
                if (kvp.Value.createdTime < cutoffTime || kvp.Key == null)
                {
                    keysToRemove.Add(kvp.Key);
                    
                    // Clean up preview texture
                    if (kvp.Value.previewTexture != null)
                    {
                        UnityEngine.Object.DestroyImmediate(kvp.Value.previewTexture);
                    }
                }
            }
            
            foreach (var key in keysToRemove)
            {
                previewCache.Remove(key);
            }
        }

        /// <summary>
        /// Clear all cached previews
        /// </summary>
        public static void ClearCache()
        {
            foreach (var kvp in previewCache)
            {
                if (kvp.Value.previewTexture != null)
                {
                    UnityEngine.Object.DestroyImmediate(kvp.Value.previewTexture);
                }
            }
            previewCache.Clear();
        }

        /// <summary>
        /// Get memory usage of the preview cache
        /// </summary>
        public static long GetCacheMemoryUsage()
        {
            long totalMemory = 0;
            foreach (var kvp in previewCache)
            {
                if (kvp.Value.previewTexture != null)
                {
                    totalMemory += kvp.Value.previewMemorySize;
                }
            }
            return totalMemory;
        }
    }
}