{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21056, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21056, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21056, "tid": 675, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21056, "tid": 675, "ts": 1751339831317240, "dur": 1893, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831330686, "dur": 1623, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21056, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21056, "tid": 1, "ts": 1751339829804185, "dur": 9259, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751339829813449, "dur": 92076, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751339829905538, "dur": 51331, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831332315, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 21056, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829800294, "dur": 31980, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829832279, "dur": 1470143, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829835451, "dur": 8714, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829844180, "dur": 5427, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829849616, "dur": 919, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850545, "dur": 57, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850605, "dur": 143, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850755, "dur": 7, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850765, "dur": 102, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850874, "dur": 4, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829850881, "dur": 129, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851018, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851022, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851126, "dur": 4, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851132, "dur": 89, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851228, "dur": 3, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851234, "dur": 101, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851342, "dur": 4, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851348, "dur": 88, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851443, "dur": 4, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851450, "dur": 89, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851547, "dur": 3, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851553, "dur": 96, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851656, "dur": 4, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851663, "dur": 146, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851818, "dur": 4, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851825, "dur": 94, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851927, "dur": 4, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829851934, "dur": 103, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852044, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852050, "dur": 93, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852156, "dur": 3, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852162, "dur": 97, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852267, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852273, "dur": 94, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852374, "dur": 4, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852381, "dur": 95, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852482, "dur": 3, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852488, "dur": 110, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852605, "dur": 4, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852611, "dur": 96, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852714, "dur": 4, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852721, "dur": 95, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852823, "dur": 4, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852830, "dur": 90, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852927, "dur": 3, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829852933, "dur": 95, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853035, "dur": 4, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853042, "dur": 89, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853138, "dur": 3, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853143, "dur": 120, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853270, "dur": 5, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853277, "dur": 102, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853386, "dur": 4, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853392, "dur": 94, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853494, "dur": 4, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853500, "dur": 98, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853605, "dur": 4, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853611, "dur": 85, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853703, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853708, "dur": 99, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853818, "dur": 4, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853824, "dur": 104, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853935, "dur": 4, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829853941, "dur": 96, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854045, "dur": 4, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854052, "dur": 84, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854143, "dur": 3, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854149, "dur": 102, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854258, "dur": 4, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854264, "dur": 97, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854369, "dur": 4, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854377, "dur": 98, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854482, "dur": 4, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854488, "dur": 94, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854589, "dur": 3, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854595, "dur": 103, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854705, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854712, "dur": 106, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854824, "dur": 4, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854831, "dur": 95, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854933, "dur": 4, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829854943, "dur": 102, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855052, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855059, "dur": 96, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855163, "dur": 4, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855170, "dur": 98, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855276, "dur": 4, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855282, "dur": 101, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855390, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855398, "dur": 90, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855494, "dur": 3, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855500, "dur": 95, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855603, "dur": 4, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855609, "dur": 101, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855717, "dur": 4, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855724, "dur": 105, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855836, "dur": 4, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855843, "dur": 75, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855926, "dur": 3, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829855933, "dur": 102, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856042, "dur": 4, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856049, "dur": 102, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856158, "dur": 4, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856165, "dur": 97, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856270, "dur": 4, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856277, "dur": 79, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856363, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856369, "dur": 372, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856748, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856753, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856872, "dur": 5, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856883, "dur": 103, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829856994, "dur": 5, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857005, "dur": 95, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857108, "dur": 4, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857115, "dur": 89, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857211, "dur": 3, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857217, "dur": 96, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857321, "dur": 4, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857328, "dur": 98, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857433, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857439, "dur": 100, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857547, "dur": 4, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857554, "dur": 84, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857645, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857651, "dur": 89, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857749, "dur": 3, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857755, "dur": 99, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857860, "dur": 4, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857867, "dur": 97, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857971, "dur": 4, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829857978, "dur": 82, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858067, "dur": 3, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858073, "dur": 145, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858225, "dur": 4, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858232, "dur": 131, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858371, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858379, "dur": 94, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858480, "dur": 3, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858486, "dur": 87, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858597, "dur": 3, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858603, "dur": 83, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858694, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858700, "dur": 79, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858786, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858791, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858886, "dur": 3, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858893, "dur": 94, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829858994, "dur": 3, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859001, "dur": 94, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859102, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859108, "dur": 86, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859201, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859207, "dur": 109, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859323, "dur": 3, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859330, "dur": 87, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859424, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859430, "dur": 78, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859514, "dur": 17, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859534, "dur": 86, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859628, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859634, "dur": 92, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859732, "dur": 3, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859742, "dur": 86, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859835, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859841, "dur": 85, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859933, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829859939, "dur": 85, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860030, "dur": 3, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860036, "dur": 83, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860126, "dur": 4, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860132, "dur": 85, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860224, "dur": 3, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860230, "dur": 73, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860310, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860315, "dur": 93, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860414, "dur": 3, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860420, "dur": 86, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860513, "dur": 33, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860549, "dur": 102, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860659, "dur": 4, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860665, "dur": 88, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860761, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860767, "dur": 83, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860856, "dur": 4, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860863, "dur": 59, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860926, "dur": 2, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860930, "dur": 61, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829860998, "dur": 3, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861004, "dur": 75, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861083, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861087, "dur": 69, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861163, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861169, "dur": 68, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861242, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861246, "dur": 58, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861311, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861318, "dur": 64, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861387, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861390, "dur": 60, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861457, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861467, "dur": 86, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861557, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861562, "dur": 66, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861635, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861641, "dur": 70, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861715, "dur": 3, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861720, "dur": 63, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861790, "dur": 3, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861795, "dur": 70, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861869, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861873, "dur": 65, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861947, "dur": 3, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829861952, "dur": 71, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862028, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862032, "dur": 61, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862100, "dur": 3, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862106, "dur": 63, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862173, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862177, "dur": 71, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862255, "dur": 3, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862261, "dur": 554, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862828, "dur": 4, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829862836, "dur": 182, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863021, "dur": 11, "ph": "X", "name": "ProcessMessages 3452", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863035, "dur": 57, "ph": "X", "name": "ReadAsync 3452", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863096, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863100, "dur": 52, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863157, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863161, "dur": 62, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863228, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863232, "dur": 54, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863291, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863295, "dur": 53, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863354, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863358, "dur": 49, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863411, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863414, "dur": 55, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863474, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863478, "dur": 64, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863546, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863549, "dur": 51, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863603, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863605, "dur": 40, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863650, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863653, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863712, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863715, "dur": 51, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863770, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863774, "dur": 49, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863828, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863830, "dur": 55, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863890, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863893, "dur": 55, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863952, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829863957, "dur": 49, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864010, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864014, "dur": 49, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864068, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864071, "dur": 52, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864127, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864131, "dur": 51, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864186, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864189, "dur": 49, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864242, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864246, "dur": 55, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864305, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864309, "dur": 55, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864368, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864371, "dur": 56, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864431, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864435, "dur": 56, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864495, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864499, "dur": 51, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864555, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864559, "dur": 64, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864633, "dur": 3, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864638, "dur": 86, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864730, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864735, "dur": 62, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864802, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864806, "dur": 63, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864874, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864877, "dur": 64, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864945, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829864950, "dur": 58, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865013, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865017, "dur": 63, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865084, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865088, "dur": 55, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865148, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865153, "dur": 63, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865221, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865226, "dur": 55, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865285, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865288, "dur": 54, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865347, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865350, "dur": 55, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865409, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865414, "dur": 59, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865477, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865480, "dur": 51, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865535, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865539, "dur": 54, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865597, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865601, "dur": 50, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865654, "dur": 2, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865658, "dur": 56, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865719, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865724, "dur": 52, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865781, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865784, "dur": 52, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865840, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865844, "dur": 53, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865902, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865906, "dur": 64, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865974, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829865978, "dur": 51, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866033, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866037, "dur": 52, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866093, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866096, "dur": 61, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866162, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866166, "dur": 52, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866222, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866225, "dur": 55, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866287, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866291, "dur": 55, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866351, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866354, "dur": 48, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866406, "dur": 2, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866409, "dur": 83, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866498, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866502, "dur": 60, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866566, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866570, "dur": 52, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866627, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866630, "dur": 54, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866688, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866691, "dur": 63, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866758, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866762, "dur": 57, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866823, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866826, "dur": 54, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866885, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866888, "dur": 49, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866942, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829866945, "dur": 58, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867008, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867011, "dur": 54, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867069, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867073, "dur": 54, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867131, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867134, "dur": 46, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867185, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867189, "dur": 51, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867244, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867246, "dur": 48, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867298, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867302, "dur": 57, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867365, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867370, "dur": 56, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867430, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867434, "dur": 51, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867489, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867493, "dur": 55, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867553, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867557, "dur": 57, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867621, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867625, "dur": 60, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867689, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867693, "dur": 54, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867751, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867754, "dur": 50, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867808, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867812, "dur": 70, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867885, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867889, "dur": 55, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867948, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829867952, "dur": 52, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868008, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868011, "dur": 58, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868073, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868077, "dur": 78, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868163, "dur": 4, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868170, "dur": 79, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868254, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868258, "dur": 65, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868330, "dur": 3, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868338, "dur": 74, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868417, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868421, "dur": 63, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868491, "dur": 3, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868498, "dur": 82, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868584, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868588, "dur": 63, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868656, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868660, "dur": 56, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868720, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868724, "dur": 53, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868781, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868785, "dur": 50, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868839, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868843, "dur": 49, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868897, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868901, "dur": 50, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868955, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829868958, "dur": 51, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829869014, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829869017, "dur": 3203, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829872230, "dur": 5, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829872239, "dur": 598, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829872844, "dur": 1227, "ph": "X", "name": "ProcessMessages 5460", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829874081, "dur": 110, "ph": "X", "name": "ReadAsync 5460", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829874198, "dur": 6, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829874293, "dur": 76, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829874379, "dur": 674, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829875063, "dur": 13102, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888206, "dur": 196, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888407, "dur": 13, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888585, "dur": 334, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888923, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829888927, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889159, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889163, "dur": 253, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889420, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889422, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889707, "dur": 188, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829889959, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890095, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890102, "dur": 248, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890479, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890485, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890537, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890540, "dur": 271, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829890815, "dur": 80, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891006, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891145, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891223, "dur": 116, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891634, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891637, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891808, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891937, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829891997, "dur": 171, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829892192, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829892196, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829892407, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829892412, "dur": 1149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893618, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893623, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893676, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893679, "dur": 225, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893957, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829893962, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894149, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894154, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894401, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894404, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894557, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829894729, "dur": 4583, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829899320, "dur": 6, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829899329, "dur": 995, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829900331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829900335, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829900405, "dur": 957, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339829901367, "dur": 593680, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830495061, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830495068, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830495127, "dur": 4085, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830499220, "dur": 13240, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830512466, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830512470, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830512524, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830512530, "dur": 2831, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830515370, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830515375, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830515451, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339830515479, "dur": 773296, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831288788, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831288795, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831288886, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831288894, "dur": 1249, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831290153, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831290158, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831290279, "dur": 48, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831290332, "dur": 2304, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831292645, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831292650, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831292746, "dur": 1029, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339831293783, "dur": 8568, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831332338, "dur": 1419, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339829794814, "dur": 162184, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339829957001, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339829957011, "dur": 2144, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831333760, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21056, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339829754550, "dur": 1549482, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339829769328, "dur": 14041, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339831304302, "dur": 6032, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339831308089, "dur": 142, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339831310463, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831333773, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751339829827063, "dur": 2416, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339829829497, "dur": 672, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339829830250, "dur": 85, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751339829830336, "dur": 1290, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339829834202, "dur": 8873, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829845510, "dur": 6284, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829851939, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829852376, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829852481, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829852583, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829852798, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829852904, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853011, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853181, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853297, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853391, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853500, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853728, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853839, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829853961, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854173, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854283, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854376, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854636, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854749, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829854858, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855059, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855196, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855304, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855505, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855622, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855736, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829855956, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856070, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856185, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856303, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856409, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856517, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856629, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856748, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_BF8650D11F8E519A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829856956, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829857074, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829857288, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829857403, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829857515, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829858091, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829858243, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829858460, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829858570, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829858678, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859004, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859105, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859224, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859426, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859566, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859632, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859720, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829859838, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860061, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_1315E0A407C70415.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860132, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860246, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860348, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860450, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860549, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860653, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860826, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860894, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829860978, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861089, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861188, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861288, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861390, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861578, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861647, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861761, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861844, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829861913, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862035, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862108, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862276, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862369, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862437, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862676, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862829, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829862901, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829863140, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829863208, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829863302, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829863514, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829863614, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864188, "dur": 181, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864376, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864439, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864499, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864815, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864877, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829864997, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829865240, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829865648, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829865895, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866087, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866156, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866211, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866356, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866415, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866504, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866562, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866619, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866748, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866867, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829866998, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867054, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867252, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867310, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867573, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867635, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867785, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339829867849, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868033, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868091, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868292, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868407, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868520, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868638, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868698, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868833, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829868891, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869039, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869233, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869345, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869535, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869691, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869762, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869853, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869927, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829869993, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339829870357, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339829831673, "dur": 38982, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339829870671, "dur": 1420913, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831291586, "dur": 1289, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831292887, "dur": 201, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831293089, "dur": 61, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831293576, "dur": 66, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831293683, "dur": 183, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831293910, "dur": 2342, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751339829832108, "dur": 38736, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829870852, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829871132, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829871495, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829871635, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829871728, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829871924, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829872148, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339829872452, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829872558, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339829872698, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339829872752, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829872807, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339829873291, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339829873586, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829873894, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829874241, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829875037, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829875756, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829876500, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829877389, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\ByteInspector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751339829877271, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829878773, "dur": 1181, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\State\\InputStateHistory.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751339829878773, "dur": 1993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829880767, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829881563, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829882397, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829884069, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829884632, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829884818, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829886131, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829887213, "dur": 1307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829888520, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829888634, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829888736, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829889232, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829889301, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829889400, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829890073, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829890884, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829890955, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829891029, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829891461, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829891514, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829891791, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829892214, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829892319, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829893168, "dur": 1783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829894952, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829895216, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829895943, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829896438, "dur": 1567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829898005, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829898177, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829899084, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339829900011, "dur": 1391707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829831869, "dur": 38906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829870782, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829871007, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829871101, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829871440, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829871569, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829871628, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829871762, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829872184, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829872342, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339829872424, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829872530, "dur": 835, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751339829873370, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751339829873574, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829873643, "dur": 942, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751339829874587, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\Recommendations\\RecommenderSystemData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751339829874587, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829875941, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829876553, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829877292, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829878766, "dur": 1206, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\EnumerableCloner.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751339829878289, "dur": 2155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829880445, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829881153, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829882065, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829882821, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829883936, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829884638, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829884785, "dur": 1361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829886147, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829887097, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829887207, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829888527, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829888630, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829888740, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829889098, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829889217, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829889306, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829889422, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829890120, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829890908, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829890965, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829891035, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829891427, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829891558, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829891855, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829892347, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829893171, "dur": 1729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829894950, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829895206, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829895935, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829896444, "dur": 1574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829898018, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829898190, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829899088, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339829899986, "dur": 1391601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829831779, "dur": 38937, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829870720, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829871001, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829871151, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829871382, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829871468, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829871735, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829871801, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829871984, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829872366, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829872543, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829872609, "dur": 794, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751339829873505, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829873587, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829873933, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829874268, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829875053, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829875894, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829876564, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829877319, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829878786, "dur": 1165, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\Graph.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751339829878147, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829880029, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829880814, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829881583, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829882365, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829883848, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829884617, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829884834, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829886139, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829887066, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829887177, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339829887491, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829887632, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751339829888545, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829888788, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829889107, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829889171, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829889292, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829889398, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829890092, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829890894, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829890961, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829891039, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829891414, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829891574, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829891818, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829892359, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829893176, "dur": 1731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829894908, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829895177, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829895899, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829896440, "dur": 1561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829898002, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829898180, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829899068, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339829899980, "dur": 1391619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829831641, "dur": 39048, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829870716, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829870961, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829871058, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829871358, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829871440, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829871650, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829871847, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829872108, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829872200, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339829872440, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751339829872693, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751339829872893, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751339829873039, "dur": 483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751339829873525, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751339829873625, "dur": 889, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751339829874516, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829875835, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829876728, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829878751, "dur": 1160, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphNesterElement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751339829878072, "dur": 2258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829880331, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829881125, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829881964, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829883016, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751339829882761, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829883802, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829884662, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829884799, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829886127, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829887209, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829888534, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829888646, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829888750, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829889105, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829889216, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829889300, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829889409, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829890075, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829890893, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829890968, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829891049, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829891452, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829891555, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829891793, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829892124, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829892339, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829893208, "dur": 1739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829894948, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829895213, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829895906, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829896488, "dur": 1535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829898023, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829898185, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829899086, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339829900018, "dur": 1391648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829831771, "dur": 38956, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829870733, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871023, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871217, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829871354, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871533, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871706, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871843, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829871907, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829871971, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829872317, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829872413, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829872483, "dur": 1086, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829873585, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829873820, "dur": 791, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339829874613, "dur": 9897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751339829884661, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829884787, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829886154, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829887086, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829887205, "dur": 1310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829888574, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829888642, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829888746, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829889098, "dur": 1265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829890376, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829890903, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829890966, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829891082, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829891436, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829891529, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829891799, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829892331, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829893162, "dur": 1755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829894918, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829895176, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829895911, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829896452, "dur": 1568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829898020, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829898198, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829899107, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339829900005, "dur": 1391686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829832129, "dur": 38729, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829870863, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829871023, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829871111, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829871406, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829871594, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829871739, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829871808, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829872176, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829872518, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339829872645, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339829872851, "dur": 421, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339829873273, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339829873560, "dur": 874, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751339829874436, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829875471, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829876103, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829876871, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829877742, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829878767, "dur": 1114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Common\\IOnboardingSection.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751339829878600, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829880713, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829881592, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829882358, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829883952, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829884634, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829884816, "dur": 1347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829886163, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829887070, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829887166, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829887596, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751339829888405, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829888525, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829888586, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829888638, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829888780, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829889112, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829889170, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829889235, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829889294, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829889416, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829890131, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829890891, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829890952, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339829891261, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751339829892093, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829892219, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829892321, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829893200, "dur": 1735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829894935, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829895196, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829895930, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829896465, "dur": 1545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829898010, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829898186, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829899069, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339829899993, "dur": 1391588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829831987, "dur": 38802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829870795, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871028, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871186, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871437, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871523, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871742, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829871923, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829872248, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829872441, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339829872714, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339829872893, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339829873087, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751339829873202, "dur": 1073, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339829874277, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829875221, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829875864, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829876757, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829877481, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829878763, "dur": 1173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Connections\\IConnectionCollection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751339829878256, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829880466, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829881747, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829882473, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829883328, "dur": 1314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829884643, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829884795, "dur": 1363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829886158, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829887092, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829887224, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829888523, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829888689, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829888748, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829889114, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829889220, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829889289, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829889489, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829890078, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829890889, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829890956, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829891027, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829891426, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829891512, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829891788, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829892158, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829892343, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829893184, "dur": 1736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829894921, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829895197, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829895937, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829896449, "dur": 1550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829898057, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829898188, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829899083, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829899974, "dur": 60494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829962567, "dur": 446, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751339829960470, "dur": 2547, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339829963018, "dur": 1328613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829832081, "dur": 38744, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829870831, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829870974, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829871076, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829871252, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829871469, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829871526, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829871642, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829871734, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829871893, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829872139, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829872424, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829872527, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829872927, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339829873071, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751339829873311, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339829873519, "dur": 815, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339829874336, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829875317, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829876063, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829876856, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829877668, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829878759, "dur": 1086, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Audio\\AudioPlayableAsset.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751339829878381, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829880156, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829880982, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829881975, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829882751, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829883596, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829884646, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829884822, "dur": 1287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829886110, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829887069, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829887203, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829887741, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751339829889156, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829889330, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339829889683, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829889745, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751339829891023, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829891547, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829891844, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829892354, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829893195, "dur": 1730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829894930, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829895218, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829895927, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829896467, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829898022, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829898189, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829899095, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339829899982, "dur": 1391655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829832278, "dur": 38632, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829870916, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829871181, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829871336, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829871564, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829871684, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829871882, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829872118, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829872293, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829872456, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829872655, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751339829872710, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829872902, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829873030, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829873283, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829873435, "dur": 1023, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751339829874460, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829876079, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\UnitPortDefinitionUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751339829875428, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829877189, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829878777, "dur": 1153, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Extensions\\TrackExtensions.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751339829878378, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829880700, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829881887, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829882708, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829883489, "dur": 1124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829884613, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829884780, "dur": 1339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829886120, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829887075, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829887218, "dur": 1312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829888531, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829888678, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829888759, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829889060, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829889226, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829889302, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829889453, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829890088, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829890945, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829891417, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339829892163, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829892323, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829893186, "dur": 1756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829894942, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829895205, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829895941, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829896451, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829898006, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829898184, "dur": 881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829899066, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829899124, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339829899378, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339829899987, "dur": 1391605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829832240, "dur": 38654, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829870900, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829871054, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829871165, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829871562, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829871694, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829871801, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829871918, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829872168, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829872394, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829872474, "dur": 457, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829872945, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829873226, "dur": 11440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829884833, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829885014, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829886184, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829886380, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829887163, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829887703, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829888072, "dur": 2663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829890736, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829890945, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829891273, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829893048, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829893219, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829893421, "dur": 1385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829894959, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339829895190, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339829895934, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829896463, "dur": 1553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829898017, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829898211, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829899096, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339829900013, "dur": 1391697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829832375, "dur": 38548, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829870928, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829871067, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829871141, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829871479, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829871659, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829871932, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829872256, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829872348, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829872459, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339829872661, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751339829872845, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751339829873279, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751339829873497, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829873593, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829874418, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829875819, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829877006, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829878770, "dur": 1171, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Typeset.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751339829878167, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829880523, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829881287, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829882387, "dur": 2106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829884494, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829884626, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829884776, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829886117, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829887102, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829887210, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829888563, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829888644, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829888769, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829889108, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829889222, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829889298, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829889406, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829890071, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829890897, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829890970, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829891087, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829891415, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829891554, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829891828, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829892329, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829893159, "dur": 1801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829894960, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829895211, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829895904, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829896442, "dur": 1566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829898008, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829898217, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829899104, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339829900007, "dur": 1391668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829832479, "dur": 38459, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829870943, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829871112, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829871193, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829871472, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829871637, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829871854, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829872130, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829872359, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829872525, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339829872669, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829872840, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751339829872998, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751339829873424, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339829873561, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339829873651, "dur": 906, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339829874558, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829875526, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829876168, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829876901, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829877747, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829878761, "dur": 1111, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\BranchPoint.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751339829880074, "dur": 1422, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\UserSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751339829878523, "dur": 3512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829882037, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829883201, "dur": 1410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829884612, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829884782, "dur": 1333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829886115, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829887063, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829887171, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829887752, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751339829889268, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829889432, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339829889795, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751339829891713, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829892325, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829892398, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829893204, "dur": 1719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829894924, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829895168, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829895268, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829895923, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829896468, "dur": 1561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829898030, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829898214, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829899076, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339829900003, "dur": 1391738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829832539, "dur": 38424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829870971, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829871238, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829871407, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829871490, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829871654, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829871940, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829872528, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829873036, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751339829873338, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339829873461, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829873550, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339829873653, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829873710, "dur": 889, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339829875725, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\InputForUI\\InputSystemProvider.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751339829874600, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829876407, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829877170, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829877943, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829878773, "dur": 1156, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\Observables\\ForDeviceEventObservable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751339829878696, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829880628, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829881447, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829882296, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829883826, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829884641, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829884798, "dur": 1323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829886122, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829887091, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829887230, "dur": 1306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829888536, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829888633, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829888735, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829889095, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829889291, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829889412, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829890077, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829890882, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829890958, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829891033, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829891585, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829891806, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829892367, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829893165, "dur": 1750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829894916, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829895165, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829895919, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829896487, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339829896842, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751339829897880, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829898037, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829898213, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829899089, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339829900015, "dur": 1391671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829832642, "dur": 38347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829871006, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829871451, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829871561, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829871630, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829871775, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829871911, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829872089, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829872225, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829872428, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829872573, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751339829872866, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829872956, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751339829873098, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829873217, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829873303, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751339829873607, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751339829874497, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829876999, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Literal.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751339829875994, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829878728, "dur": 1106, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerStayMListener.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751339829877811, "dur": 2506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829880319, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829881475, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829882608, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829883516, "dur": 1128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829884645, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829884826, "dur": 1316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829886144, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829887094, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829887207, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339829887464, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751339829888283, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829889174, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829889296, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829889404, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829890090, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829890953, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829891040, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829891445, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829891527, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829891797, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829892364, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829893161, "dur": 1741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829894903, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829895162, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829895903, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829896446, "dur": 1586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829898033, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829898215, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829899077, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339829899989, "dur": 1391586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829832775, "dur": 38239, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829871019, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829871201, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829871550, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829871619, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829871780, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829871895, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829872133, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829872343, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829872397, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829872493, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339829872707, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829872796, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829872878, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339829872994, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829873053, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339829873169, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751339829873540, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339829873628, "dur": 903, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339829874533, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829875934, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829876692, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829877525, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829878795, "dur": 1165, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\GuidCollection.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751339829878270, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829880262, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829881129, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829881944, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829882727, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829883673, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829884616, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829884788, "dur": 1319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829886167, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829887065, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829887175, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829887864, "dur": 1207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829889105, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751339829889963, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829890126, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339829890356, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751339829891284, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829891454, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829891595, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829891792, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829892358, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829893166, "dur": 1743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829894910, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829895164, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829895901, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829896461, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829898015, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829898173, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829899091, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339829900024, "dur": 1391631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829832837, "dur": 38200, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829871046, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829871390, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829871583, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829871821, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829871960, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829872330, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829872431, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339829872601, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829872800, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751339829872994, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829873048, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751339829873135, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829873224, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339829873622, "dur": 924, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339829874547, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829875820, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829876560, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829877327, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829878764, "dur": 1231, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Settings.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751339829878453, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829880681, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829882827, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\UnityTestProtocol\\Messages\\PlayerSystemInfoMessage.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751339829881868, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829883400, "dur": 1228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829884629, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829884802, "dur": 1333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829886137, "dur": 951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829887089, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829887237, "dur": 1287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829888525, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829888640, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829888738, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829889057, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829889111, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829889221, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829889311, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829889413, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829890080, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829890901, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829890975, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829891031, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829891425, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829891536, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829891882, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829892349, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829893177, "dur": 1754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829894932, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829895194, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829895907, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829896436, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829896491, "dur": 1521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829898013, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829898176, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829899074, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339829899977, "dur": 613329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339830513347, "dur": 776611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751339830513309, "dur": 776652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751339831290006, "dur": 1454, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751339829832965, "dur": 38093, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829871064, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829871347, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829871413, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829871585, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829871670, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829871811, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829871947, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829872232, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829872326, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829872468, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829872980, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829873320, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339829873512, "dur": 747, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751339829874261, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829875204, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829875996, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829876648, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829877404, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829878789, "dur": 1138, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsTypeCache.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751339829878189, "dur": 2127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829880317, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829881107, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829882114, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829882967, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829883142, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829883209, "dur": 1400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829884666, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829884805, "dur": 1340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829886145, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829887082, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829887226, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829888518, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829888643, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829888773, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829889118, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829889215, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829889304, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829889420, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829890098, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829890895, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829890952, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339829891280, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751339829892000, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829892091, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829892330, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829893157, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829893226, "dur": 1685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829894912, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829895167, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829895896, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829896456, "dur": 1578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829898035, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829898210, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829899080, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339829899984, "dur": 1391594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829833073, "dur": 38003, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829871083, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829871405, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829871579, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829871768, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829871879, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829872066, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829872179, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829872328, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829872433, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829872513, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829872598, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751339829872796, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829872847, "dur": 753, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751339829873602, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829873904, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829874250, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829875726, "dur": 859, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Analytics\\StateMacroSavedEvent.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751339829875113, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829876854, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829877619, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829878792, "dur": 1132, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751339829878383, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829880336, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829881135, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829881963, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829882815, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829883737, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829884639, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829884791, "dur": 1342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829886133, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829887194, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829888018, "dur": 3472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751339829891496, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829891869, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339829892451, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751339829893174, "dur": 1794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829894969, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829895190, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829895936, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829896458, "dur": 1545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829898003, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829898174, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829899125, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339829899991, "dur": 1391734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829833146, "dur": 37947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829871099, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829871394, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829871624, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829871815, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829872113, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829872210, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829872639, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751339829872900, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751339829873329, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751339829873499, "dur": 687, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751339829874188, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829874971, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829875821, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829876621, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829877393, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829878769, "dur": 1139, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsExceptions.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751339829878191, "dur": 2156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829880349, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829881152, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829881994, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829882820, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829884254, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829884663, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829884790, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829886112, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829887078, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829887196, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339829887766, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829887917, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751339829889089, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829889289, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829889459, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829890089, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829890905, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829890960, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829891046, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829891421, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829891542, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829891871, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829892318, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829893170, "dur": 1728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829894964, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829895200, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829895892, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829896448, "dur": 1580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829898029, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829898199, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829899097, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339829900022, "dur": 1391623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829833256, "dur": 37855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829871116, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829871375, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829871494, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829871573, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829871687, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829871774, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829871913, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829872127, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829872375, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829872525, "dur": 478, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829873178, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751339829873547, "dur": 868, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751339829875164, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_PostBuildProcessHandler.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751339829874417, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829875878, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829876694, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829877535, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829878779, "dur": 1159, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751339829878350, "dur": 2036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829880387, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829881693, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829882831, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829883248, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829884621, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829884823, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829886114, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829887080, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829887185, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339829887507, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751339829888454, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829888684, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829888756, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829889102, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829889173, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829889224, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829889287, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829889424, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829890096, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829890898, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829890971, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829891050, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829891443, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829891518, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829891795, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829892327, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829893202, "dur": 1702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829894906, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829895178, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829895894, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829896454, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829898031, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829898201, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829899071, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339829900000, "dur": 1391595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829833344, "dur": 37783, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829871132, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339829871618, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339829871926, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339829872335, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829872533, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751339829872848, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751339829872945, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751339829873007, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829873061, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751339829873126, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829873177, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751339829873520, "dur": 833, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751339829874355, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829875372, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829876480, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829877275, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829878771, "dur": 1171, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Types.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751339829878150, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829880117, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829880893, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829881693, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829882530, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829883414, "dur": 1216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829884631, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829884793, "dur": 1331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829886124, "dur": 975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829887100, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829887197, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339829887593, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829888117, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751339829888957, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829889106, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829889341, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829889401, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829890086, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829890973, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829891044, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829891418, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829891517, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829891831, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829892271, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829892332, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829893224, "dur": 1708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829894933, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829895214, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829895914, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829896473, "dur": 1540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829898014, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829898178, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829899072, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339829900021, "dur": 1391603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829833430, "dur": 37724, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829871164, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339829871436, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829871513, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339829871701, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829871793, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339829872195, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339829872352, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339829872470, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339829872544, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829872603, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339829872894, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339829873293, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339829873503, "dur": 741, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751339829874891, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ManipulationsTimeline.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339829875726, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ItemGui\\TimelineItemGUI.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339829874247, "dur": 2710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829876958, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Licenses\\License.MSPL.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339829876958, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829878775, "dur": 1144, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339829878504, "dur": 2192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829880697, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829881490, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829882359, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829884472, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829884619, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829884778, "dur": 1362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829886141, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829887084, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829887201, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829888529, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829888636, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829888742, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829889109, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829889168, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829889227, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829889313, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829889415, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829890095, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829890910, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829890972, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829891037, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829891448, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829891534, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829891891, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829892335, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829893205, "dur": 1708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829894914, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829895182, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829895918, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829896476, "dur": 1550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829898027, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829898193, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829899093, "dur": 915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339829900009, "dur": 1391726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829833503, "dur": 37663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829871172, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829871419, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829871557, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829871748, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829871804, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829871901, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829872136, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829872240, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339829872391, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829872520, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829872572, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751339829872775, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751339829873031, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751339829873315, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751339829873542, "dur": 852, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751339829874395, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829875458, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829876206, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829876906, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829877638, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829878788, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_DefaultControls.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751339829878489, "dur": 2270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829880760, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829882045, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829883252, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829884635, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829884811, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829886130, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829887073, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829887234, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829888535, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829888649, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829888745, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829889097, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829889218, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829889335, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829889418, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829890083, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829890886, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829890963, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829891042, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829891432, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829891532, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829891905, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829892336, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829893213, "dur": 1740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829894954, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829895180, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829895897, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829896503, "dur": 1494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829898052, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829898222, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829899081, "dur": 895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829899976, "dur": 63050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339829963027, "dur": 1328584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829833563, "dur": 37614, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829871178, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829871359, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829871437, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829871693, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829871854, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829871952, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829872446, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829872665, "dur": 718, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339829873541, "dur": 832, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339829874376, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829875356, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829876700, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829877447, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829878495, "dur": 1442, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\RectOffset_DirectConverter.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751339829878206, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829880547, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829881365, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829882211, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829883343, "dur": 1280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829884624, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829884783, "dur": 1368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829886151, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829887081, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829887164, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829887625, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829888899, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829889101, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829889547, "dur": 1215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829890763, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829891034, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829891125, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829891426, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829891510, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829891793, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829892355, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829893221, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829893452, "dur": 1593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829895046, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829895210, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829895424, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829896485, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829896846, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829898244, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829898438, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829899115, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339829899355, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829899972, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339829901226, "dur": 108, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339829902252, "dur": 594099, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339830513330, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1751339830513299, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1751339830513781, "dur": 2889, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 24, "ts": 1751339830516676, "dur": 774927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339831299626, "dur": 3412, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21056, "tid": 675, "ts": 1751339831334524, "dur": 7286, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21056, "tid": 675, "ts": 1751339831341871, "dur": 4521, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21056, "tid": 675, "ts": 1751339831326044, "dur": 21881, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}