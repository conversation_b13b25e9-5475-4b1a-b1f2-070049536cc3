{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21056, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21056, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21056, "tid": 705, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21056, "tid": 705, "ts": 1751340365077263, "dur": 1027, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365084633, "dur": 1445, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21056, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21056, "tid": 1, "ts": 1751340363601903, "dur": 20618, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751340363622526, "dur": 92076, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751340363714622, "dur": 50681, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365086085, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 21056, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363597674, "dur": 22776, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363620457, "dur": 1444403, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363622798, "dur": 6882, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363629688, "dur": 4212, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363633909, "dur": 1641, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635559, "dur": 30, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635590, "dur": 74, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635672, "dur": 6, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635681, "dur": 61, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635744, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635748, "dur": 48, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635800, "dur": 2, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635803, "dur": 109, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635916, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635919, "dur": 69, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363635996, "dur": 3, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636002, "dur": 72, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636081, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636188, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636194, "dur": 75, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636273, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636277, "dur": 50, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636332, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636335, "dur": 54, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636393, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636396, "dur": 57, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636457, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636460, "dur": 47, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636511, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636514, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636569, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636571, "dur": 56, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636632, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636635, "dur": 62, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636704, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636710, "dur": 73, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636787, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636790, "dur": 50, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636844, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636847, "dur": 50, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636901, "dur": 3, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636905, "dur": 40, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636949, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636952, "dur": 40, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636996, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363636998, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637046, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637049, "dur": 49, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637102, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637105, "dur": 45, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637154, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637156, "dur": 47, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637207, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637210, "dur": 46, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637260, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637263, "dur": 45, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637311, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637314, "dur": 44, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637362, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637365, "dur": 71, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637441, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637493, "dur": 3, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637497, "dur": 51, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637552, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637555, "dur": 55, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637614, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637617, "dur": 52, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637673, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637676, "dur": 75, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637758, "dur": 3, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637764, "dur": 51, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637819, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637822, "dur": 52, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637878, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637881, "dur": 54, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637939, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637942, "dur": 48, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637995, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363637997, "dur": 41, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638042, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638045, "dur": 65, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638117, "dur": 3, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638123, "dur": 61, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638188, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638191, "dur": 50, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638246, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638249, "dur": 60, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638313, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638316, "dur": 53, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638377, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638382, "dur": 60, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638446, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638449, "dur": 47, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638500, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638502, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638550, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638553, "dur": 44, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638603, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638660, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638663, "dur": 51, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638719, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638721, "dur": 44, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638770, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638774, "dur": 46, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638824, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638826, "dur": 45, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638876, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638878, "dur": 63, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638949, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363638954, "dur": 59, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639019, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639022, "dur": 48, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639074, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639077, "dur": 53, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639134, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639137, "dur": 55, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639196, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639199, "dur": 46, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639250, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639253, "dur": 46, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639302, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639305, "dur": 50, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639359, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639362, "dur": 65, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639434, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639440, "dur": 61, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639507, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639510, "dur": 49, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639563, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639566, "dur": 50, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639620, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639623, "dur": 55, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639682, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639685, "dur": 50, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639738, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639741, "dur": 77, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639825, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639831, "dur": 49, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639884, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639887, "dur": 62, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639953, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363639956, "dur": 45, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640005, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640008, "dur": 86, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640101, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640107, "dur": 94, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640206, "dur": 3, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640211, "dur": 52, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640267, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640270, "dur": 41, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640315, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640318, "dur": 47, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640368, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640371, "dur": 49, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640424, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640427, "dur": 58, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640490, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640494, "dur": 49, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640547, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640550, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640598, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640601, "dur": 44, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640649, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640652, "dur": 61, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640721, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640726, "dur": 57, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640787, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640790, "dur": 49, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640843, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640845, "dur": 50, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640900, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640903, "dur": 49, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640956, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363640959, "dur": 58, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641022, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641025, "dur": 64, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641096, "dur": 3, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641101, "dur": 64, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641169, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641173, "dur": 312, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641493, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641572, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641578, "dur": 68, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641650, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641653, "dur": 61, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641723, "dur": 3, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641729, "dur": 71, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641805, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641808, "dur": 58, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641873, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641878, "dur": 57, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641943, "dur": 5, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363641951, "dur": 71, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642026, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642029, "dur": 60, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642097, "dur": 3, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642102, "dur": 77, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642183, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642186, "dur": 60, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642253, "dur": 7, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642263, "dur": 69, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642337, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642340, "dur": 39, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642384, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642387, "dur": 70, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642464, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642470, "dur": 67, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642547, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642551, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642634, "dur": 3, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642639, "dur": 53, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642697, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642700, "dur": 60, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642767, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642772, "dur": 80, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642858, "dur": 8, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642869, "dur": 50, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642923, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642926, "dur": 56, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642991, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363642995, "dur": 48, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643047, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643050, "dur": 60, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643118, "dur": 4, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643126, "dur": 98, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643232, "dur": 10, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643245, "dur": 76, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643326, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643329, "dur": 67, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643402, "dur": 3, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643408, "dur": 60, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643480, "dur": 4, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643487, "dur": 67, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643558, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643561, "dur": 58, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643627, "dur": 3, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643634, "dur": 71, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643711, "dur": 5, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643722, "dur": 71, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643798, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643802, "dur": 50, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643888, "dur": 2, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643891, "dur": 59, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643955, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363643958, "dur": 63, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644028, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644034, "dur": 87, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644125, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644129, "dur": 56, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644189, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644192, "dur": 58, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644257, "dur": 3, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644263, "dur": 64, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644331, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644334, "dur": 61, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644403, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644408, "dur": 74, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644487, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644490, "dur": 50, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644544, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644547, "dur": 46, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644597, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644600, "dur": 60, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644667, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644672, "dur": 66, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644742, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644745, "dur": 58, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644809, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644812, "dur": 63, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644883, "dur": 3, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644888, "dur": 62, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644952, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363644956, "dur": 49, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645010, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645015, "dur": 52, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645072, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645074, "dur": 57, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645139, "dur": 3, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645144, "dur": 58, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645208, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645210, "dur": 61, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645280, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645286, "dur": 76, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645368, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645371, "dur": 59, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645438, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645443, "dur": 64, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645511, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645514, "dur": 62, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645583, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645587, "dur": 85, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645679, "dur": 4, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645686, "dur": 84, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645777, "dur": 4, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645783, "dur": 62, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645849, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645853, "dur": 57, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645917, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645922, "dur": 61, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645988, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363645991, "dur": 57, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646055, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646061, "dur": 66, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646130, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646133, "dur": 59, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646199, "dur": 2, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646204, "dur": 69, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646277, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646280, "dur": 55, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646342, "dur": 3, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646347, "dur": 79, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646431, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646434, "dur": 74, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646516, "dur": 3, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646522, "dur": 89, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646615, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646619, "dur": 49, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646672, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646675, "dur": 51, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646730, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646734, "dur": 58, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646796, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646799, "dur": 50, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646853, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646856, "dur": 62, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646928, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363646933, "dur": 88, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647029, "dur": 4, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647035, "dur": 71, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647111, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647115, "dur": 54, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647173, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647176, "dur": 82, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647265, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647271, "dur": 96, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647371, "dur": 2, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647374, "dur": 67, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647449, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647454, "dur": 65, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647524, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647528, "dur": 63, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647599, "dur": 4, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647606, "dur": 87, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647699, "dur": 5, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647708, "dur": 94, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647810, "dur": 3, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647815, "dur": 109, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647931, "dur": 4, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363647937, "dur": 69, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648013, "dur": 3, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648019, "dur": 73, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648096, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648100, "dur": 62, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648167, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648171, "dur": 67, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648245, "dur": 4, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648251, "dur": 77, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648332, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648336, "dur": 61, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648406, "dur": 4, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648414, "dur": 114, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648534, "dur": 6, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648542, "dur": 60, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648607, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648611, "dur": 60, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648678, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648683, "dur": 69, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648757, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648761, "dur": 59, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648827, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648832, "dur": 91, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648934, "dur": 6, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363648944, "dur": 73, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649022, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649025, "dur": 62, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649099, "dur": 4, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649105, "dur": 90, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649202, "dur": 3, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649211, "dur": 115, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649335, "dur": 7, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649347, "dur": 121, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649482, "dur": 6, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649492, "dur": 87, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649588, "dur": 6, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649598, "dur": 71, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649675, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649682, "dur": 58, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649747, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649751, "dur": 54, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649811, "dur": 2, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649816, "dur": 59, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649883, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649887, "dur": 74, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649968, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363649972, "dur": 61, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650043, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650047, "dur": 58, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650113, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650117, "dur": 71, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650193, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650197, "dur": 57, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650258, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650261, "dur": 65, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650335, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650343, "dur": 74, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650422, "dur": 3, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650428, "dur": 62, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650496, "dur": 2, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650501, "dur": 128, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650641, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650725, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650728, "dur": 76, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650811, "dur": 2, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650814, "dur": 53, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650875, "dur": 2, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650880, "dur": 63, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650950, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363650953, "dur": 64, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651023, "dur": 4, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651030, "dur": 52, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651096, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651100, "dur": 68, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651176, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651180, "dur": 63, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651250, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651254, "dur": 63, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651327, "dur": 2, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651332, "dur": 58, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651398, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651402, "dur": 63, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651473, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651477, "dur": 62, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651546, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651550, "dur": 67, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651623, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651627, "dur": 71, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651710, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651719, "dur": 72, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651797, "dur": 3, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651802, "dur": 64, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651873, "dur": 3, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651879, "dur": 69, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651954, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363651958, "dur": 72, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652037, "dur": 3, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652043, "dur": 64, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652113, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652117, "dur": 111, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652239, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652314, "dur": 3, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652320, "dur": 69, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652397, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652402, "dur": 60, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652470, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652474, "dur": 72, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652552, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652556, "dur": 67, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652629, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652633, "dur": 65, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652705, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652710, "dur": 81, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652796, "dur": 5, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652803, "dur": 68, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652877, "dur": 4, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652884, "dur": 74, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652963, "dur": 3, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363652968, "dur": 79, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653053, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653059, "dur": 90, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653154, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653159, "dur": 84, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653249, "dur": 4, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653255, "dur": 76, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653337, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653341, "dur": 72, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653420, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653426, "dur": 74, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653505, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653510, "dur": 83, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653599, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653603, "dur": 72, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653680, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653685, "dur": 67, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653758, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653763, "dur": 83, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653852, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653857, "dur": 77, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653940, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363653945, "dur": 74, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654028, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654033, "dur": 60, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654097, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654103, "dur": 427, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654538, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654542, "dur": 122, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654677, "dur": 11, "ph": "X", "name": "ProcessMessages 2168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654691, "dur": 62, "ph": "X", "name": "ReadAsync 2168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654759, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363654764, "dur": 1194, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363655963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363655966, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363656028, "dur": 988, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657033, "dur": 149, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657188, "dur": 24, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657216, "dur": 58, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657280, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657286, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657354, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657360, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657418, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657423, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657484, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657492, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657553, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657562, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657627, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657633, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657707, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657712, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657765, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657770, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657843, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657848, "dur": 71, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657927, "dur": 8, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657940, "dur": 52, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363657999, "dur": 9, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658013, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658064, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658073, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658133, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658139, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658206, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658214, "dur": 55, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658274, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658278, "dur": 62, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658347, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658353, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658423, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658428, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658509, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658516, "dur": 63, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658584, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658591, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658657, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658669, "dur": 73, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658748, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658754, "dur": 65, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658825, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658832, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658894, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658900, "dur": 55, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658964, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363658968, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659012, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659022, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659078, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659083, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659147, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659153, "dur": 70, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659230, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659236, "dur": 54, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659295, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659302, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659357, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659362, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659414, "dur": 8, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659426, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659488, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363659493, "dur": 618, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660120, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660174, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660179, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660326, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660444, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660450, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660496, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660575, "dur": 61, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363660640, "dur": 882, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363661528, "dur": 10741, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672303, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672320, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672458, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672467, "dur": 63, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672537, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672542, "dur": 361, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672907, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672909, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363672956, "dur": 2179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675143, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675148, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675222, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675227, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675299, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675301, "dur": 336, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675641, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675644, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675693, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363675696, "dur": 1186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363676887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363676890, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363676954, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363676958, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677022, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677033, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677085, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677471, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677477, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677539, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677544, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677592, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677598, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677626, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677629, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677657, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677660, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677904, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677940, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363677944, "dur": 704, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678655, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678663, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678700, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678704, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678762, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678767, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678817, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678824, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678868, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678871, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678932, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678983, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363678986, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679023, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679026, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679079, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679109, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679113, "dur": 186, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679309, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679360, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679364, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679422, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679428, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679476, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679479, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679528, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679534, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679594, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679598, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679647, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679653, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679698, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679702, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679747, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679751, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679814, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679818, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679889, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679894, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363679976, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680026, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680031, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680083, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680087, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680140, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680144, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680209, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680288, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680295, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680360, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680370, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680419, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680422, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680472, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680476, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680532, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680536, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680591, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680596, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680768, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363680845, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681069, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681076, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681109, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681116, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681172, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681177, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681231, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681236, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681284, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681289, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681482, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681524, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681531, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681567, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681570, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681617, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681621, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681670, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681676, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681733, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681737, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681791, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363681795, "dur": 392, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682196, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682247, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682252, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682303, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682308, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682366, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682370, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682419, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682423, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682477, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682482, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363682538, "dur": 1055, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683598, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683602, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683661, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683665, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683726, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363683731, "dur": 269, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363684008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363684012, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363684075, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363684078, "dur": 2455, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686542, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686548, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686610, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686615, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686683, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686687, "dur": 244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686939, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686995, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363686999, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687105, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687162, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687166, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687245, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687249, "dur": 55, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687313, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687317, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687466, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687514, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687521, "dur": 428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363687957, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688000, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688003, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688067, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688077, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688140, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688143, "dur": 336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688488, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688549, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688608, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688611, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688669, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688673, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688802, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688805, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688857, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363688861, "dur": 762, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689631, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689683, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689686, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689742, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689746, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689827, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689834, "dur": 124, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689967, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363689971, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690033, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690037, "dur": 532, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690575, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690579, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690641, "dur": 7, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690651, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690720, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690727, "dur": 257, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690992, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363690998, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691053, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691057, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691116, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691120, "dur": 788, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691915, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691982, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363691989, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363692056, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363692065, "dur": 2039, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363694111, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363694116, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363694180, "dur": 1717, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340363695903, "dur": 553272, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364249185, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364249189, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364249245, "dur": 2287, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364251537, "dur": 7287, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364258830, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364258834, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364258882, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364258887, "dur": 1566, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364260457, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364260459, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364260512, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340364260533, "dur": 793193, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365053735, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365053740, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365053798, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365053804, "dur": 804, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365054613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365054615, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365054681, "dur": 33, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365054718, "dur": 1273, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365055996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365055999, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365056044, "dur": 604, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751340365056653, "dur": 8131, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365086106, "dur": 2298, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21056, "tid": 8589934592, "ts": 1751340363589555, "dur": 175805, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751340363765364, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751340363765374, "dur": 7214, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365088407, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21056, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21056, "tid": 4294967296, "ts": 1751340363556639, "dur": 1509826, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751340363564489, "dur": 13399, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751340365066748, "dur": 6636, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751340365070951, "dur": 156, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751340365073522, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365088420, "dur": 21, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751340363606951, "dur": 2094, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340363609062, "dur": 599, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340363609733, "dur": 87, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751340363609821, "dur": 1159, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340363613226, "dur": 14985, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363630613, "dur": 5494, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363636219, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363636290, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363636689, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363636806, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363636998, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363637167, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363637317, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363637594, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363637655, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363638045, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363638282, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363638425, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363638799, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363638996, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363639209, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363639268, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363639489, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363639687, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640063, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640233, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640344, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640494, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640617, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640716, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363640798, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363641043, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363641330, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_514B12BC0D1D8DB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363641395, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363641560, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363642170, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363642330, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363642573, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363642731, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363642857, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643176, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643308, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643373, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643538, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643658, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643754, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363643852, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644032, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644104, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644260, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644339, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644516, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644652, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644732, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363644884, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645038, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645293, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645361, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645509, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645634, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645765, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363645902, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646189, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646296, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646534, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646657, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646817, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363646957, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647029, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647153, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647461, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647557, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647734, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363647870, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648134, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648225, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648312, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648417, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648539, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648633, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648701, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363648889, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649057, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649150, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649291, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649468, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649551, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649694, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649851, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363649980, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650114, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650251, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650362, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650452, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650561, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650637, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650721, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650834, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363650940, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651165, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651314, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651439, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651526, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651645, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363651786, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652008, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652073, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652135, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652213, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652330, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652423, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652495, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652571, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652841, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363652942, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653024, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653085, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653174, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653305, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653413, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653503, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653572, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653664, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653762, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653858, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751340363653945, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654030, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654124, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654297, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654369, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654461, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654539, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751340363654622, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751340363611023, "dur": 44270, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340363655317, "dur": 1400249, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365055570, "dur": 503, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365056073, "dur": 57, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365056131, "dur": 54, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365056473, "dur": 79, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365056576, "dur": 1493, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751340363610776, "dur": 44559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363655363, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363656561, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363656735, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363656813, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363656965, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363657107, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363657227, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363657558, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363657846, "dur": 681, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751340363658536, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751340363658699, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363658781, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751340363659331, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751340363659544, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751340363659768, "dur": 1153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751340363660924, "dur": 4437, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Animation\\AnimationClipActions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751340363665519, "dur": 812, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\TimelineAction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751340363660923, "dur": 5759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363666683, "dur": 1001, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\InstrumentationPoint.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751340363666683, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363668237, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363669110, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363670025, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363671619, "dur": 1269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363672888, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363673026, "dur": 2759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363675786, "dur": 1751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363677538, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363677772, "dur": 1732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363679505, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363679658, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363680033, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363680093, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363680198, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363680666, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363680784, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363681048, "dur": 1127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363682176, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363682424, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363682912, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363683016, "dur": 1226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363684242, "dur": 2925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363687229, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363687776, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363688571, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363689111, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363689162, "dur": 1134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363690296, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363691189, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363691243, "dur": 1337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751340363692580, "dur": 1362875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363611929, "dur": 43854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363655789, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751340363657221, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751340363657565, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751340363657716, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751340363658029, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751340363658226, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751340363658429, "dur": 2610, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751340363661043, "dur": 4194, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\RiderStyles.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751340363661043, "dur": 5237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363666550, "dur": 1252, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\AssemblyQualifiedNameParser\\ParsedAssemblyQualifiedName.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751340363666281, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363668219, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363668859, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363669839, "dur": 1043, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestSettings\\ITestSettingsDeserializer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751340363669467, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363671023, "dur": 1992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363673015, "dur": 2796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363675812, "dur": 1735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363677547, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363677685, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751340363678414, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363678483, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751340363679948, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363680129, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363680217, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363680668, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363680803, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363681077, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363681806, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363682198, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363682388, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363682900, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363683009, "dur": 1241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363684251, "dur": 2958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363687209, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363687732, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363688577, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363689121, "dur": 1190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363690312, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363691222, "dur": 1345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751340363692568, "dur": 1362932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363610837, "dur": 44529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363655373, "dur": 1318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363656735, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363656894, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363657055, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363657186, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363657368, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363657819, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751340363658046, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363658333, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751340363658849, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363659196, "dur": 1785, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751340363660984, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\PropertyDrawers\\TMP_SpriteCharacterPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751340363660983, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363662673, "dur": 3358, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectOnIntegerDescriptor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751340363662199, "dur": 3879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363666558, "dur": 1340, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorWideAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751340363666079, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363667990, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363668766, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363669828, "dur": 1107, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\UnityTestProtocol\\TesRunDataHolder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751340363669447, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363671133, "dur": 1893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363673027, "dur": 2789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363675817, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363677824, "dur": 1430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363679312, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363679428, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363679761, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363680042, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363680172, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363680284, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363680679, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363680793, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363681070, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363681788, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363682219, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363682371, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363682876, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363683058, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363684253, "dur": 2969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363687223, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363687720, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363688597, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363689139, "dur": 1182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363690321, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363691307, "dur": 1276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751340363692583, "dur": 1362847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363610879, "dur": 44504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363655390, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363656855, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363656984, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363657333, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363657668, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363657902, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363658012, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363658096, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363658209, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363658433, "dur": 924, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751340363659431, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751340363659604, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751340363659717, "dur": 1284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751340363661003, "dur": 4156, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageStats\\ICoverageStatsProvider.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751340363661002, "dur": 4754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363666239, "dur": 1553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\AndHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751340363665756, "dur": 2183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363667940, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363668698, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363669288, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363669797, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363670351, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363671652, "dur": 1231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363672883, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363673056, "dur": 2727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363675784, "dur": 1740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363677524, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363677612, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363678070, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751340363679469, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363679677, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751340363680099, "dur": 1445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751340363681546, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363681769, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363682176, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363682369, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363682908, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363683039, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363684256, "dur": 2938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363687195, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363687758, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363688593, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363689125, "dur": 1204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363690329, "dur": 956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363691285, "dur": 1291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751340363692576, "dur": 1362908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363611972, "dur": 43828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363655806, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363656757, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363656887, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363657028, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363657169, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363657722, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363658069, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363658203, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751340363658484, "dur": 778, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751340363659382, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363659454, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751340363659582, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363659696, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751340363659791, "dur": 1260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751340363661053, "dur": 4164, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\PluginSettings.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751340363661052, "dur": 4703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363665756, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363666691, "dur": 913, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloner.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751340363666359, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363668201, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363668778, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363669431, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363669988, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363671754, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363672877, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363673117, "dur": 2676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363675793, "dur": 1724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363677876, "dur": 1390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363679266, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363679405, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363679691, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363679970, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363680100, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363680267, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363680687, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363680743, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363681049, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363681828, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363682158, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363682385, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363682882, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363683008, "dur": 1251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363684259, "dur": 2945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363687205, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363687749, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363688581, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363689130, "dur": 1112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363690266, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363690327, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363691294, "dur": 1306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751340363692600, "dur": 1362773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363612096, "dur": 43722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363655829, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751340363656800, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751340363656953, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751340363657357, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751340363657736, "dur": 590, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751340363658382, "dur": 416, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751340363658801, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751340363658909, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363658988, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363659151, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751340363659307, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363659441, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751340363659664, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363659799, "dur": 1301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751340363661102, "dur": 1122, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\Recommendations\\RecommenderSystemData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751340363661102, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363662832, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363663366, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363663944, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363664476, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363664960, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363665445, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363666694, "dur": 938, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_FontAssetCommon.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751340363667768, "dur": 2160, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ColorGradient.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751340363666577, "dur": 3548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363670125, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363670873, "dur": 2211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363673085, "dur": 2769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363675855, "dur": 2334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363678190, "dur": 1441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363679632, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363680012, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363680138, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363680235, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363680629, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363680752, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363681071, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363681972, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363682246, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363682394, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363682886, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363683021, "dur": 1206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363684227, "dur": 2956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363687184, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363687722, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363688584, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363689124, "dur": 1125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363690277, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363691192, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363692542, "dur": 80622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363776512, "dur": 382, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751340363773166, "dur": 3732, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751340363776898, "dur": 1278435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363611100, "dur": 44353, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363655461, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363656541, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363656881, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363657006, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363657134, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363657273, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363657631, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363657764, "dur": 793, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363658653, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751340363659305, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751340363659919, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363661288, "dur": 1546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Move\\MoveItemHandler.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751340363660645, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363663141, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363664062, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363664992, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363665514, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363666692, "dur": 1119, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelineAsset.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751340363666366, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363668009, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363669909, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\PlasticApp.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751340363668955, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363670860, "dur": 1959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363672831, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363673097, "dur": 2746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363675844, "dur": 2355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363678199, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363679300, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363679641, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363679952, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363680014, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363680130, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363680237, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363680708, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363680771, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363681061, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363682008, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363682179, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363682403, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363682869, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363683057, "dur": 1220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363684279, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363684606, "dur": 2977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363687585, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363687789, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363688032, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363689158, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363689375, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363690130, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363690300, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363690538, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363691081, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363691235, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751340363691564, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363692411, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363692538, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340363693913, "dur": 117, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751340363695660, "dur": 554110, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751340364259145, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751340364259126, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751340364259388, "dur": 1657, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751340364261050, "dur": 794505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363611146, "dur": 44356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363655508, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363656942, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363657573, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363657714, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363658070, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363658196, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363659419, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751340363659571, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751340363659849, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751340363659949, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363660463, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363661475, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363662090, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363662680, "dur": 1674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\FlowGraphContext.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751340363662582, "dur": 2576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363665159, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363665871, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363666712, "dur": 909, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751340363667760, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ResourcesManager.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751340363666554, "dur": 1936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363668490, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363669028, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363669537, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363670193, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363670984, "dur": 2120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363673105, "dur": 2684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363675791, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363677535, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363677613, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363678075, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363678230, "dur": 1748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751340363679979, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363680135, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363680578, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751340363682771, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363683069, "dur": 1169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363684238, "dur": 2997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363687236, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363687750, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363688575, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363689119, "dur": 1197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363690316, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363691241, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751340363691642, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340363692548, "dur": 566608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751340364259190, "dur": 795045, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751340364259158, "dur": 795080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751340365054267, "dur": 947, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751340363611307, "dur": 44207, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363655518, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363656864, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363656989, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363657088, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363657155, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363657301, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363657800, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363657933, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363658213, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363658341, "dur": 388, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751340363658774, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751340363658833, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363658930, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363659066, "dur": 1840, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751340363660909, "dur": 624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Audio\\AudioPlayableAssetInspector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751340363660908, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363661970, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363662518, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363663008, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363663497, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363663969, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363664460, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363664916, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363666294, "dur": 1824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvoker_4.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751340363665656, "dur": 2498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363668265, "dur": 995, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\PropertyDrawers\\InputControlPathDrawer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751340363668154, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363669674, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363670303, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363670957, "dur": 2054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363673011, "dur": 2852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363675865, "dur": 2727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363678592, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363679262, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363679563, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363679657, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363679949, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363680189, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363680611, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363680673, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363680814, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363681063, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363681425, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363682017, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363682264, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363682366, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363682892, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363683010, "dur": 1210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363684221, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363684280, "dur": 2953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363687234, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363687756, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363688605, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363689142, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363690286, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363691242, "dur": 1318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751340363692561, "dur": 1362950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363611359, "dur": 44171, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363655538, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363656583, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363656827, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363656968, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363657097, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363657794, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363657939, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363658179, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363658384, "dur": 1366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751340363659752, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751340363659869, "dur": 844, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751340363660716, "dur": 1481, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Items\\ITimelineItem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751340363660716, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363662685, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363663226, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363663994, "dur": 994, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\GetMember.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751340363663711, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363665250, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363665953, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363666701, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\ClipCaps.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751340363667763, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\AssetUpgrade\\TrackUpgrade.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751340363666536, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363668483, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363669064, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363669832, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestResultSerializer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751340363669635, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363670972, "dur": 2116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363673089, "dur": 2686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363675783, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363675858, "dur": 1758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363677640, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751340363678072, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363678153, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751340363679375, "dur": 761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363680212, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363680645, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363680744, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363681072, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363682166, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363682324, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363682931, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363683032, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363684248, "dur": 2942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363687191, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363687755, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363688583, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363689115, "dur": 1143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363690259, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363690331, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363691264, "dur": 1305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751340363692570, "dur": 1362720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363611393, "dur": 44154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363655554, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363656547, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363657261, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363657668, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363657888, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363658042, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363658223, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363658472, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751340363658808, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751340363659043, "dur": 879, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751340363659924, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363660451, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363661568, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363662177, "dur": 1879, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\VariableUnitOption.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751340363662113, "dur": 2329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363664443, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363664906, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363665484, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363666065, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363666716, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\FontFeatureCommonGSUB.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751340363667769, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Mono.Reflection\\Disassembler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751340363666645, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363668590, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363669160, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363669702, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363670347, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363670863, "dur": 2243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363673107, "dur": 2771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363675879, "dur": 1661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363677540, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363677648, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363678045, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751340363679151, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363679305, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363679411, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363679707, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363679958, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363680221, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363680584, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363680644, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363680754, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363681065, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363681385, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363681720, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751340363682693, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363682800, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363682872, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363683043, "dur": 1181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363684224, "dur": 2951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363687176, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363687764, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363688588, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363689160, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751340363689428, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751340363690165, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363690319, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363691220, "dur": 1346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751340363692567, "dur": 1362926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363611434, "dur": 44128, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363655569, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363656545, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363656718, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363656915, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363657039, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363657662, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363657768, "dur": 465, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751340363658238, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751340363658453, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751340363658670, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363659149, "dur": 1787, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751340363660944, "dur": 4674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_UIStyleManager.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751340363660944, "dur": 5184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363666129, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363666710, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Common\\StyleConstants.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751340363666710, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363668369, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363669172, "dur": 1794, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\EnumPopupSetting.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751340363668900, "dur": 2358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363671258, "dur": 1564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363672823, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363673093, "dur": 2702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363675796, "dur": 1726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363677523, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363677781, "dur": 1714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363679496, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363679685, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363679999, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363680116, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363680170, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363680655, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363680763, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363681058, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363682129, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363682431, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363682922, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363683019, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363684234, "dur": 2936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363687171, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363687724, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363688606, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363689132, "dur": 1168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363690301, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363691206, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751340363692572, "dur": 1362720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363611489, "dur": 44085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363655579, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363656554, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363657288, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363657544, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363657841, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363658012, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363658085, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363658184, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363658398, "dur": 485, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751340363658885, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751340363658990, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363659156, "dur": 681, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751340363659885, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751340363659998, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363660541, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363661594, "dur": 1157, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Transitions\\TriggerStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751340363661468, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363663131, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363663603, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363664152, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363664665, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363665541, "dur": 2186, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\Internal\\GUIHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751340363665232, "dur": 2785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363668294, "dur": 909, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Events\\StateEvent.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751340363668018, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363669479, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363670261, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363671189, "dur": 1646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363672836, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363673083, "dur": 2783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363675866, "dur": 1679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363677546, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363677656, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751340363678134, "dur": 1130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363679273, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751340363680427, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363680586, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363680842, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363681067, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363682185, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363682415, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363682918, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363683041, "dur": 1184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363684226, "dur": 2937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363687222, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363687790, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363688586, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363689125, "dur": 1182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363690307, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363691201, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751340363692606, "dur": 1362741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363611788, "dur": 43936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363655734, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363656872, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657036, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657201, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657299, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363657526, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657656, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657790, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363657965, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363658202, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363658338, "dur": 2688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751340363661036, "dur": 1131, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751340363661035, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363662672, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363663201, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363663672, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363664167, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363664673, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363665951, "dur": 2624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Canvases\\CanvasControlScheme.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751340363665236, "dur": 3340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363669195, "dur": 1719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\Changelists\\MoveToChangelistMenuBuilder.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751340363668576, "dur": 2419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363670995, "dur": 1820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363672833, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363673122, "dur": 2690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363675814, "dur": 1721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363677536, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363677807, "dur": 1449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363679257, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363679555, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363679752, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363680167, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363680676, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363680803, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363681046, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363681797, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363682207, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363682379, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363682868, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363683031, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363684246, "dur": 2936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363687183, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363687740, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363688593, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363689129, "dur": 1128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363690257, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363690322, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363691324, "dur": 1262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751340363692587, "dur": 1362854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363611528, "dur": 44057, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363655591, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751340363657731, "dur": 687, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751340363658538, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751340363658759, "dur": 646, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751340363659414, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751340363660042, "dur": 12512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751340363672562, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363672830, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363672946, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363673118, "dur": 2732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363675853, "dur": 1962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363677816, "dur": 1506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363679328, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363679417, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363679770, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363680032, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363680178, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363680678, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363680802, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363681076, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363681779, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363682227, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363682362, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363682885, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363683037, "dur": 1185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363684222, "dur": 2984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363687206, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363687733, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363688592, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363689118, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363690314, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363691200, "dur": 1355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751340363692556, "dur": 1362741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363611573, "dur": 44043, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363655622, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363656574, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363656901, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363657050, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363657194, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363657322, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363657661, "dur": 775, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363658877, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751340363659127, "dur": 1931, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751340363661061, "dur": 4483, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\ProfileAnalyzerExportWindow.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751340363665959, "dur": 1694, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\BoxAndWhiskerPlot.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751340363661061, "dur": 6696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363667758, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363668409, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363668947, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363669482, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363670094, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363671913, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363672865, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363672995, "dur": 2820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363675815, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363677621, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363678077, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751340363680614, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363680790, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751340363681109, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751340363681949, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363682086, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363682196, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363682361, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363682916, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363683049, "dur": 1206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363684255, "dur": 2965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363687220, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363687725, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363687799, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363688569, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363689113, "dur": 1219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363690332, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363691276, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751340363692593, "dur": 1362801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363611635, "dur": 43992, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363655633, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363656570, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363656730, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363656980, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363657140, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363657266, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363657708, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363657931, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363658059, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751340363658517, "dur": 2479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751340363660999, "dur": 3798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Logging\\ResultsLogger.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751340363660999, "dur": 4358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363665358, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363666713, "dur": 1082, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\GraphDebugData.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751340363665955, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363667910, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363668523, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363669108, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363669928, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363671493, "dur": 1507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363673001, "dur": 2802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363675804, "dur": 2083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363677888, "dur": 1369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363679258, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363679414, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363679664, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363679963, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363680170, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363680353, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363680782, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363681089, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363681817, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363682167, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363682377, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363682899, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363683060, "dur": 1200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363684260, "dur": 2963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363687224, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363687772, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363688582, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363689122, "dur": 1208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363690330, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363691248, "dur": 1353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751340363692601, "dur": 1362759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363611728, "dur": 43908, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363655642, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363656605, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363657177, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363657315, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363657553, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363657671, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363657916, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363658041, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363658140, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363658220, "dur": 585, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363658814, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751340363659041, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751340363659153, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751340363659425, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751340363659562, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751340363659880, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751340363660004, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363660867, "dur": 1022, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\Drawers\\TrackItemsDrawer.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751340363660459, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363662136, "dur": 3307, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Units\\MissingTypeUnitWidget.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751340363661945, "dur": 3892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363665838, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363666711, "dur": 1117, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\Signals\\SignalEmitter.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751340363666424, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363668049, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363668629, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363669237, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363669801, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363670331, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363670933, "dur": 2138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363673072, "dur": 2733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363675807, "dur": 1742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363677550, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363677606, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363678060, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751340363679174, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363679377, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363679987, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751340363680420, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751340363681428, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363682088, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363682274, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363682406, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363682928, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363683055, "dur": 1237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363684293, "dur": 2909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363687203, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363687789, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363688605, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363689137, "dur": 1137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363690286, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363690354, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363691203, "dur": 1354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751340363692558, "dur": 1362962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363611836, "dur": 43909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363655750, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751340363657022, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751340363657181, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751340363657736, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751340363658288, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751340363658550, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363658699, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751340363658803, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751340363659205, "dur": 1690, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751340363660898, "dur": 1306, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\ClipInspector\\ClipInspectorSelectionInfo.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751340363662669, "dur": 2937, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Audio\\AudioTrackInspector.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751340363660898, "dur": 4709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363665608, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363666685, "dur": 944, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Bounds_DirectConverter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751340363667764, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsNullableConverter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751340363668332, "dur": 1595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsKeyValuePairConverter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751340363666230, "dur": 3697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363669928, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363670529, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363671602, "dur": 1293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363672896, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363673070, "dur": 2786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363675857, "dur": 1894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363677753, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751340363678153, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363678216, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751340363679862, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363679937, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363680118, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363680236, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363680618, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363680761, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363681061, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363681996, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363682237, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363682397, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363682878, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363683056, "dur": 1180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363684237, "dur": 2935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363687174, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363687731, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363688616, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363689134, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363690277, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363691198, "dur": 1347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363692546, "dur": 84359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751340363776906, "dur": 1278415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363611917, "dur": 43841, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363655763, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363656728, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363656806, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363657304, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363657888, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363658060, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363658183, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363658364, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751340363658701, "dur": 1278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751340363659983, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363661585, "dur": 1154, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\UI\\QuestionSection.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751340363661462, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363663097, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363663997, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnDrag.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751340363663566, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363664726, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363665207, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363665932, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363666709, "dur": 1089, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Activation\\ActivationMixerPlayable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751340363666543, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363668151, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363669403, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363670456, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363672018, "dur": 809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363672828, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363672999, "dur": 2809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363675809, "dur": 1717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363677527, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363677616, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363677902, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363678494, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751340363679786, "dur": 802, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363680654, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751340363680979, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751340363682196, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363682352, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363682926, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363683038, "dur": 1177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363684282, "dur": 2950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363687232, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363687741, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363688604, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363689143, "dur": 1103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363690306, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363691255, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751340363692581, "dur": 1362886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363610930, "dur": 44469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363655406, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751340363656778, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751340363657944, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751340363658182, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751340363658322, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751340363658641, "dur": 572, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751340363659221, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363659451, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751340363659610, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751340363659726, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751340363659904, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363661820, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Flow\\UnitBaseStateExtensions.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751340363661505, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363663218, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363664212, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363664669, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_0_3.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751340363666180, "dur": 1871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\Changelog_1_4_0.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751340363664669, "dur": 3795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363668464, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363669153, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Gluon\\Errors\\ErrorListViewItem.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751340363669041, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363670320, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363671128, "dur": 1910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363673039, "dur": 2821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363675861, "dur": 1696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363677557, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363678579, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363679382, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363679553, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363679666, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363680011, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363680104, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363680228, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363680625, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363680777, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363681097, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363681460, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363681773, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363682356, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363682905, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363683006, "dur": 1223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363684229, "dur": 2970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363687200, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363687746, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363688623, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363689141, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363690288, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363691253, "dur": 1341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751340363692594, "dur": 1362812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363610998, "dur": 44420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363655424, "dur": 1373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363656844, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363657211, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363657734, "dur": 1647, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363659395, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363659682, "dur": 13174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363672862, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363673069, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363673478, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363675586, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363675857, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363676191, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363677383, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363677602, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363678052, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363680864, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363681071, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363681380, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363681771, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363681840, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363684096, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363684276, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363684577, "dur": 2390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363686969, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363687219, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751340363687499, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751340363688428, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363688631, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363689132, "dur": 1121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363690288, "dur": 906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363691194, "dur": 1345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751340363692599, "dur": 1362784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363612012, "dur": 43797, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363655815, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363656725, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363656958, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363657094, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363657233, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363657579, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363657716, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363658114, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363658312, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363658397, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363658873, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363659057, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363659303, "dur": 483, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363659877, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751340363659992, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363660493, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363661499, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363662067, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363662657, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363663166, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363663658, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363664180, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363664650, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363665189, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363665806, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363666704, "dur": 906, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\IKeyedCollection.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751340363666354, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363667747, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363668263, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363668863, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363669470, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363670072, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363671681, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363672839, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363672997, "dur": 2783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363675782, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363677607, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363678085, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751340363679261, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363679957, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363680094, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363680260, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363680698, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363680792, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363681052, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363681383, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751340363681764, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751340363682732, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363682925, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363683023, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363684283, "dur": 2894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363687178, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363687777, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363688572, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363689116, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363690284, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363690346, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363691196, "dur": 1341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751340363692585, "dur": 1362834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363611037, "dur": 44396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363655441, "dur": 1274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363656781, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363656930, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363657069, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363657215, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363657655, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363657809, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363657947, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363658119, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363658298, "dur": 566, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751340363658868, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751340363659193, "dur": 1764, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1751340363660959, "dur": 4650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_EditorUtility.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751340363660959, "dur": 5170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363666312, "dur": 1501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsSerializer.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751340363666130, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363668189, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363669295, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363669932, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363671903, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363672850, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363673078, "dur": 2699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363675849, "dur": 1940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363677789, "dur": 1696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363679486, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363679677, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363680022, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363680107, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363680181, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363680654, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363680772, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363681060, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363681388, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751340363681796, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751340363682763, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363683046, "dur": 1203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363684250, "dur": 2942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363687193, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363687721, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363688573, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363689182, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363690287, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363691191, "dur": 1358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751340363692550, "dur": 1362981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751340365061495, "dur": 3263, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21056, "tid": 705, "ts": 1751340365090892, "dur": 3467, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21056, "tid": 705, "ts": 1751340365094415, "dur": 3960, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21056, "tid": 705, "ts": 1751340365082166, "dur": 17794, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}