{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17072, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17072, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17072, "tid": 1715, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17072, "tid": 1715, "ts": 1751286602668397, "dur": 1185, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602677347, "dur": 1688, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17072, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17072, "tid": 1, "ts": 1751286601215043, "dur": 7786, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751286601222832, "dur": 46430, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751286601269273, "dur": 58888, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602679043, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 17072, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601211670, "dur": 28446, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601240120, "dur": 1406105, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601241804, "dur": 5882, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601247696, "dur": 2896, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601250598, "dur": 588, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251202, "dur": 55, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251261, "dur": 170, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251438, "dur": 5, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251444, "dur": 95, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251544, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251549, "dur": 94, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251655, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251768, "dur": 4, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251775, "dur": 101, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251888, "dur": 5, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601251897, "dur": 111, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252015, "dur": 4, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252023, "dur": 76, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252107, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252112, "dur": 103, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252223, "dur": 4, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252230, "dur": 102, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252340, "dur": 5, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252347, "dur": 94, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252448, "dur": 4, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252455, "dur": 97, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252563, "dur": 4, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252570, "dur": 96, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252674, "dur": 4, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252680, "dur": 96, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252785, "dur": 4, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252792, "dur": 82, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252882, "dur": 3, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252888, "dur": 101, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601252996, "dur": 4, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253003, "dur": 108, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253118, "dur": 4, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253125, "dur": 96, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253231, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253239, "dur": 104, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253350, "dur": 4, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253358, "dur": 94, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253460, "dur": 4, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253467, "dur": 96, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253574, "dur": 4, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253584, "dur": 89, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253681, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253694, "dur": 100, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253801, "dur": 4, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253808, "dur": 102, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253917, "dur": 5, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601253925, "dur": 95, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254028, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254039, "dur": 109, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254155, "dur": 4, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254162, "dur": 93, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254262, "dur": 4, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254268, "dur": 100, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254379, "dur": 4, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254385, "dur": 89, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254482, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254487, "dur": 104, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254599, "dur": 4, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254605, "dur": 103, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254717, "dur": 5, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254725, "dur": 92, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254823, "dur": 4, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254830, "dur": 97, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254935, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601254941, "dur": 123, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255071, "dur": 4, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255078, "dur": 98, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255184, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255191, "dur": 112, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255310, "dur": 4, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255317, "dur": 106, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255430, "dur": 5, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255438, "dur": 101, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255546, "dur": 10, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255635, "dur": 164, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255807, "dur": 6, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255816, "dur": 142, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255967, "dur": 6, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601255976, "dur": 165, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256151, "dur": 5, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256159, "dur": 147, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256314, "dur": 6, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256324, "dur": 364, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256700, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256705, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256868, "dur": 5, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601256877, "dur": 154, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257040, "dur": 6, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257049, "dur": 161, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257219, "dur": 5, "ph": "X", "name": "ProcessMessages 1256", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257227, "dur": 147, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257382, "dur": 6, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257391, "dur": 139, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257539, "dur": 4, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257546, "dur": 157, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257712, "dur": 7, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257733, "dur": 153, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257895, "dur": 5, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601257903, "dur": 155, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258068, "dur": 5, "ph": "X", "name": "ProcessMessages 1236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258076, "dur": 137, "ph": "X", "name": "ReadAsync 1236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258222, "dur": 5, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258236, "dur": 159, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258404, "dur": 5, "ph": "X", "name": "ProcessMessages 1258", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258412, "dur": 154, "ph": "X", "name": "ReadAsync 1258", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258575, "dur": 6, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258584, "dur": 159, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258752, "dur": 5, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258761, "dur": 147, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258916, "dur": 6, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601258925, "dur": 162, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259096, "dur": 6, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259105, "dur": 147, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259261, "dur": 5, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259269, "dur": 155, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259433, "dur": 6, "ph": "X", "name": "ProcessMessages 1343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259441, "dur": 152, "ph": "X", "name": "ReadAsync 1343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259602, "dur": 5, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259610, "dur": 144, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259762, "dur": 5, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259770, "dur": 163, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259940, "dur": 5, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601259948, "dur": 88, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260043, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260049, "dur": 126, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260184, "dur": 4, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260191, "dur": 139, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260339, "dur": 6, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260348, "dur": 103, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260459, "dur": 4, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260466, "dur": 120, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260591, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260595, "dur": 130, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260733, "dur": 5, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260741, "dur": 108, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260860, "dur": 4, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260867, "dur": 93, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260964, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601260968, "dur": 88, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261064, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261070, "dur": 86, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261165, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261171, "dur": 62, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261237, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261307, "dur": 71, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261386, "dur": 5, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261393, "dur": 85, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261486, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261493, "dur": 92, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261589, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261592, "dur": 75, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261676, "dur": 3, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261682, "dur": 102, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261793, "dur": 4, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261801, "dur": 81, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261923, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601261926, "dur": 72, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262006, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262014, "dur": 81, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262103, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262109, "dur": 82, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262195, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262198, "dur": 67, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262273, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262278, "dur": 95, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262378, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262382, "dur": 56, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262442, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262445, "dur": 70, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262522, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262527, "dur": 67, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262598, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262601, "dur": 56, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262661, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262664, "dur": 68, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262739, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262745, "dur": 66, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262815, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262817, "dur": 56, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262878, "dur": 4, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262884, "dur": 67, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262959, "dur": 3, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601262964, "dur": 76, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263045, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263048, "dur": 56, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263108, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263111, "dur": 68, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263186, "dur": 3, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263192, "dur": 85, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263281, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263287, "dur": 55, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263346, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263350, "dur": 74, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263435, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263514, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263517, "dur": 55, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263576, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263579, "dur": 68, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263654, "dur": 3, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263659, "dur": 79, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263742, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263745, "dur": 53, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263802, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263805, "dur": 70, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263882, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263887, "dur": 71, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263963, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601263967, "dur": 57, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264027, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264030, "dur": 69, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264109, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264114, "dur": 88, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264207, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264210, "dur": 54, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264269, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264272, "dur": 67, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264346, "dur": 3, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264351, "dur": 78, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264436, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264439, "dur": 55, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264498, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264501, "dur": 69, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264577, "dur": 3, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264583, "dur": 81, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264669, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264672, "dur": 56, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264732, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264735, "dur": 71, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264813, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264819, "dur": 78, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264901, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264905, "dur": 54, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264962, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601264965, "dur": 72, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265044, "dur": 3, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265049, "dur": 75, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265129, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265132, "dur": 54, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265190, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265193, "dur": 68, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265268, "dur": 3, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265274, "dur": 78, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265356, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265360, "dur": 54, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265418, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265421, "dur": 63, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265492, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265496, "dur": 74, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265574, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265577, "dur": 56, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265638, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265641, "dur": 71, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265719, "dur": 3, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265724, "dur": 99, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265831, "dur": 3, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265837, "dur": 108, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265953, "dur": 3, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601265959, "dur": 91, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266058, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266064, "dur": 82, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266153, "dur": 6, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266163, "dur": 89, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266259, "dur": 3, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266265, "dur": 91, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266363, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266368, "dur": 147, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266523, "dur": 4, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266530, "dur": 81, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266618, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266624, "dur": 82, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266713, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266717, "dur": 86, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266810, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266816, "dur": 89, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266912, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601266917, "dur": 85, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267010, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267015, "dur": 74, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267096, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267101, "dur": 97, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267206, "dur": 3, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267211, "dur": 86, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267304, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267310, "dur": 83, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267400, "dur": 3, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267407, "dur": 76, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267490, "dur": 3, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267496, "dur": 86, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267589, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267594, "dur": 83, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267687, "dur": 3, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267693, "dur": 83, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267783, "dur": 3, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267789, "dur": 86, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267882, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267888, "dur": 85, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267980, "dur": 3, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601267986, "dur": 83, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268076, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268082, "dur": 85, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268173, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268179, "dur": 91, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268277, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268282, "dur": 85, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268375, "dur": 3, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268380, "dur": 80, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268468, "dur": 3, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268473, "dur": 68, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268552, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268646, "dur": 3, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268651, "dur": 88, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268747, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268752, "dur": 86, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268845, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268851, "dur": 82, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268941, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601268946, "dur": 88, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269041, "dur": 3, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269047, "dur": 89, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269143, "dur": 3, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269149, "dur": 82, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269238, "dur": 3, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269244, "dur": 91, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269341, "dur": 3, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269347, "dur": 89, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269443, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269449, "dur": 82, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269538, "dur": 2, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269543, "dur": 89, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269639, "dur": 3, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269644, "dur": 84, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269736, "dur": 3, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269741, "dur": 87, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269836, "dur": 3, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269842, "dur": 83, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269932, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601269937, "dur": 84, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270028, "dur": 4, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270034, "dur": 95, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270136, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270142, "dur": 82, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270232, "dur": 3, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270237, "dur": 91, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270335, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270341, "dur": 87, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270436, "dur": 4, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270443, "dur": 104, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270554, "dur": 4, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270562, "dur": 94, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270664, "dur": 4, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270670, "dur": 136, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270815, "dur": 4, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270822, "dur": 154, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270985, "dur": 5, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601270994, "dur": 130, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271132, "dur": 4, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271139, "dur": 151, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271299, "dur": 6, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271308, "dur": 144, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271461, "dur": 5, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271469, "dur": 133, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271610, "dur": 4, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601271617, "dur": 424, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272052, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272060, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272146, "dur": 553, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272704, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272805, "dur": 9, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272815, "dur": 53, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272872, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272875, "dur": 51, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272930, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272933, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601272998, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273001, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273085, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273091, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273174, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273179, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273244, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273249, "dur": 83, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273343, "dur": 7, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273354, "dur": 64, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273423, "dur": 6, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273432, "dur": 98, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273541, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273551, "dur": 129, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273699, "dur": 10, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273714, "dur": 111, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273832, "dur": 6, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273840, "dur": 68, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273913, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273917, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273991, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601273997, "dur": 82, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274084, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274088, "dur": 81, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274175, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274184, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274245, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274249, "dur": 73, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274336, "dur": 6, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274346, "dur": 87, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274441, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274450, "dur": 72, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274529, "dur": 9, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274542, "dur": 55, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274602, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274609, "dur": 85, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274700, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274709, "dur": 79, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274795, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274802, "dur": 105, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274921, "dur": 13, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601274941, "dur": 119, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275068, "dur": 6, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275080, "dur": 74, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275160, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275167, "dur": 72, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275247, "dur": 5, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275254, "dur": 78, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275337, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275340, "dur": 45, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275392, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275448, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275451, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275505, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275509, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275569, "dur": 2, "ph": "X", "name": "ProcessMessages 45", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275603, "dur": 51, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601275659, "dur": 342, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601276007, "dur": 73, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601276086, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601276089, "dur": 11053, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287152, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287220, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287271, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287275, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287340, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287393, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287396, "dur": 152, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287556, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601287610, "dur": 1090, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288708, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288713, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288768, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288820, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288823, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601288981, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289028, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289031, "dur": 631, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289669, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289672, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289740, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289811, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601289816, "dur": 346, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290170, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290220, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290223, "dur": 105, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290335, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290387, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290390, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290438, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601290442, "dur": 3463, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601293914, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601293918, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294038, "dur": 20, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294061, "dur": 64, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294130, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294134, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294192, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294196, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294243, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294246, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294303, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294308, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294369, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294426, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294429, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294480, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294483, "dur": 368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294858, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294909, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601294913, "dur": 505, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295425, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295428, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295492, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295495, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295545, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295548, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295702, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295764, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295810, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601295813, "dur": 1567, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297389, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297452, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297455, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297511, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297514, "dur": 406, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601297930, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298001, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298004, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298053, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298056, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298239, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298311, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298314, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298389, "dur": 565, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601298965, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299028, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299083, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299087, "dur": 807, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299904, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299956, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601299958, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300050, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300053, "dur": 416, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300478, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300552, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601300555, "dur": 528, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301090, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301145, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301149, "dur": 572, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301729, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301782, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601301786, "dur": 437, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601302228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601302235, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601302309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601302315, "dur": 750, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303073, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303182, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303185, "dur": 474, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303664, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601303674, "dur": 856, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601304535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601304538, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601304593, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601304596, "dur": 2198, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601306863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601306867, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601311350, "dur": 919, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601312274, "dur": 520239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601832528, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601832538, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601832643, "dur": 2481, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601835129, "dur": 15552, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601850689, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601850693, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601850774, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601850782, "dur": 2862, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601853648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601853651, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601853713, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286601853734, "dur": 770599, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602624344, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602624351, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602624494, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602624502, "dur": 1241, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602625752, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602625757, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602625862, "dur": 49, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602625918, "dur": 2411, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602628337, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602628342, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602628423, "dur": 926, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286602629356, "dur": 16485, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602679064, "dur": 1901, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286601206737, "dur": 121500, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286601328240, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286601328249, "dur": 2193, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602680968, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17072, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286601176886, "dur": 1472455, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286601184968, "dur": 11391, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286602649869, "dur": 11990, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286602657420, "dur": 69, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286602662171, "dur": 35, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602680983, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751286601235695, "dur": 2229, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286601237940, "dur": 654, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286601238664, "dur": 74, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751286601238739, "dur": 1106, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286601242173, "dur": 3549, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601247789, "dur": 3776, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601251742, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252122, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252248, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252352, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252572, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252708, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601252933, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253054, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253155, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253360, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253463, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253720, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253840, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601253943, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254053, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254149, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254289, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254501, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254632, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254731, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254846, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_39D3266F6029657A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601254945, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601255079, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601255308, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601255409, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601255652, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601255790, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601256093, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601256317, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_514B12BC0D1D8DB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601256438, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601257069, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601257175, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601257514, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601257851, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601258017, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601258230, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601258368, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601258687, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601258924, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601259045, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601259409, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601259611, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_8B44F821019897E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601259734, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601259903, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260096, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260226, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260426, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_C6D619E6129B87D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260517, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260662, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260838, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_D0BA3615D322EF6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601260932, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261070, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261203, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_17F688653313AA4C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261348, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261423, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261507, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261648, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261811, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_EE1755C0672F8EE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261876, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601261954, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262067, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262138, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262247, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262358, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262506, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_6CA31C4FA5286AB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262571, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262683, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262746, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262862, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601262968, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263046, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263189, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0FE027DDD6F41767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263265, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263339, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263409, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263510, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263580, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263652, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263826, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263893, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601263990, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264057, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264117, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264224, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264285, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264380, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264440, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264508, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264571, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264677, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264744, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264811, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264916, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601264980, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265041, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265156, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265217, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265276, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265380, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265446, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265509, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265613, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265673, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265732, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265869, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601265937, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266034, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266102, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266173, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266278, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266376, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266438, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266548, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266622, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266729, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266829, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601266895, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267070, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267141, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267239, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267343, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267475, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267546, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267658, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267760, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601267922, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268019, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268115, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268214, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268278, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268349, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268448, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268548, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268648, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268749, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601268945, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269011, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269110, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269210, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269308, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269387, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269492, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269589, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269686, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269782, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269879, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601269977, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270076, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270175, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270271, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270350, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270409, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270504, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270607, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270685, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270814, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601270911, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271023, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271133, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271278, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271448, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271644, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601271778, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286601239886, "dur": 32056, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286601271957, "dur": 1354364, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602626326, "dur": 1063, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602627392, "dur": 107, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602627506, "dur": 294, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602627819, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602627891, "dur": 58, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602627955, "dur": 53, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602628450, "dur": 216, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602628711, "dur": 2565, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751286601241156, "dur": 31124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601272284, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601272460, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601272649, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601272879, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601272952, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601273106, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601273195, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601273395, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601273780, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601273843, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601274423, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751286601274758, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286601275147, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751286601275398, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286601275584, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601275649, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286601276453, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Extensions\\AnimationTrackExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751286601276006, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601277542, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601278679, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601279996, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601281728, "dur": 1058, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsReflectionUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751286601281085, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601282949, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601283711, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601284824, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601285816, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601286670, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601287612, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601287842, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601289155, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601290152, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601290291, "dur": 1357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601291690, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292031, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292269, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292404, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292523, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292749, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601292862, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286601293333, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751286601294503, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601294629, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601294807, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601295169, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601295869, "dur": 1977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601297846, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601298441, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601299413, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601300360, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601301371, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601302098, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601303465, "dur": 1374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286601304840, "dur": 1321453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601240989, "dur": 31226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601272220, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601272727, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601272797, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601272972, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601273149, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601273201, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601273440, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601273776, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601273868, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274035, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274273, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274428, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274658, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274861, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751286601274949, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751286601275108, "dur": 420, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751286601275535, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601275603, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751286601276425, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\KeyTraverser.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751286601275846, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601277423, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601278490, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601279705, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601281731, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\InputAction_DirectConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751286601281201, "dur": 2309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601283511, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601284765, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601285870, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601286765, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601287648, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601287746, "dur": 1392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601289139, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601290165, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601290299, "dur": 1339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601291692, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292079, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292305, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292407, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292524, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292753, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601292856, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601293278, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751286601294395, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601294511, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601294625, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601294794, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601295150, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601295888, "dur": 1943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601297876, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601298405, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601299416, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601300398, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286601300632, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751286601301395, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601302107, "dur": 1350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601303458, "dur": 1384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286601304843, "dur": 1321474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601241016, "dur": 31226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601272249, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601272477, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601272543, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601272615, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601272799, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601272878, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601273083, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601273269, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601273367, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601273648, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601273835, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601273975, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601274138, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601274190, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601274340, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751286601274485, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751286601274731, "dur": 569, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751286601275365, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751286601275561, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751286601275699, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751286601276464, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\PropertyDrawers\\GlyphPairAdjustmentRecordPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751286601276118, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601277592, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601278841, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601279929, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601280805, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601281744, "dur": 1085, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_SpriteAnimator.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751286601281572, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601283540, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601284435, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601285205, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601286031, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601286212, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601286285, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601286345, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601287615, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601287823, "dur": 1314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601289137, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601290140, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601290277, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286601290710, "dur": 1002, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601291720, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751286601292632, "dur": 950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601293662, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601294039, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601294417, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601294566, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601294825, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601295175, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601295932, "dur": 1937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601297869, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601298401, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601299422, "dur": 992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601300415, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601301401, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601302127, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601303456, "dur": 1381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286601304838, "dur": 1321463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601240654, "dur": 31462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601272121, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601272532, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601272704, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601272803, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601273173, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601273705, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601273892, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601274056, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601274202, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601274253, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751286601274510, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286601274619, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286601274745, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601274956, "dur": 418, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286601275376, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286601275488, "dur": 447, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286601276433, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\TimelineClipGroup.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751286601275937, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601277344, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601278424, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601280032, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601280910, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601281745, "dur": 1065, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\CoverageSession.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751286601281613, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601283415, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601284342, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601285189, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601286023, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601287161, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601287609, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601287739, "dur": 1421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601289161, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601290130, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601290289, "dur": 1352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601291649, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601291741, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292029, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292266, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292458, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292527, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292750, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292815, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601292865, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601293116, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601293643, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601294054, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601294405, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601294516, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601294834, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601295177, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601295882, "dur": 1953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601297835, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601298411, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601299426, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601300375, "dur": 987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601301363, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601302154, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601303493, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286601303754, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286601304837, "dur": 1321469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601240415, "dur": 31625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601272048, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601272745, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601273065, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601273147, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601273380, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601273711, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601273851, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601273997, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601274122, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601274236, "dur": 543, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601274787, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751286601274942, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751286601275178, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601275327, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751286601275396, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751286601275562, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751286601276466, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\TimelineWindow.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751286601275810, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601277200, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601278201, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601278961, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601279622, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601280512, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601281736, "dur": 1060, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751286601281335, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601283115, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601283967, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601284724, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601285374, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601286971, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601287613, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601287851, "dur": 1289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601289140, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601290122, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601290250, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601290818, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751286601291911, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601292072, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286601292480, "dur": 1773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751286601294254, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601294823, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601295160, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601295880, "dur": 1972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601297853, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601298416, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601299429, "dur": 951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601300381, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601301368, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601302122, "dur": 1356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601303478, "dur": 1391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286601304869, "dur": 1321483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601240540, "dur": 31549, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601272097, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286601272740, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601273128, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286601273263, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286601273550, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286601273794, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601273964, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751286601274209, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601274360, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286601274635, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601274697, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286601274946, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751286601275351, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286601275460, "dur": 675, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286601276436, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Events\\SessionMode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751286601276136, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601277622, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601278397, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601279121, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601280042, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601280851, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601281735, "dur": 1075, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Common\\IOnboardingSectionAnalyticsProvider.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751286601281633, "dur": 2118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601283752, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601284527, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601285384, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601287016, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601287630, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601287794, "dur": 1370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601289165, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601290135, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601290312, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601291727, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292038, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292271, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292408, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292521, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292751, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601292869, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601293117, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601293190, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601293626, "dur": 985, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601294621, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601294814, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601295182, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601295911, "dur": 1930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601297842, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601298402, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601299407, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601300399, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601301379, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601302124, "dur": 1337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601303462, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286601304861, "dur": 1321462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601240622, "dur": 31482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601272108, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601272400, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601272512, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601272847, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601272983, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601273091, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601273317, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601273644, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601273704, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601273840, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601273992, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601274055, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286601274566, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601274716, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751286601274936, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751286601275414, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286601275556, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601275619, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286601275671, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286601276457, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\StyleNormalColorOverride.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751286601277089, "dur": 1274, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\Scopes\\PropertyScope.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751286601275827, "dur": 2599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601278426, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601279172, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601279924, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601281727, "dur": 1078, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Decorators\\IDecoratorAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751286601281221, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601283207, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601284203, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601285344, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601286673, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601287618, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601287813, "dur": 1358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601289171, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601290138, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601290286, "dur": 1368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601291654, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601291714, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601292046, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601292311, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601292451, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601292562, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601292868, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601293122, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601293649, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601294059, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601294408, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601294513, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601294784, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601295163, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601295893, "dur": 1961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601297854, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601298429, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601299418, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601300363, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601301361, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601302101, "dur": 1359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601303460, "dur": 1388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286601304849, "dur": 1321507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601240738, "dur": 31396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601272141, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601272718, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601272914, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601273092, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601273183, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601273344, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601273574, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601273777, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601273897, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601273996, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601274130, "dur": 429, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286601274592, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601274730, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601274798, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751286601275282, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751286601275422, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751286601275655, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751286601276081, "dur": 1675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Items\\ItemsGroup.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751286601275968, "dur": 2318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601278286, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601279068, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601279795, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601281715, "dur": 1153, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Attributes\\VisualScriptingHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751286601281229, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601283061, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601283862, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601284634, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601285456, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601287242, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601287602, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601287741, "dur": 1391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601289197, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601290127, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601290288, "dur": 1364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601291674, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292028, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292263, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292411, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292524, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292594, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292759, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601292864, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601293119, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601293637, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601293794, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601294079, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601294410, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601294508, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601294598, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601294788, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601295152, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601295908, "dur": 1941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601297849, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601298397, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601299431, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601300378, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601301347, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601302121, "dur": 1352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601303473, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286601304846, "dur": 1321480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601240857, "dur": 31324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601272189, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601272656, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601272819, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601273023, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601273126, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601273346, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601273675, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601273848, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601274042, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601274211, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286601274474, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286601274795, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286601275211, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601275360, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286601275492, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601275546, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286601276460, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Cli.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751286601275731, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601277350, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601278461, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601279484, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601280307, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601281702, "dur": 1048, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\TypeIconPriorityAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751286601281067, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601283301, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601284437, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601285757, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601286798, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601287598, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601287751, "dur": 1426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601289178, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601290157, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601290277, "dur": 1369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601291689, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292026, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292264, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292401, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292535, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292756, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601292861, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286601293322, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751286601294307, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601294418, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751286601294601, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601294806, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601295199, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601295877, "dur": 1980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601297857, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601298422, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601299430, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601300371, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601301375, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601302104, "dur": 1348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601303453, "dur": 1398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286601304851, "dur": 1321446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601240292, "dur": 31683, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601272003, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601272573, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601272766, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601273014, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601273164, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601273629, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601273886, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601273995, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286601274312, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286601274550, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601274610, "dur": 875, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751286601275489, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286601275585, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601275669, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286601276065, "dur": 1559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_UIStyleManager.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751286601276065, "dur": 2237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601278303, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601279255, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601280362, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601281717, "dur": 1085, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Animation\\AnimationPlayableAsset.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751286601281571, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601283741, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601284861, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601286036, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601286255, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601286357, "dur": 1239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601287596, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601287749, "dur": 1419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601289168, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601290153, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601290284, "dur": 1359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601291644, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601291736, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601292036, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601292267, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601292402, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601292532, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601292866, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601293124, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601293647, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601294051, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601294420, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601294527, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601294800, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601295162, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601295865, "dur": 1993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601297858, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601298407, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601299414, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601300404, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601301359, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601302123, "dur": 1346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601303469, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286601304855, "dur": 1321483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601241154, "dur": 31108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601272270, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601272460, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601272583, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601272780, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601272998, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601273201, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601273276, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601273573, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601273660, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601273857, "dur": 741, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601274626, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601275137, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601275227, "dur": 12394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601287622, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601287793, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601287980, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601289192, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601289410, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601290238, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601290569, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601292637, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601292770, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601292856, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601293202, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601295722, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601295900, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601296208, "dur": 2075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601298284, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601298436, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601298760, "dur": 1459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601300220, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601300393, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601300710, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601301961, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601302131, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601302443, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601303485, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286601303829, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601304825, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601306644, "dur": 134, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286601307806, "dur": 525141, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286601850702, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751286601850670, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751286601851131, "dur": 2964, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751286601854101, "dur": 772249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601241237, "dur": 31054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601272296, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601272407, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601272469, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601272642, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601272760, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601272900, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601273088, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601273178, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601273434, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601273699, "dur": 585, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601274333, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751286601274635, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601274705, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751286601275178, "dur": 855, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751286601276462, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Attributes\\MenuEntryAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751286601276037, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601277743, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601279001, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601280183, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601280955, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601281725, "dur": 1046, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\Observables\\Observable.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751286601281683, "dur": 2245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601283929, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601285135, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601286030, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601286529, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601287604, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601287743, "dur": 1426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601289170, "dur": 968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601290139, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601290271, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601290827, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751286601292080, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601292365, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601292429, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601292537, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601292769, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601292876, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601293117, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601293633, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601294053, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601294403, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601294517, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601294796, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601295173, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601295906, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286601296129, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751286601297883, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601298415, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601299440, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601300362, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601300427, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601301349, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601302106, "dur": 1360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601303467, "dur": 1380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286601304847, "dur": 1321457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601241349, "dur": 30965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601272322, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601272539, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601272666, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601273014, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601273091, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601273337, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601273672, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601273791, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601274073, "dur": 388, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751286601274512, "dur": 807, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751286601275378, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751286601275529, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751286601275654, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751286601276431, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\AddDelete\\IAddDeleteItemMode.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751286601275953, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601277088, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601278320, "dur": 1161, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\NesterUnitEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751286601277723, "dur": 2034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601281113, "dur": 1828, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\PackageEventListener.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751286601279758, "dur": 3184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601282943, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601284179, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601285318, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601286506, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601287615, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601287860, "dur": 1314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601289175, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601290168, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601290252, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286601290564, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751286601291461, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601291697, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601292030, "dur": 2270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601294309, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601294413, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601294509, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601294728, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601294832, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601295174, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601295879, "dur": 1957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601297837, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601298444, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601299408, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601300376, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601301389, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601302119, "dur": 1355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601303475, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286601304841, "dur": 1321472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601241460, "dur": 30869, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601272333, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601272437, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601272528, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601272762, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601272842, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601273029, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601273113, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601273280, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601273372, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601273700, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601273839, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274000, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274065, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601274175, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274506, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274594, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274830, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286601274971, "dur": 808, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751286601276437, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\ViewModel\\TimelineAssetViewModel.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751286601275782, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601277458, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601278743, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601279991, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601281699, "dur": 1033, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsObjectAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751286601281198, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601283360, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601284570, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601285764, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601286603, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601287595, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601287752, "dur": 1401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601289154, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601290117, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601290243, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601290854, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751286601292278, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601292434, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286601292777, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751286601293828, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601294043, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601294122, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601294414, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601294554, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601294850, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601295167, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601295891, "dur": 1963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601297855, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601298404, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601299419, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601300373, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601301384, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601302109, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601303475, "dur": 1351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286601304892, "dur": 1321491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601241588, "dur": 30762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601272357, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601272565, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601272750, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601272936, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601273027, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601273191, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601273257, "dur": 306, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601273572, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601273741, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601273819, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601273965, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601274025, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601274115, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601274287, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601274479, "dur": 760, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751286601275319, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286601275651, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286601275742, "dur": 739, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286601276483, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751286601276483, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601277959, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601279283, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601280412, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601281712, "dur": 1119, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751286601281606, "dur": 2343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601283950, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601285175, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601286336, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601286402, "dur": 1197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601287600, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601287742, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601289177, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601290123, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601290240, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601290768, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751286601292135, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601292300, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601292406, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601292536, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601292778, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601292881, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601293125, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601293636, "dur": 773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601294410, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751286601294520, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601294798, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601295164, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601295887, "dur": 1976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601297864, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601298440, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286601298665, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751286601299455, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601300397, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601301366, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601302111, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601303455, "dur": 1392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286601304848, "dur": 1321487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601241681, "dur": 30693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601272382, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601272643, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601272728, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601272910, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601273007, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601273202, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601273488, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601273798, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601273986, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601274128, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601274318, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601274435, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601274487, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286601274729, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601274820, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286601274969, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286601275105, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601275167, "dur": 549, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751286601275718, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601276454, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageRunData.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751286601276326, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601277581, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601278749, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601280033, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601281678, "dur": 1153, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\ISet.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751286601281223, "dur": 2283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601283507, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601284635, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601285795, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601286697, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601287607, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601287773, "dur": 1384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601289158, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601290120, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601290268, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286601290585, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601290790, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751286601291689, "dur": 1082, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601292832, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601293197, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601293635, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601294056, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601294454, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751286601294514, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601294792, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601295158, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601295933, "dur": 1900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601297888, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601298414, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601299424, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601300369, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601301355, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601302113, "dur": 1336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601303502, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601304831, "dur": 26065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601333042, "dur": 422, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1751286601330897, "dur": 2571, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286601333469, "dur": 1292872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601241807, "dur": 30588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601272402, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601272617, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601272790, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601272939, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601273093, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601273222, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601273514, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601273849, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601273907, "dur": 570, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601274632, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601274688, "dur": 520, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751286601275251, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601275362, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601275415, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751286601275693, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601276456, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_BaseEditorPanel.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751286601276109, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601277655, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601278338, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601279342, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601280251, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601281693, "dur": 1087, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_FontFeaturesCommon.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751286601281589, "dur": 2264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601283854, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601285145, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601286026, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601287121, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601287640, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601287778, "dur": 1395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601289173, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601290119, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601290258, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601290603, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751286601291521, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601291683, "dur": 1110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601292814, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286601293151, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751286601294188, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601294405, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601294519, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601294785, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601295154, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601295867, "dur": 1992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601297859, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601298418, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601299428, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601300383, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601301356, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601302145, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601303459, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601304832, "dur": 28667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286601333499, "dur": 1292911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601241879, "dur": 30532, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601272417, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601272732, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601272977, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601273107, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601273329, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601273606, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601273664, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601273738, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286601273903, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601274061, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601274184, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751286601274406, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751286601274941, "dur": 655, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751286601275681, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751286601275790, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601276449, "dur": 794, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ItemGui\\TimelineMarkerClusterGUI.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751286601275866, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601277753, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601279035, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601280175, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601281741, "dur": 1081, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\ClipCaps.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751286601281372, "dur": 2154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601283527, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601284712, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601286010, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601286923, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601287641, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601287767, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601289172, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601290136, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601290276, "dur": 1381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601291700, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292040, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292258, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292405, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292533, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292771, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601292880, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601293112, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601293653, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601294040, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601294416, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601294524, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601294807, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601295187, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601295871, "dur": 1972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601297843, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601298464, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601299465, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601300382, "dur": 987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601301369, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601302099, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601303465, "dur": 1388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286601304853, "dur": 1321513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601241951, "dur": 30474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601272429, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601272547, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601272638, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601272736, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601272803, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601272932, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601273059, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601273174, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601273249, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601273342, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601273639, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601273822, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601273976, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601274132, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286601274277, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601274409, "dur": 1640, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751286601276464, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\Invoker.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751286601276051, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601277321, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601278859, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601279926, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601281713, "dur": 1114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\HashSetPool.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751286601280781, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601282874, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601283625, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601284343, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601285180, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601285932, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601286601, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601287592, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601287780, "dur": 1379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601289160, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601290141, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601290283, "dur": 1367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601291687, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292034, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292261, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292409, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292526, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292754, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601292872, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601293120, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601293648, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601294057, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601294446, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1751286601294529, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601294802, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601295214, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601295895, "dur": 1949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601297845, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601298399, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601299431, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601300361, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601301345, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601302116, "dur": 1360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601303477, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286601304850, "dur": 1321494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601242025, "dur": 30420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601272454, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601272756, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601272927, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601273114, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601273187, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601273470, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601273776, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601273851, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601274164, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601274327, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601274427, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286601274697, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601274792, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286601275244, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601275315, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286601275613, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601276397, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\VisualStudioForMacInstallation.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286601276970, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\VisualStudioEditor.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286601277524, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\VisualStudioCodeInstallation.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286601275702, "dur": 2589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601279021, "dur": 2574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Time\\WaitForNextFrameUnit.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286601281707, "dur": 1048, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Time\\Timer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286601278293, "dur": 5026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601283320, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601284490, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601285609, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601287189, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601287610, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601287833, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601289163, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601290128, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601290238, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601290789, "dur": 1119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601291916, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751286601292833, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601293683, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286601294153, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751286601295209, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601295885, "dur": 1962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601297848, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601298426, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601299416, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601300366, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601301364, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601302117, "dur": 1354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601303472, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286601304844, "dur": 1321485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601242100, "dur": 30361, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601272466, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601272618, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601272735, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601272886, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601273033, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601273141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601273191, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601273388, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601273757, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601273938, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751286601274056, "dur": 592, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751286601274666, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601274731, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751286601274993, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601275054, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751286601275417, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751286601275595, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751286601276439, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Signals\\SignalEmitterInspector.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751286601275914, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601277322, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601278162, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601278914, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601279736, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601281719, "dur": 1078, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\Extensions\\XComparable.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751286601281011, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601283017, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601283848, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601284636, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601285516, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601287081, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601287593, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601287745, "dur": 1396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601289151, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601290133, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601290271, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601290834, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751286601292903, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601293163, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286601293580, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751286601294619, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601294706, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601294804, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601295156, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601295875, "dur": 1976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601297851, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601298446, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601299411, "dur": 968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601300379, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601301365, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601302115, "dur": 1355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601303470, "dur": 1382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286601304852, "dur": 1321457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601242140, "dur": 30335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601272480, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601272624, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601272785, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601272964, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601273047, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601273215, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601273295, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601273610, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601273694, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601273868, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601273949, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286601274226, "dur": 580, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751286601274813, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601274932, "dur": 613, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751286601275578, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601275692, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751286601275783, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601276398, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\TrackPropertyCurvesDataSource.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751286601277560, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\TimelineDataSource.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751286601275856, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601278185, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601279799, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601280547, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601281716, "dur": 1335, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\Signals\\SignalEmitter.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751286601281358, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601283627, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601285270, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601286458, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601287605, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601287748, "dur": 1387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601289211, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601290125, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601290294, "dur": 1432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601291726, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292033, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292259, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292403, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292529, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292757, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292816, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601292873, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601293167, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601293669, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601294042, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601294146, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601294411, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601294581, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601294827, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601295171, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601295910, "dur": 1951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601297862, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601298409, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601299421, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601300364, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601301357, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601302138, "dur": 1308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601303455, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601303513, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286601304835, "dur": 1321497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601242260, "dur": 30236, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601272504, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601272739, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601272968, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601273138, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601273201, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601273499, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601273804, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601273863, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601274023, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601274129, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601274197, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601274369, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601274422, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751286601274768, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751286601274876, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751286601275121, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601275177, "dur": 705, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751286601276416, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\TimelineUtility.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751286601275885, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601277483, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601278623, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601279767, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601281737, "dur": 1087, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerStayMListener.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751286601280845, "dur": 2338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601283185, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601284372, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601285576, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601287268, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601287616, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601287803, "dur": 1349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601289152, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601290143, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601290285, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286601290848, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751286601292026, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601292526, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601292601, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601292761, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601292870, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601293114, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601293636, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601294052, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601294418, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601294534, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601294819, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601295170, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601295884, "dur": 1954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601297839, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601298417, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601299458, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601300405, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601301352, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601302125, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601303468, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601304834, "dur": 545864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286601850734, "dur": 773903, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1751286601850701, "dur": 773940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1751286602624688, "dur": 1491, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 24, "ts": 1751286601242414, "dur": 30098, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601272514, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601272649, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601272831, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601273004, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601273071, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601273269, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601273746, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601273881, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601274029, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601274132, "dur": 1061, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601275205, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286601275498, "dur": 11961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751286601287461, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601287661, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601287757, "dur": 1409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601289167, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601290161, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601290281, "dur": 1377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601291733, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601292045, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601292270, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601292418, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601292530, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601292863, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601293123, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601293638, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601294055, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601294407, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601294511, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601294786, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601295166, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601295921, "dur": 1918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601297840, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601298450, "dur": 975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601299425, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601300377, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601301351, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601302103, "dur": 1360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601303464, "dur": 1364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286601304888, "dur": 1321491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286602637412, "dur": 7684, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17072, "tid": 1715, "ts": 1751286602681837, "dur": 4201, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17072, "tid": 1715, "ts": 1751286602686096, "dur": 4527, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17072, "tid": 1715, "ts": 1751286602674549, "dur": 17572, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}