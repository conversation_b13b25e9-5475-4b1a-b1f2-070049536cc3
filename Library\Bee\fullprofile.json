{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17072, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17072, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17072, "tid": 1740, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17072, "tid": 1740, "ts": 1751286780987092, "dur": 1229, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780995552, "dur": 1368, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17072, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17072, "tid": 1, "ts": 1751286779387107, "dur": 19914, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751286779407028, "dur": 56322, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751286779463366, "dur": 55425, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780996926, "dur": 17, "ph": "X", "name": "", "args": {}}, {"pid": 17072, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779380129, "dur": 29750, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779409883, "dur": 1554650, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779413401, "dur": 13597, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779427012, "dur": 5568, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779432588, "dur": 1451, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434045, "dur": 30, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434076, "dur": 99, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434183, "dur": 6, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434191, "dur": 95, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434290, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434294, "dur": 69, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434373, "dur": 4, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434379, "dur": 99, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434484, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434580, "dur": 4, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434587, "dur": 81, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434671, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434675, "dur": 76, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434757, "dur": 3, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434763, "dur": 83, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434849, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434853, "dur": 61, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434920, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779434926, "dur": 79, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435009, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435012, "dur": 73, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435091, "dur": 4, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435097, "dur": 85, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435186, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435190, "dur": 70, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435268, "dur": 3, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435274, "dur": 74, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435352, "dur": 2, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435355, "dur": 78, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435440, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435446, "dur": 117, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435571, "dur": 4, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435578, "dur": 110, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435694, "dur": 4, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435703, "dur": 68, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435776, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435779, "dur": 71, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435857, "dur": 3, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435863, "dur": 80, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435947, "dur": 2, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779435950, "dur": 70, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436027, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436033, "dur": 82, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436119, "dur": 4, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436124, "dur": 63, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436194, "dur": 3, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436199, "dur": 84, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436287, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436290, "dur": 73, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436370, "dur": 4, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436376, "dur": 75, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436456, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436459, "dur": 73, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436538, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436544, "dur": 71, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436618, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436622, "dur": 74, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436702, "dur": 3, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436708, "dur": 80, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436792, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436796, "dur": 85, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436888, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436894, "dur": 81, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436979, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779436983, "dur": 73, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437063, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437069, "dur": 85, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437158, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437162, "dur": 76, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437244, "dur": 3, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437250, "dur": 88, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437342, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437346, "dur": 61, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437414, "dur": 3, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437419, "dur": 84, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437507, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437510, "dur": 72, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437589, "dur": 3, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437594, "dur": 87, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437685, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437689, "dur": 75, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437771, "dur": 3, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437776, "dur": 67, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437847, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437852, "dur": 102, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437960, "dur": 3, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779437966, "dur": 77, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438047, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438050, "dur": 65, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438125, "dur": 3, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438131, "dur": 80, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438215, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438218, "dur": 71, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438296, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438302, "dur": 81, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438386, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438390, "dur": 74, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438470, "dur": 3, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438476, "dur": 80, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438561, "dur": 2, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438564, "dur": 71, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438641, "dur": 3, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438647, "dur": 81, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438732, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438736, "dur": 65, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438807, "dur": 3, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438813, "dur": 84, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438901, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438905, "dur": 67, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438979, "dur": 3, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779438986, "dur": 90, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439080, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439084, "dur": 74, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439165, "dur": 4, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439172, "dur": 69, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439245, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439248, "dur": 72, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439327, "dur": 3, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439332, "dur": 84, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439421, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439425, "dur": 73, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439504, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439510, "dur": 85, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439599, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439603, "dur": 68, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439677, "dur": 3, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779439684, "dur": 363, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440057, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440157, "dur": 5, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440165, "dur": 92, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440261, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440265, "dur": 73, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440346, "dur": 4, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440353, "dur": 93, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440451, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440454, "dur": 79, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440540, "dur": 4, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440547, "dur": 87, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440638, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440643, "dur": 73, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440722, "dur": 4, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440729, "dur": 63, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440796, "dur": 2, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440800, "dur": 68, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440875, "dur": 3, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440881, "dur": 67, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440952, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779440955, "dur": 75, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441036, "dur": 4, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441044, "dur": 85, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441133, "dur": 4, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441139, "dur": 73, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441218, "dur": 3, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441224, "dur": 67, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441295, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441298, "dur": 75, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441380, "dur": 3, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441386, "dur": 82, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441472, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441476, "dur": 73, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441556, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441562, "dur": 83, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441649, "dur": 4, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441655, "dur": 67, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441729, "dur": 3, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441734, "dur": 84, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441822, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441825, "dur": 73, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441905, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779441911, "dur": 91, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442006, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442011, "dur": 72, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442090, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442096, "dur": 72, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442172, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442175, "dur": 72, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442254, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442260, "dur": 86, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442349, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442353, "dur": 74, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442434, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442441, "dur": 82, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442527, "dur": 2, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442531, "dur": 64, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442601, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442609, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442696, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442700, "dur": 72, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442779, "dur": 7, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442788, "dur": 81, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442874, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442878, "dur": 72, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442956, "dur": 3, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779442962, "dur": 66, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443033, "dur": 2, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443036, "dur": 78, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443125, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443132, "dur": 92, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443228, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443232, "dur": 64, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443303, "dur": 4, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443309, "dur": 111, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443424, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443427, "dur": 64, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443498, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443504, "dur": 89, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443596, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443601, "dur": 72, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443680, "dur": 3, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443685, "dur": 83, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443773, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443777, "dur": 71, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443855, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443861, "dur": 68, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443933, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779443936, "dur": 77, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444020, "dur": 4, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444027, "dur": 84, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444115, "dur": 4, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444121, "dur": 68, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444195, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444201, "dur": 66, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444271, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444275, "dur": 54, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444336, "dur": 4, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444342, "dur": 70, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444416, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444419, "dur": 57, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444484, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444490, "dur": 66, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444560, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444564, "dur": 61, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444632, "dur": 4, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444638, "dur": 65, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444707, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444711, "dur": 58, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444776, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779444781, "dur": 900, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779445691, "dur": 4, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779445697, "dur": 463, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446171, "dur": 19, "ph": "X", "name": "ProcessMessages 7068", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446193, "dur": 51, "ph": "X", "name": "ReadAsync 7068", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446249, "dur": 2, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446253, "dur": 80, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446343, "dur": 3, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446349, "dur": 61, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446413, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446418, "dur": 49, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446471, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446473, "dur": 57, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446558, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446565, "dur": 75, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446642, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446648, "dur": 50, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446702, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446705, "dur": 59, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446772, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446777, "dur": 54, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446834, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446838, "dur": 48, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446891, "dur": 2, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446894, "dur": 61, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446963, "dur": 3, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779446969, "dur": 67, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447040, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447044, "dur": 95, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447147, "dur": 3, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447153, "dur": 62, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447218, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447223, "dur": 49, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447276, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447279, "dur": 61, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447347, "dur": 3, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447353, "dur": 69, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447426, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447430, "dur": 60, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447497, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447504, "dur": 71, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447579, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447582, "dur": 59, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447649, "dur": 3, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447655, "dur": 61, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447719, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447723, "dur": 47, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447774, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447778, "dur": 59, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447843, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447849, "dur": 56, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447908, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447914, "dur": 52, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447970, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779447973, "dur": 40, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448015, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448018, "dur": 51, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448075, "dur": 3, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448081, "dur": 85, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448170, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448174, "dur": 65, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448246, "dur": 3, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448254, "dur": 85, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448343, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448347, "dur": 84, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448439, "dur": 3, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448445, "dur": 89, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448538, "dur": 2, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448542, "dur": 60, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448609, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448615, "dur": 84, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448703, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448706, "dur": 74, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448786, "dur": 3, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448792, "dur": 75, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448871, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448875, "dur": 81, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448963, "dur": 3, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779448968, "dur": 90, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449062, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449066, "dur": 64, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449135, "dur": 2, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449139, "dur": 77, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449221, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449224, "dur": 63, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449292, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449296, "dur": 62, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449362, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449365, "dur": 67, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449436, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449440, "dur": 69, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449513, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449517, "dur": 61, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449582, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449586, "dur": 58, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449649, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449652, "dur": 58, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449716, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449719, "dur": 64, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449787, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449792, "dur": 70, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449866, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449870, "dur": 63, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449937, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779449940, "dur": 61, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450006, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450010, "dur": 64, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450078, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450082, "dur": 66, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450152, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450156, "dur": 61, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450220, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450224, "dur": 61, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450289, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450293, "dur": 62, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450359, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450363, "dur": 60, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450427, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450430, "dur": 62, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450496, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450499, "dur": 60, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450563, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450566, "dur": 79, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450650, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450653, "dur": 68, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450726, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450729, "dur": 66, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450799, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450803, "dur": 59, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450867, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450870, "dur": 64, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450938, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779450941, "dur": 69, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451015, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451018, "dur": 61, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451083, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451087, "dur": 63, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451154, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451158, "dur": 63, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451225, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451228, "dur": 61, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451293, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451297, "dur": 94, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451395, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451398, "dur": 68, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451470, "dur": 2, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451474, "dur": 56, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451534, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451538, "dur": 63, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451605, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451608, "dur": 61, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451674, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451678, "dur": 57, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451740, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451743, "dur": 61, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451808, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779451814, "dur": 320, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452140, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452145, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452218, "dur": 595, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452819, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452951, "dur": 21, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779452976, "dur": 80, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453061, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453065, "dur": 63, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453140, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453146, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453212, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453217, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453277, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453283, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453339, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453345, "dur": 65, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453416, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453422, "dur": 76, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453504, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453510, "dur": 68, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453586, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453593, "dur": 85, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453688, "dur": 8, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453701, "dur": 97, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453807, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453817, "dur": 79, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453901, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453905, "dur": 64, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453978, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779453985, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454041, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454044, "dur": 56, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454105, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454109, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454164, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454167, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454216, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454219, "dur": 48, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454272, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454275, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454340, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454349, "dur": 84, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454443, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454452, "dur": 63, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454518, "dur": 6, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454526, "dur": 60, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454592, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454599, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454659, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454663, "dur": 184, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454862, "dur": 7, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779454873, "dur": 152, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455034, "dur": 14, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455052, "dur": 69, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455127, "dur": 8, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455140, "dur": 69, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455213, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455217, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455282, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455287, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455346, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455367, "dur": 53, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455424, "dur": 376, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455805, "dur": 54, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455863, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779455868, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779456008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779456012, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779456066, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779456070, "dur": 9156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465243, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465250, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465303, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465307, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465353, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779465356, "dur": 850, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466219, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466299, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466303, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466361, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466364, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466413, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466415, "dur": 280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466703, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466776, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779466779, "dur": 1822, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468609, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468613, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468694, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468700, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468783, "dur": 14, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779468799, "dur": 388, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779469195, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779469200, "dur": 211, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779469416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779469419, "dur": 903, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470329, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470394, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470397, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470456, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470461, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470533, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470537, "dur": 321, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470870, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470936, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779470945, "dur": 93, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471046, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471050, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471109, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471113, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471267, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471329, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471333, "dur": 470, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471811, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471816, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471884, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471889, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471951, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779471956, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472017, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472021, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472071, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472074, "dur": 258, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472341, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472399, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472403, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472461, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472464, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472512, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472515, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472686, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472736, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472740, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472799, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472849, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472852, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472899, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472902, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779472963, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473010, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473013, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473248, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473304, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473309, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473363, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473368, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473423, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473426, "dur": 229, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473663, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473725, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473728, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473775, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473779, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473826, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473828, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779473876, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474054, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474116, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474120, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474178, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474182, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474251, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474254, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474313, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474317, "dur": 61, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474390, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474447, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474450, "dur": 259, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474715, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474766, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474770, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474858, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474866, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474934, "dur": 11, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779474949, "dur": 165, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475123, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475127, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475192, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475197, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475251, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475255, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475309, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475312, "dur": 406, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475723, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475726, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475789, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475836, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475839, "dur": 131, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475975, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779475978, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476039, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476090, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476093, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476156, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476200, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476203, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476256, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476321, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476323, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476372, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476374, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476423, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476426, "dur": 480, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476911, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476913, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476983, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779476985, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477228, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477235, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477318, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477321, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477380, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477386, "dur": 508, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477902, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477966, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779477969, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478019, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478067, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478188, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478237, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478240, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478283, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478285, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478768, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478824, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478828, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478877, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478880, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478959, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779478963, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479006, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479009, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479072, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479122, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479125, "dur": 149, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479281, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479327, "dur": 423, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479759, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479817, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479820, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479872, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779479875, "dur": 407, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480290, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480294, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480352, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480402, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480405, "dur": 347, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480759, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480800, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779480802, "dur": 1078, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779481885, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779481892, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779481947, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779481950, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779481997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482000, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482066, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482070, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482128, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482176, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482179, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482229, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482232, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482490, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779482537, "dur": 1029, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483573, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483579, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483632, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483635, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483680, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483683, "dur": 209, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483898, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483943, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779483945, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779484024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779484026, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779484070, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779484073, "dur": 1006, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485084, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485089, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485145, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485147, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485194, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779485196, "dur": 4551, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779489753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779489756, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779489807, "dur": 862, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286779490675, "dur": 570089, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780060778, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780060785, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780060888, "dur": 4717, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780065613, "dur": 5961, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780071584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780071589, "dur": 192, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780071789, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780071796, "dur": 1461, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780073262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780073265, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780073482, "dur": 114, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780073707, "dur": 869470, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780943190, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780943198, "dur": 11547, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780954760, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780954771, "dur": 95, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780954871, "dur": 681, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751286780955558, "dur": 8889, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780996946, "dur": 2040, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286779369197, "dur": 149645, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286779518845, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751286779518854, "dur": 2087, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780998989, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17072, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286779330164, "dur": 1637369, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286779338968, "dur": 13158, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286780968067, "dur": 12359, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286780975702, "dur": 274, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751286780980704, "dur": 37, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780999003, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751286779403890, "dur": 83, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286779404067, "dur": 2817, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286779406902, "dur": 657, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286779407640, "dur": 84, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751286779407725, "dur": 1172, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286779411441, "dur": 14708, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779428535, "dur": 6736, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779435384, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779435496, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779435824, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779435903, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779435971, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436071, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436230, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436423, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436498, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436650, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436770, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779436903, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437092, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437187, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437258, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437348, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437506, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437687, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437754, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779437921, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438021, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438125, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438294, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438459, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438558, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438745, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438820, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438913, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779438989, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439084, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439163, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439264, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439375, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439454, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439524, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439705, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439802, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439872, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779439965, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440114, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440294, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440407, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440560, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440657, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440730, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779440824, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779441373, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779441478, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779441674, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779441869, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779441947, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442051, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_900B338E31D9525C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442185, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442279, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442371, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442451, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442552, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_DC1879B0E64A8F15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442612, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442709, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442782, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779442880, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443046, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443121, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443247, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443319, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443413, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443478, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443574, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443647, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443741, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779443905, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444016, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444112, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444186, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444286, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_8B44F821019897E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444342, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444442, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444553, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444634, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444824, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444903, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779444995, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445069, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445165, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_C6D619E6129B87D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445352, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445427, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445507, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445649, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779445809, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779446096, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779447014, "dur": 489, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779447511, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779447567, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779447714, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779447888, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448099, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448204, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448294, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448363, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448459, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448528, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448672, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448741, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448831, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779448954, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449148, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449214, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449398, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449478, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449573, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449643, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449773, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779449934, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450002, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450116, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450183, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450280, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450438, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450537, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450596, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779450746, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451031, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451174, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451315, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451404, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451464, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451521, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451611, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451669, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451726, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451815, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779451873, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452037, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452096, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452239, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452336, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452395, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452455, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452590, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452715, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751286779452837, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751286779408938, "dur": 44117, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286779453070, "dur": 1492793, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780945865, "dur": 858, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780946724, "dur": 437, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780947179, "dur": 175, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780947364, "dur": 77, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780947456, "dur": 55, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780947682, "dur": 65, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780948050, "dur": 161, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780948253, "dur": 2641, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751286779408596, "dur": 44490, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779453116, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779453414, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779453562, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779453818, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779453992, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779454133, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779454287, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779454763, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779454868, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779455016, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779455186, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286779455326, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751286779455869, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286779455967, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286779456212, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751286779457485, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\ClipAction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751286779456599, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779458185, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779458909, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779459569, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779460349, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779461305, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_SpriteAnimator.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751286779461037, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779463103, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Actions\\Interactions\\TapInteraction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751286779462603, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779464237, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779464954, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779465209, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779465269, "dur": 1259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779466529, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779467553, "dur": 2375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779469969, "dur": 1727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779471697, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779471850, "dur": 1263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779473163, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779473242, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779473683, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779474069, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779474575, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779474685, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751286779475003, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751286779475936, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779476140, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779476521, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779477017, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779477303, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779477683, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779478585, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779479280, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779480075, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779480327, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779481076, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779481609, "dur": 1587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779483196, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779483360, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779484882, "dur": 1527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751286779486410, "dur": 1459471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779408664, "dur": 44472, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779453141, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779453411, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779453566, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779453680, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779453772, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779453963, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779454064, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779454225, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779454533, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779454773, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779454905, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751286779455164, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779455288, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751286779455791, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751286779456194, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751286779456301, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779457183, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779458347, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779459043, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779459758, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779461284, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsMetaType.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751286779460584, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779462135, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779463244, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779463922, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779464652, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779465318, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779466571, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779467580, "dur": 2362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779469943, "dur": 1749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779471694, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779471845, "dur": 1261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779473106, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779473171, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779473273, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779473685, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779474082, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779474695, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779474984, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779475384, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779475547, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779476160, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779476511, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779477020, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779477304, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779477656, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779478576, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779478639, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779479245, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779480109, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779480299, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779481063, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779481618, "dur": 1569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779483188, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779483368, "dur": 1501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779484870, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779484923, "dur": 1469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751286779486392, "dur": 1459466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779410016, "dur": 43522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779453544, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779453803, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779453965, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779454070, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779454139, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779454239, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779454408, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779454553, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779454711, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779454852, "dur": 1505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779456368, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779456881, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779457251, "dur": 9172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751286779466605, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779467598, "dur": 2341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779469941, "dur": 1727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779471668, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779471804, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779472176, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751286779473191, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779473707, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751286779473965, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751286779474878, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779475038, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779475410, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779475512, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779476089, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779476496, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779477030, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779477356, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779477606, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779478595, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779479254, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779480088, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779480301, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779481067, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779481623, "dur": 1550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779483222, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779483382, "dur": 1504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779484886, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751286779486404, "dur": 1459516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779408855, "dur": 44377, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779453240, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779453433, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779453583, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779453767, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779453850, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779454027, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779454123, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779454418, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779454515, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779454831, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779454886, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779455165, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751286779455670, "dur": 446, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751286779456123, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779456202, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751286779457202, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ItemGui\\TimelineMarkerClusterGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751286779456375, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779458432, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779459167, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779459875, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779461298, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Connections\\GraphConnectionCollection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751286779460732, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779462484, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779463543, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779464400, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779466260, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779466547, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779467567, "dur": 2380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779469948, "dur": 1716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779471665, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779471828, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779473019, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779473124, "dur": 1996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779475122, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779475488, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779476074, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779476145, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779476508, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779477070, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779477450, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779480346, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779480559, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779481653, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779482011, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779483425, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779483774, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779484736, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779484918, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751286779485306, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779486220, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779486375, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286779488572, "dur": 166, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751286779490038, "dur": 571938, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751286780072549, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751286780072529, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751286780072818, "dur": 1738, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751286780074561, "dur": 871350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779408935, "dur": 44333, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779453273, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779453390, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779453484, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779453657, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779453869, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454035, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454162, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454348, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454725, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454820, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779454948, "dur": 709, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779455669, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779455725, "dur": 1219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751286779456945, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779458360, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779459056, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779459844, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779461271, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\IMergedCollection.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751286779460772, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779462289, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779463412, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779464535, "dur": 2814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779467350, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779467585, "dur": 2411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779469996, "dur": 1691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779471688, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779471832, "dur": 1288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779473164, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779473229, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779473682, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779474067, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779474573, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779474665, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779475058, "dur": 921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751286779475980, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779476153, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779476499, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779477033, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779477311, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779477637, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779478555, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779479265, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779480089, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779480281, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779481060, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779481631, "dur": 1544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779483175, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779483364, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779484922, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751286779485178, "dur": 1206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751286779486385, "dur": 1459464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779410567, "dur": 43106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779453675, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779453992, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779454090, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779454271, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779454497, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779454710, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779454884, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286779455049, "dur": 585, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751286779455678, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751286779456011, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751286779456215, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751286779456563, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779457573, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779458215, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779458995, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779459730, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779461244, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnBecameVisibleMessageListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751286779460424, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779462077, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779463229, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779464316, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779465117, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779465174, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779465268, "dur": 1272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779466542, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779467587, "dur": 2396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779469984, "dur": 1714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779471698, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779471818, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779472819, "dur": 1281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779474116, "dur": 3302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751286779477420, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779477681, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751286779478076, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779478145, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751286779479365, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779479514, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779480100, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779480283, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779481057, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779481637, "dur": 1569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779483207, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779483374, "dur": 1510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779484885, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751286779486394, "dur": 1459492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779409088, "dur": 44235, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779453328, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779453490, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779453653, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779453866, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779453937, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779454096, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779454252, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779454542, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779454794, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751286779454954, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286779455247, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751286779455398, "dur": 628, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286779456028, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751286779456173, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751286779456512, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779457568, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779458238, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779458965, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779459837, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779461311, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsPrimitiveConverter.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751286779460628, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779462154, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779463035, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779464155, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779465334, "dur": 1191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779466526, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779467594, "dur": 2359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779469955, "dur": 1702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779471711, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779471847, "dur": 1254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779473290, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779473702, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779474079, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779474589, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779474678, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779474993, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779475402, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779475492, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779476120, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779476491, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779477041, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779477321, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779477623, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779478566, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779479252, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779480063, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779480359, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779481109, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779481624, "dur": 1570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779483194, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779483406, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779484925, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779486381, "dur": 38498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751286779524881, "dur": 1421096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779409198, "dur": 44139, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779453342, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779453462, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779453531, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779453654, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779453733, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779453893, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779454068, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779454244, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779454572, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779454740, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779454852, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779455030, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751286779455352, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751286779455549, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751286779455625, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779455711, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779455763, "dur": 729, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751286779456494, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779457909, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779459006, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779459761, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779460548, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779461297, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Common\\StyleConstants.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751286779461297, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779462716, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779463594, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779464387, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779465918, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779466531, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779467596, "dur": 2391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779469988, "dur": 1672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779471820, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751286779472170, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751286779473106, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779473236, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779473298, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779473684, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779474241, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779474581, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779474669, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779474991, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779475380, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779475504, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779476114, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779476483, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779477036, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779477349, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779477613, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779478547, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779479224, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779479521, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779480116, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779480335, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779481066, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779481613, "dur": 1570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779483184, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779483369, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779484896, "dur": 1517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751286779486414, "dur": 1459532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779409306, "dur": 44044, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779453354, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779453536, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779453795, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779453959, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779454109, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779454282, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751286779454820, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779454958, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751286779455223, "dur": 539, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751286779455804, "dur": 740, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751286779456547, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779457748, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779458645, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779459355, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779461276, "dur": 990, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticActionInvoker_0.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751286779460123, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779462297, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779463173, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779463865, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779464604, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779465534, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779466553, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779467583, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779467652, "dur": 2297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779469950, "dur": 1749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779471700, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779471834, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779473108, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779473167, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779473238, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779473684, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779474075, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779474605, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779474675, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779474990, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779475658, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779476118, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779476507, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779477027, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779477300, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779477643, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779478587, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779479233, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779480085, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779480324, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779481084, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779481659, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779483186, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779483354, "dur": 1517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779484871, "dur": 1530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751286779486402, "dur": 1459540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779409375, "dur": 43986, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779453366, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779453544, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779453656, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779453786, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779454014, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779454116, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779454169, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779454343, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779454643, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779454710, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779454950, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286779455051, "dur": 841, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286779455982, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286779456101, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751286779456340, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779457395, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779458309, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779459092, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779460077, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779461303, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Timeline.deprecated.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751286779460859, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779462298, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779463194, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779463988, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779464715, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779465480, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779466558, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779467599, "dur": 2377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779469977, "dur": 1704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779471683, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779471832, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751286779472182, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751286779473045, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779473250, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779473691, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779474061, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779474579, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779474676, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779475045, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779475382, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779475502, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779476079, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779476472, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779477029, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779477318, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779477627, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779478589, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779479272, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779480099, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779480309, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779481103, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779481641, "dur": 1568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779483210, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779483387, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779484876, "dur": 1506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286779486383, "dur": 586187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751286780072630, "dur": 871737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751286780072574, "dur": 871795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751286780944402, "dur": 1300, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751286779409707, "dur": 43763, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779453476, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779453679, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779453751, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779453908, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779454074, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779454238, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779454485, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779454602, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779454823, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779454909, "dur": 504, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779455429, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779455626, "dur": 11709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779467337, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779467631, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779467959, "dur": 1815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779469776, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779469942, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779470025, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779470405, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779471496, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779471790, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779472320, "dur": 2142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779474463, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779474661, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779474936, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779476914, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779477068, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779477435, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779479945, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779480064, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779480125, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751286779480353, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751286779481098, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779481610, "dur": 1568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779483181, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779483352, "dur": 1537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779484889, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751286779486411, "dur": 1459455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779409480, "dur": 43904, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779453392, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779453690, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779453833, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779453915, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779454134, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779454233, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779454491, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779454617, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751286779454859, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779454989, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779455047, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751286779455485, "dur": 490, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751286779455977, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751286779456087, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751286779456181, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779456272, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751286779456669, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779458114, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779459284, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779461294, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Events\\EmptyEventArgs.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751286779460467, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779462551, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779463793, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779464544, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779465553, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779466536, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779467583, "dur": 2385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779469968, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779471706, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779471854, "dur": 1258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779473112, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779473175, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779473236, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779473749, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779474077, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779474595, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779474698, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779475001, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779475418, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779475525, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779476093, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779476481, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779477044, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779477312, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779477634, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779478560, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779479257, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779480096, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779480290, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779481075, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779481635, "dur": 1556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779483191, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779483356, "dur": 1538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779484895, "dur": 1494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751286779486390, "dur": 1459463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779409522, "dur": 43879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779453406, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779453598, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779453944, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779454103, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779454307, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779454779, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779454835, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779454888, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779455000, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751286779455221, "dur": 1035, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751286779456261, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751286779456369, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779457392, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779458038, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779458732, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779459443, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779461084, "dur": 958, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\ModuloHandler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751286779460294, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779462043, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779462819, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779464069, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779465394, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779466544, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779467593, "dur": 2351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779469945, "dur": 1717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779471663, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779471810, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751286779472188, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751286779473066, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779473743, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779474083, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779474570, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779474674, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779474987, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779475382, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779475669, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779476086, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779476518, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779477053, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779477307, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779477692, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779478567, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779479259, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779480071, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779480285, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779481055, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779481614, "dur": 1604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779483218, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779483380, "dur": 1500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779484881, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751286779486387, "dur": 1459458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779409592, "dur": 43840, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779453440, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779453811, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779453996, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779454095, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779454255, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779454564, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779454827, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779454952, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751286779455262, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779455330, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751286779455483, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286779455599, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779455664, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286779455963, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286779456040, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751286779456315, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779457385, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779458035, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779458982, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779459673, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779461279, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsISerializationCallbacks.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751286779460604, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779462315, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779463271, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779464284, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779465248, "dur": 1290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779466539, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779467589, "dur": 2335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779469938, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779470004, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779471684, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779471862, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779473240, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779473696, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779474123, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779474587, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779474672, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779474994, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779475423, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779475516, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779476165, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779476505, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779477047, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779477309, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779477645, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779478572, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779479242, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779480073, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779480337, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779481064, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779481628, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779483202, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779483393, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779484879, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751286779486398, "dur": 1459503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779409655, "dur": 43798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779453459, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286779453645, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286779453858, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286779454241, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779454482, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286779454843, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751286779455026, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779455122, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286779455294, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779455484, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286779455594, "dur": 487, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286779456133, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751286779456898, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\TimelineEditor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751286779456436, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779458055, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779458866, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779459572, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779460332, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779461302, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Control\\ControlPlayableAsset.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751286779461026, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779462500, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779463525, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779464689, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779465528, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779466551, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779467605, "dur": 2388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779469994, "dur": 1700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779471695, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779471852, "dur": 1258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779473110, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779473167, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779473234, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779473687, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779474101, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779474599, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779474693, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779475012, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779475413, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779475495, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779476124, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779476501, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779477019, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779477306, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779477664, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779478575, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779479270, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779480083, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779480288, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779481059, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779481616, "dur": 1573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779483190, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779483361, "dur": 1516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779484878, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751286779486400, "dur": 1459563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779409799, "dur": 43696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779453504, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286779453913, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779454031, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286779454206, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779454261, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286779454430, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286779454602, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751286779454674, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286779454808, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779454894, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286779455113, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286779455418, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779455607, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779455663, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286779456016, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751286779456185, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779456272, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779457644, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779459018, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779461233, "dur": 841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\CSharpNameUtility.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751286779460065, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779462147, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779463395, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779464666, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779465622, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779466534, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779467557, "dur": 2404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779469964, "dur": 1725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779471690, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779471837, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779473165, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779473231, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779473681, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779474063, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779474607, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779474680, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779474996, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779475376, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779475532, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779476082, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779476476, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779477023, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779477314, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779477674, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779478579, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779479261, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779480079, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779480302, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779481088, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779481639, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779483217, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779483355, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779483461, "dur": 1430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779484893, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751286779486420, "dur": 1459554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779409951, "dur": 43570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779453530, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779453827, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779453930, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779454106, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779454196, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779454413, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779454580, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779454716, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779454810, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779454878, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751286779455098, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751286779455231, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751286779455326, "dur": 725, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751286779456053, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751286779456182, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779456270, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751286779456645, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779457713, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779458689, "dur": 1265, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NotEqual.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751286779458526, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779461288, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Exceptions\\UnexpectedEnumValueException.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751286779460459, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779462216, "dur": 944, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XInput\\XboxGamepadMacOS.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751286779462087, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779463740, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779464479, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779466019, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779466550, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779467555, "dur": 2378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779469933, "dur": 1740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779471675, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779471815, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779472214, "dur": 930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779473163, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751286779473956, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779474136, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779474583, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779474667, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779475063, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779475128, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751286779476030, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779476176, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779476513, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779477028, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779477294, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779477602, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779478543, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779479228, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779480069, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779480278, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779481085, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779481655, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751286779482021, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751286779483053, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779483215, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779483405, "dur": 1501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779484906, "dur": 1500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751286779486407, "dur": 1459528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779408759, "dur": 44424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779453191, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779453384, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779453458, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779453620, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779453755, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779453895, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779453988, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779454167, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779454248, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779454403, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779454730, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779454825, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779454989, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779455049, "dur": 614, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751286779455780, "dur": 692, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751286779456475, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779457895, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779458734, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779459417, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779460076, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779461287, "dur": 706, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\SignalTrack.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751286779460932, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779463270, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Analytics\\InputActionsEditorSessionAnalytic.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751286779462413, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779463884, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779464509, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779466189, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779466522, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779467548, "dur": 2389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779469937, "dur": 1734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779471671, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779471806, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779472452, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779472540, "dur": 1733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751286779474275, "dur": 1173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779475490, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779475574, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751286779476071, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779476148, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751286779478330, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779478551, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779478620, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779479228, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779479313, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779480081, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779480312, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779481079, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779481626, "dur": 1578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779483205, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779483375, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779484899, "dur": 1496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751286779486396, "dur": 1459499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779410093, "dur": 43462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779453561, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286779453769, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779453889, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286779454053, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779454154, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286779454483, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779454663, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779454757, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751286779454864, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779454999, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751286779455235, "dur": 827, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751286779456207, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751286779456305, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779457273, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779458200, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779459383, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779460456, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779461295, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751286779461220, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779462719, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779463502, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779464215, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779464940, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779466012, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779466566, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779467564, "dur": 2401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779469965, "dur": 1714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779471679, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779471858, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779473110, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779473243, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779473686, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779474070, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779474577, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779474670, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779474990, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779475380, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779475491, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779475562, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779476090, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779476478, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779477083, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779477319, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779477611, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779477699, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779478541, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779479236, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779480067, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779480319, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779481099, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779481621, "dur": 1576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779483197, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779483376, "dur": 1524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779484900, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751286779486403, "dur": 1459520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779410215, "dur": 43354, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779453575, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286779454199, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286779454366, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286779454723, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286779454940, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751286779455173, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286779455387, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286779455751, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779455958, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286779456174, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779456229, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751286779456617, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779457888, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779458879, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779459579, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779460324, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779461283, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Activation\\ActivationMixerPlayable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751286779461030, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779462433, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779463739, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779464520, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779466066, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779466545, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779467609, "dur": 2381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779469991, "dur": 1695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779471686, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779471794, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286779472910, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779473179, "dur": 1956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751286779475137, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779475557, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751286779475892, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779475995, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751286779477165, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779477342, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779477611, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779478553, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779479240, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779480065, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779480298, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779481111, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779481620, "dur": 1551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779483226, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779483358, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779484873, "dur": 1542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751286779486416, "dur": 1459541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779410320, "dur": 43268, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779453594, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286779453861, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286779453964, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779454044, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286779454297, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751286779454829, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779454975, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751286779455067, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751286779455774, "dur": 388, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751286779456213, "dur": 365, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751286779456579, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779458665, "dur": 881, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.State\\OnExitState.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751286779457843, "dur": 2306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779460349, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\OptimizedReflection.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751286779461273, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFunctionInvoker_3.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751286779460150, "dur": 2203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779462353, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779463192, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779463971, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779464690, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779465409, "dur": 1110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779466587, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779467559, "dur": 2376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779469936, "dur": 1781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779471718, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779471839, "dur": 1277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779473116, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779473173, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779473235, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779473678, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779474065, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779474592, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779474682, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779474986, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779475379, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779475540, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779476109, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779476486, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779477016, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779477074, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779477298, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779477607, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779478539, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779479238, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779480093, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779480304, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779481089, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779481632, "dur": 1560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779483193, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779483350, "dur": 1533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779484884, "dur": 1490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751286779486421, "dur": 1459529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779410358, "dur": 43244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779453608, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779453829, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454008, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454154, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454278, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454587, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454754, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779454850, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751286779455106, "dur": 623, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751286779455928, "dur": 398, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751286779456329, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779458020, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779460116, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_4_5_to_1_4_6.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751286779459200, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779461306, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\IDocumentReference.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751286779461273, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779462759, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779463581, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779464341, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779465214, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779465386, "dur": 1162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779466549, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779467570, "dur": 2402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779469973, "dur": 1703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779471677, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779471830, "dur": 1294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779473246, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779473665, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779473723, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779474078, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779474603, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779474689, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779475003, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779475388, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779475500, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779476076, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779476474, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779477025, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779477297, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779477609, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779478536, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779479226, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779480097, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779480350, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779481091, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779481607, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779481660, "dur": 1516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779483177, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779483379, "dur": 1525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779484905, "dur": 1512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751286779486418, "dur": 1459450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779410453, "dur": 43181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779453639, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779453835, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779454000, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779454118, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779454170, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779454462, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779454590, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779454701, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779455146, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751286779455493, "dur": 793, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751286779456288, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779457308, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779458059, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779458771, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779459769, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779461299, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\ArrayCloner.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751286779460840, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779462406, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779463354, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779464125, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779464900, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779466168, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779466552, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779467572, "dur": 2417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779469989, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779471670, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779471833, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751286779472139, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751286779472984, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779473170, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779473233, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779473697, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779474073, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779474594, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779474684, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779474998, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779475425, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779475491, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779476084, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779476477, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779476542, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779477039, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779477302, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779477604, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779478538, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779479288, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779480074, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779480342, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779481081, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779481643, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779483199, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779483373, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779484888, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751286779486432, "dur": 1459495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779409004, "dur": 44283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779453294, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779453540, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779453615, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779453814, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779453867, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779453955, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779454057, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779454128, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779454232, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779454455, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779454651, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779454746, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779454884, "dur": 628, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779455693, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779455780, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779456043, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751286779456169, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779456622, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\SolutionParser.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751286779456299, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779457867, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779459010, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779460348, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779461291, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_FontAssetCommon.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751286779461142, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779462999, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779463940, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779464830, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779465986, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779466524, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779467551, "dur": 2379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779469931, "dur": 1735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779471667, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779471803, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779472605, "dur": 1537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779474158, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751286779476236, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779476486, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779476565, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751286779476914, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779477688, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751286779479033, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779479231, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779479303, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779480102, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779480316, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779481070, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779481630, "dur": 1582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779483212, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779483366, "dur": 1535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779484903, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779486379, "dur": 35876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779524408, "dur": 425, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 24, "ts": 1751286779522257, "dur": 2580, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751286779524837, "dur": 1421078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751286780957246, "dur": 7359, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17072, "tid": 1740, "ts": 1751286780999796, "dur": 4080, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17072, "tid": 1740, "ts": 1751286781003937, "dur": 4304, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17072, "tid": 1740, "ts": 1751286780992789, "dur": 16948, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}