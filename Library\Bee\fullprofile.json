{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21056, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21056, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21056, "tid": 1040, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21056, "tid": 1040, "ts": 1751342140693732, "dur": 1901, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140705100, "dur": 1554, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21056, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21056, "tid": 1, "ts": 1751342139158887, "dur": 8830, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751342139167720, "dur": 98951, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751342139266686, "dur": 59957, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140706661, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 21056, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139155035, "dur": 29111, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139184151, "dur": 1494265, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139187314, "dur": 8472, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139195795, "dur": 2972, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139198772, "dur": 552, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199334, "dur": 58, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199395, "dur": 114, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199514, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199518, "dur": 73, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199599, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199604, "dur": 77, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199684, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199688, "dur": 64, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199759, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199763, "dur": 76, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199843, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199847, "dur": 71, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199925, "dur": 3, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139199930, "dur": 72, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200007, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200010, "dur": 260, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200277, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200282, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200379, "dur": 3, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200383, "dur": 78, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200468, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200474, "dur": 91, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200568, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200572, "dur": 74, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200654, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200660, "dur": 66, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200730, "dur": 2, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200733, "dur": 77, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200817, "dur": 3, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200823, "dur": 84, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200911, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200915, "dur": 75, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139200997, "dur": 3, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201003, "dur": 88, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201095, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201099, "dur": 66, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201171, "dur": 3, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201177, "dur": 78, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201259, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201263, "dur": 83, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201352, "dur": 3, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201358, "dur": 85, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201447, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201451, "dur": 115, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201573, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201579, "dur": 82, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201665, "dur": 2, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201669, "dur": 72, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201747, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201753, "dur": 113, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201870, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201874, "dur": 63, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201941, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139201944, "dur": 60, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202009, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202012, "dur": 45, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202062, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202064, "dur": 43, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202111, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202114, "dur": 108, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202228, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202279, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202281, "dur": 45, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202330, "dur": 2, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202333, "dur": 45, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202382, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202386, "dur": 50, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202440, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202442, "dur": 62, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202508, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202512, "dur": 65, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202586, "dur": 3, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202592, "dur": 73, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202669, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202672, "dur": 62, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202742, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202747, "dur": 62, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202812, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202815, "dur": 59, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202882, "dur": 9, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202893, "dur": 58, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202955, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139202958, "dur": 61, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203026, "dur": 3, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203033, "dur": 66, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203103, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203107, "dur": 60, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203173, "dur": 2, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203178, "dur": 61, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203242, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203246, "dur": 57, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203316, "dur": 3, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203321, "dur": 67, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203392, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203395, "dur": 59, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203459, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203462, "dur": 49, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203515, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203518, "dur": 43, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203564, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203568, "dur": 55, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203629, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203683, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203686, "dur": 48, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203738, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203741, "dur": 45, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203790, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203793, "dur": 50, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203847, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203850, "dur": 44, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203898, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203901, "dur": 46, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203950, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139203953, "dur": 48, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204007, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204010, "dur": 47, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204061, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204064, "dur": 46, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204114, "dur": 2, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204117, "dur": 48, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204169, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204173, "dur": 64, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204243, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204296, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204298, "dur": 45, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204348, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204351, "dur": 46, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204400, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204403, "dur": 48, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204455, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204458, "dur": 47, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204509, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204512, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204560, "dur": 2, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204564, "dur": 51, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204619, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204622, "dur": 59, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204689, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204695, "dur": 66, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204765, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204768, "dur": 47, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204819, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204822, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204887, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204890, "dur": 51, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204945, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139204948, "dur": 53, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205006, "dur": 2, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205009, "dur": 54, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205067, "dur": 4, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205072, "dur": 53, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205129, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205134, "dur": 54, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205192, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205196, "dur": 54, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205254, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205257, "dur": 54, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205315, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205318, "dur": 44, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205366, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205369, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205422, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205424, "dur": 53, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205481, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205485, "dur": 47, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205536, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205539, "dur": 59, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205603, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205605, "dur": 77, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205687, "dur": 2, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205690, "dur": 65, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205760, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205763, "dur": 65, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205832, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205835, "dur": 62, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205901, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205905, "dur": 60, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139205971, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206037, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206040, "dur": 61, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206105, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206108, "dur": 56, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206168, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206171, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206233, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206236, "dur": 58, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206298, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206302, "dur": 61, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206368, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206371, "dur": 62, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206437, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206441, "dur": 59, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206503, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206506, "dur": 109, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206623, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206628, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206715, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206719, "dur": 45, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206769, "dur": 2, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206773, "dur": 50, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206826, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206830, "dur": 47, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206881, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206884, "dur": 48, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206936, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206939, "dur": 48, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206992, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139206995, "dur": 46, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207046, "dur": 2, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207049, "dur": 52, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207105, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207110, "dur": 40, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207154, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207157, "dur": 614, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207776, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207779, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207837, "dur": 5, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207843, "dur": 44, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207891, "dur": 1, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207894, "dur": 47, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207945, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139207948, "dur": 48, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208000, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208003, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208053, "dur": 2, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208056, "dur": 48, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208110, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208115, "dur": 47, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208166, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208169, "dur": 50, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208224, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208227, "dur": 46, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208278, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208281, "dur": 44, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208330, "dur": 2, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208334, "dur": 65, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208405, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208457, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208461, "dur": 47, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208515, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208519, "dur": 49, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208572, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208576, "dur": 50, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208630, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208633, "dur": 50, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208688, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208692, "dur": 50, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208746, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208749, "dur": 48, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208802, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208805, "dur": 46, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208856, "dur": 2, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208859, "dur": 51, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208914, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208918, "dur": 43, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208967, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139208971, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209018, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209021, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209073, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209077, "dur": 124, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209210, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209267, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209270, "dur": 45, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209320, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209325, "dur": 47, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209376, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209379, "dur": 48, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209432, "dur": 2, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209435, "dur": 50, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209494, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209497, "dur": 46, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209547, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209551, "dur": 46, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209600, "dur": 2, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209604, "dur": 55, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209663, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209667, "dur": 77, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209753, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209811, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209815, "dur": 57, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209876, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209879, "dur": 53, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209936, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209940, "dur": 53, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139209998, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210003, "dur": 73, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210085, "dur": 4, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210092, "dur": 81, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210178, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210181, "dur": 60, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210249, "dur": 3, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210256, "dur": 63, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210323, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210327, "dur": 92, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210425, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210431, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210510, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210514, "dur": 72, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210590, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210594, "dur": 68, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210665, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210669, "dur": 71, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210744, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210748, "dur": 64, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210816, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210820, "dur": 56, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210880, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210884, "dur": 61, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210949, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139210953, "dur": 68, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211025, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211029, "dur": 65, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211099, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211103, "dur": 67, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211174, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211178, "dur": 64, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211246, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211250, "dur": 53, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211308, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211311, "dur": 61, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211376, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211379, "dur": 64, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211448, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211452, "dur": 66, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211522, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211526, "dur": 71, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211602, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211606, "dur": 71, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211681, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211686, "dur": 57, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211748, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211752, "dur": 71, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211827, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211830, "dur": 64, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211899, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139211903, "dur": 100, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212007, "dur": 3, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212011, "dur": 59, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212075, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212079, "dur": 60, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212143, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139212146, "dur": 3638, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139215793, "dur": 60, "ph": "X", "name": "ProcessMessages 20511", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139215859, "dur": 117, "ph": "X", "name": "ReadAsync 20511", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139215980, "dur": 4, "ph": "X", "name": "ProcessMessages 1551", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139215986, "dur": 68, "ph": "X", "name": "ReadAsync 1551", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216061, "dur": 3, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216067, "dur": 75, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216147, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216151, "dur": 66, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216225, "dur": 4, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216231, "dur": 76, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216312, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216316, "dur": 70, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216394, "dur": 3, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216400, "dur": 71, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216475, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216479, "dur": 70, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216557, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216563, "dur": 57, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216624, "dur": 2, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139216628, "dur": 882, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217516, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217520, "dur": 241, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217769, "dur": 22, "ph": "X", "name": "ProcessMessages 6681", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217794, "dur": 139, "ph": "X", "name": "ReadAsync 6681", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217937, "dur": 5, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139217955, "dur": 67, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218025, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218030, "dur": 82, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218128, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218132, "dur": 84, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218223, "dur": 4, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218230, "dur": 118, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218351, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218357, "dur": 38, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218397, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218401, "dur": 87, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218492, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218496, "dur": 156, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218661, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218667, "dur": 220, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218897, "dur": 5, "ph": "X", "name": "ProcessMessages 1605", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139218904, "dur": 92, "ph": "X", "name": "ReadAsync 1605", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219008, "dur": 5, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219015, "dur": 67, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219087, "dur": 47, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219139, "dur": 136, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219279, "dur": 3, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219284, "dur": 67, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219376, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219380, "dur": 105, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219503, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139219508, "dur": 15058, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139234677, "dur": 3, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139234683, "dur": 342, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139235113, "dur": 558, "ph": "X", "name": "ProcessMessages 4157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237067, "dur": 92, "ph": "X", "name": "ReadAsync 4157", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237164, "dur": 413, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237674, "dur": 118, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237796, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237800, "dur": 140, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237945, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139237948, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238014, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238083, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238273, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238277, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238607, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238781, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139238787, "dur": 487, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239280, "dur": 65, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239349, "dur": 197, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239551, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239560, "dur": 67, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239682, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239689, "dur": 183, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239914, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139239920, "dur": 103, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240028, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240087, "dur": 411, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240552, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240612, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240757, "dur": 7, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139240768, "dur": 143, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241005, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241011, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241088, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241092, "dur": 281, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241426, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241431, "dur": 122, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241558, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241562, "dur": 243, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139241857, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242019, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242025, "dur": 130, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242160, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242220, "dur": 221, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242500, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242505, "dur": 86, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242596, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139242600, "dur": 427, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243040, "dur": 167, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243262, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243266, "dur": 90, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243413, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139243460, "dur": 7915, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251387, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251504, "dur": 8, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251514, "dur": 255, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251777, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139251825, "dur": 916, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139252746, "dur": 600362, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139853119, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139853125, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139853201, "dur": 2430, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139855635, "dur": 7963, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139863608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139863613, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139863685, "dur": 9, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139863697, "dur": 1862, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139865564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139865566, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139865619, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342139865642, "dur": 799520, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140665174, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140665181, "dur": 169, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140665359, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140665367, "dur": 1232, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140666607, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140666611, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140666729, "dur": 71, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140666808, "dur": 1868, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140668685, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140668690, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140668772, "dur": 853, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751342140669633, "dur": 8721, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140706679, "dur": 1403, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21056, "tid": 8589934592, "ts": 1751342139149419, "dur": 177339, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751342139326764, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751342139326778, "dur": 3957, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140708085, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21056, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21056, "tid": 4294967296, "ts": 1751342139114040, "dur": 1566011, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751342139123070, "dur": 14925, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751342140680325, "dur": 6480, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751342140684155, "dur": 146, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751342140686936, "dur": 22, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140708098, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751342139179266, "dur": 55, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342139179377, "dur": 3554, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342139182962, "dur": 1281, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342139184381, "dur": 160, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751342139184542, "dur": 2103, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342139186746, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1751342139186824, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139186889, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139186955, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187025, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187090, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187155, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187223, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187288, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187348, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187413, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187473, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187535, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187602, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187663, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187723, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187900, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139187966, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188032, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188095, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188162, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188224, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188287, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188346, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188406, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188468, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188529, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188590, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188649, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188708, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188766, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188825, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139188891, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189052, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189119, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189178, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189237, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189297, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189356, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189415, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189475, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189536, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189595, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189661, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189720, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189780, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189839, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189897, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139189959, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190019, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190104, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190165, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190224, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190283, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190341, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190400, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190459, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190517, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190577, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190637, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190697, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190758, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190821, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190881, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139190943, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139191008, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139191069, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139191221, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139191285, "dur": 2441, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139193739, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139193802, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139193867, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139193927, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139193985, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194045, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194104, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194165, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194223, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194282, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194344, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194404, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194466, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194524, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194622, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194684, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194743, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194807, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194866, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194926, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139194984, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195042, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195102, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195160, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195218, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195278, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195336, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195404, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195468, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195528, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195587, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195680, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195742, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195802, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195861, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195920, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139195980, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196040, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196098, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196155, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196213, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196270, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196329, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196388, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196447, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196505, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196561, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196641, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196700, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196762, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196823, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196881, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196939, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139196998, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197056, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197115, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197173, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197242, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197316, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197376, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197435, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197496, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197672, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197846, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197908, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139197969, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139198030, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139198088, "dur": 1347, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199449, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199513, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199582, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199723, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199785, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139199927, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200033, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200444, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200552, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200648, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200732, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200897, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139200998, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201072, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201169, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201340, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201418, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201537, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201608, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139201922, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202025, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202122, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202346, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_FF44434556825F42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202428, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202506, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202584, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202701, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202765, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139202888, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203058, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203130, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203201, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203319, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203418, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203484, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203600, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203749, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_22F664961FC0158D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203828, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139203903, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204007, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204077, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204154, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204227, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204364, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_39D3266F6029657A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204447, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204522, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204604, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204674, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204743, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204821, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139204943, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205043, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205117, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205188, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205249, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205314, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205377, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205513, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205582, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205656, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205736, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205812, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205878, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139205942, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206074, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_BF8650D11F8E519A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206145, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206244, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206319, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206390, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206462, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206536, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206698, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_514B12BC0D1D8DB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206767, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206885, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139206955, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139207028, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139207101, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139207174, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139207890, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_06973127A197C649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139207992, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208073, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208147, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208217, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208289, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208369, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208521, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_CE7BFB6B1BFC18F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208599, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208681, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208752, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208832, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208904, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139208978, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209133, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_900B338E31D9525C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209326, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209414, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209486, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209557, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209634, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209717, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209859, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_DC1879B0E64A8F15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139209929, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210015, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210087, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210163, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210240, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210355, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210530, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_1315E0A407C70415.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210599, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210687, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210744, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210841, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139210908, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211053, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211112, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211172, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211267, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211328, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211455, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211551, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211613, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211764, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211912, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751342139211976, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139212106, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139214971, "dur": 895, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216021, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216132, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216223, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216413, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216483, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216574, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216647, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139216785, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139217707, "dur": 182, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139217992, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218132, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218197, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218297, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218424, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218601, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751342139218863, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139219055, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751342139219164, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751342139219306, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751342139219431, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751342139186726, "dur": 32917, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342139219658, "dur": 1447217, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140666877, "dur": 1073, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140667951, "dur": 127, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140668079, "dur": 111, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140668190, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140668339, "dur": 74, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140668605, "dur": 90, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140668716, "dur": 1830, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751342139185514, "dur": 34157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139219689, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139220949, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139221165, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139221425, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139221734, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139221837, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139221941, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139222127, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139222334, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751342139222483, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751342139222728, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751342139222960, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139223011, "dur": 515, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751342139223529, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139224856, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139225842, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139226765, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139227836, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139229157, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\SignalTrack.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751342139228760, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139230428, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139231292, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139232239, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139233007, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139233072, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139233132, "dur": 1651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139234784, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139234939, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139236399, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139237373, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139237484, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139237769, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139238677, "dur": 746, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139239464, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139239825, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139240427, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139240563, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139240906, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139241015, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139241501, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139241964, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139242234, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139242443, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139243216, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139243436, "dur": 1665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139245267, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139245481, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139246573, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139246934, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139248316, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139248517, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139249211, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751342139249575, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139250217, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139251470, "dur": 112, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751342139252544, "dur": 600648, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751342139863453, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751342139863435, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751342139863710, "dur": 1992, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751342139865708, "dur": 801249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139185636, "dur": 34057, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139219698, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139220542, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139220644, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139220750, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139220984, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139221225, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139221397, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139221708, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139221830, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139222052, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139222119, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139222373, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751342139222538, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139222609, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751342139222838, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751342139223178, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751342139223412, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751342139223584, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139224686, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139225606, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139226570, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139227426, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139228328, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139229170, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\InstrumentationPoint.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751342139229170, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139230830, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139231689, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139232518, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139234173, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139234763, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139234926, "dur": 1483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139236410, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139237328, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139237501, "dur": 1880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139239381, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139239474, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139239555, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139239727, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751342139239824, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139240470, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139240588, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139240905, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139240964, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751342139241253, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751342139242045, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139242241, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139242506, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139243256, "dur": 1699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139244955, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139245252, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139245954, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139246523, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139246580, "dur": 1549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139248129, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139248286, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139249194, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751342139250248, "dur": 1416662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139185719, "dur": 33993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139219720, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139220943, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139221027, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139221214, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139221292, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139221461, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139221689, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139221874, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139222067, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139222485, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139223031, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139223118, "dur": 11659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139234920, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139235108, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139235294, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139236434, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139236642, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139237438, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139238016, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139240758, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139240955, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139241366, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139243214, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139243414, "dur": 1430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139244988, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139245221, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751342139245980, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139246576, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139248130, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139248290, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139249213, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751342139249596, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751342139250266, "dur": 1416656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139185832, "dur": 33930, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139219770, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139220708, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139220936, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139221053, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139221337, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139221466, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139221524, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139221590, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139222019, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139222137, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139222237, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751342139222415, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139222540, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139222740, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751342139223087, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751342139223950, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Toolbar\\ToolbarButton.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751342139223483, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139225068, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139226362, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139227681, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139229123, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Animation\\AnimationOutputWeightProcessor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751342139228901, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139230606, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139231750, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139232609, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139234384, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139234755, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139234933, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751342139235117, "dur": 1268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139236439, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139237352, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139237482, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139239391, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139239502, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139239553, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139239731, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751342139239852, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139240459, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139240579, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139240883, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139241002, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139241495, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139241950, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139242233, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139242500, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139243191, "dur": 1797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139244989, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139245281, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139245983, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139246549, "dur": 1590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139248140, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139248297, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139249185, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751342139250240, "dur": 1416655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139185945, "dur": 33873, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139219826, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751342139220959, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139221111, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751342139221410, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139221496, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751342139221838, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751342139222074, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751342139222247, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751342139222500, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139222772, "dur": 443, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751342139223217, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751342139223430, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751342139223665, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139225085, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139226106, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139227020, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139227915, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139229180, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751342139228926, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139230438, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139231260, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139232212, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139232929, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139234204, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139234767, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139234918, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139236414, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139237343, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139237489, "dur": 1886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139239381, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139239967, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139240448, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139240577, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139241004, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139241496, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139241965, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139242215, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139242491, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139243188, "dur": 1768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139244957, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139245214, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139245948, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139246536, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139248109, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139248277, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139249179, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751342139250250, "dur": 1416627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139186000, "dur": 33834, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139219839, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139220703, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139221001, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139221153, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139221360, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139221413, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139221574, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139221928, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139222036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139222089, "dur": 633, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751342139222727, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751342139222895, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139223050, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751342139223347, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139223409, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751342139223521, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139224800, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139225927, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139227288, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139229185, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsReflectionUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751342139228520, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139230083, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139230958, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139231856, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139232723, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139233485, "dur": 1273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139234758, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139234920, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751342139235087, "dur": 1330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139236417, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139237351, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139237517, "dur": 1862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139239379, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139239473, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139239603, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139239734, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139239813, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139240413, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139240561, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139240890, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139240979, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751342139241259, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751342139241965, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139242153, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139242269, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139242513, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139243193, "dur": 1758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139244952, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139245231, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139245947, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139246533, "dur": 1557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139248150, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139248311, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139249173, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139250226, "dur": 613222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751342139863504, "dur": 801707, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751342139863451, "dur": 801763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751342140665259, "dur": 1477, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751342139186896, "dur": 33137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139220041, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139220998, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139221251, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139221413, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139221505, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139221809, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139221994, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139222046, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139222237, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751342139222486, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139222613, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139222712, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751342139222894, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139222995, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139223204, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751342139223295, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139223986, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Solution.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751342139223490, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139225368, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139226783, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139229186, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\HashSetPool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751342139228028, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139229986, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139231568, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139232613, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139233776, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139234760, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139234910, "dur": 1496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139236406, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139237340, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139237493, "dur": 1884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139239378, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139239475, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139239564, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139239737, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139239821, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139240418, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139240478, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139240581, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139240892, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139241053, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139241504, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139241989, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139242239, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139242444, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139243165, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139243221, "dur": 1741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139244963, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139245216, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139245952, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139246543, "dur": 1559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139248102, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139248281, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139249214, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751342139250235, "dur": 1416631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139186188, "dur": 33693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139219886, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139220896, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139221099, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139221193, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139221432, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139221555, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139221998, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139222245, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139222360, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139222418, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139222573, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139222679, "dur": 886, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751342139224184, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ItemGui\\TimelineItemGUI.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751342139223567, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139225088, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139225922, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139226910, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139227755, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139229166, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\INotifiedCollectionItem.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751342139228625, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139230415, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139231340, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139232257, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139232992, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139233151, "dur": 1593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139234745, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139234890, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139236393, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139237338, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139237471, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139238204, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751342139239581, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139239864, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139240450, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139240590, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139240884, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139240956, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751342139241385, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751342139242487, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139243190, "dur": 1754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139244944, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139245278, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139245974, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139246539, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139248120, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139248307, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139249169, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751342139250273, "dur": 1416692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139186237, "dur": 33657, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139219899, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139220757, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139220953, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139221239, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139221401, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139221502, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139221708, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139222048, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751342139222206, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751342139222382, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751342139222675, "dur": 639, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751342139223471, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139224832, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139225650, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139226937, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139227848, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139229136, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_FontAssetUtilities.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751342139228964, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139230471, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139231390, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139232396, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139234213, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139234756, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139234908, "dur": 1528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139236437, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139237362, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139237506, "dur": 1866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139239380, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139239706, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751342139239822, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139240428, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139240559, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139240880, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139241014, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139241486, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139241959, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139242286, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139242451, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139243195, "dur": 1771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139244968, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139245297, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139245950, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139246525, "dur": 1571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139248097, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139248268, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139249199, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751342139250254, "dur": 1416747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139186321, "dur": 33588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139219914, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139220742, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139220911, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139221013, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139221163, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139221333, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139221518, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139221845, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139222060, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139222198, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751342139222325, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751342139222544, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751342139222700, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751342139223161, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751342139223350, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751342139223502, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139224735, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139226019, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139227522, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139229178, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloner.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751342139228642, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139230190, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139231109, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139232056, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139232839, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139233668, "dur": 1117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139234786, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139234905, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139236395, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139237355, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139237511, "dur": 1876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139239388, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139239462, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139239556, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139239686, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139239751, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751342139239902, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139240439, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139240567, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139240886, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139241002, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139241500, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139241962, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139242216, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139242445, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139243180, "dur": 1768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139244949, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139245217, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139245995, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139246552, "dur": 1601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139248153, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139248291, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139249178, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751342139250256, "dur": 1416683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139186423, "dur": 33516, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139219950, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139220996, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139221224, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139221606, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139221990, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139222182, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139222484, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751342139222754, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751342139222980, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751342139223179, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751342139223342, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139223411, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751342139223945, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139225199, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139226211, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139227126, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139229171, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\MultiplicationHandler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751342139228013, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139230008, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139231996, "dur": 799, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UnityConfigurationChecker.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751342139231229, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139232848, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139233988, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139234776, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139234930, "dur": 1482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139236412, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139237348, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139237478, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139238046, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751342139239524, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139239729, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_DA88E6A3E9EB6155.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751342139239836, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139240419, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139240582, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139240901, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139241007, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139241486, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139241545, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139241955, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139242217, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139242449, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139243171, "dur": 1829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139245000, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139245279, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139245982, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139246544, "dur": 1590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139248135, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139248313, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139249181, "dur": 1037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751342139250268, "dur": 1416661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139187359, "dur": 32758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139220122, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139220972, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139221083, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139221331, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139221442, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139221814, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139221932, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139222209, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139222350, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139222456, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139222517, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751342139222792, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751342139222957, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139223017, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751342139223452, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751342139223603, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139225049, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139226100, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139227347, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139229168, "dur": 701, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\AnimationPreviewUtilities.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751342139228656, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139231035, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139232280, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139232981, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139233133, "dur": 1617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139234751, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139234889, "dur": 1500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139236389, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139237332, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139237444, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139237732, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751342139238608, "dur": 1895, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139240609, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751342139240902, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751342139241835, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139241994, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139242258, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139242488, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139243207, "dur": 1732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139244940, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139245222, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139245991, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139246555, "dur": 1537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139248093, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139248271, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139249164, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139249217, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751342139250229, "dur": 1416663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139186603, "dur": 33371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139219980, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139220688, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139220777, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221021, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139221097, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221231, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221377, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221705, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221793, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139221988, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139222051, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139222331, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751342139222610, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139222674, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751342139222977, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751342139223198, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751342139223434, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751342139223521, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139223591, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139224959, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139226123, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139227441, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139229183, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsWeakReferenceConverter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751342139228554, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139229987, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139230927, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139231926, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139232736, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139233598, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139234773, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139234925, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139236401, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139237324, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139237452, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139237719, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139238589, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751342139239510, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139239742, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751342139240030, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751342139240862, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139241061, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139241492, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139241953, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139242270, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139242462, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139243166, "dur": 1788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139244954, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139245225, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139245944, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139246579, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139248106, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139248275, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139249201, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751342139250247, "dur": 1416702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139186724, "dur": 33267, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139219995, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139221034, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139221270, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139221411, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139221491, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139221801, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751342139222325, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751342139222563, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751342139222860, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751342139223077, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139223151, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751342139223469, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751342139223573, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139223641, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139224839, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139225733, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139226627, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139227470, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139229163, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Strings.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751342139228388, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139230027, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139231235, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139232230, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139232946, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139233600, "dur": 1152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139234753, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139234887, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139236411, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139237366, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139237491, "dur": 1898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139239390, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139239468, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139239550, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139239733, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139239819, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139240417, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139240562, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139240896, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139241022, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139241527, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139241966, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139242277, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139242454, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139243235, "dur": 1748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139244984, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139245274, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139245976, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139246537, "dur": 1550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139248133, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139248300, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139249167, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751342139250231, "dur": 1416652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139186774, "dur": 33233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139220013, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139220930, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139220994, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139221260, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139221409, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139221512, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139221718, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139221835, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139221949, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139222057, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139222125, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139222204, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139222542, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751342139222762, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751342139222906, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751342139223160, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751342139223402, "dur": 555, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751342139223958, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139225175, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139226006, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139226997, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139227851, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139229145, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Audio\\AudioPlayableAsset.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751342139228774, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139230249, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139231559, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139232504, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139234128, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139234769, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139234914, "dur": 1468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139236435, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139237326, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139237458, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139237698, "dur": 920, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139238627, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751342139240305, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139240474, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751342139240733, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751342139241839, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139242274, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139242457, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139243225, "dur": 1716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139244942, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139245219, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139245940, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139246558, "dur": 1540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139248099, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139248274, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139249187, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751342139250251, "dur": 1416674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139186080, "dur": 33789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139219874, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139220733, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139221061, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139221215, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139221381, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139221488, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139221682, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139221892, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139222075, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751342139222413, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751342139222545, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751342139222678, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751342139223141, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751342139223630, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139224807, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139225701, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139226856, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139227670, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139229165, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsExceptions.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751342139228526, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139230247, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139231630, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139232619, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139233361, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139234802, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139234899, "dur": 1525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139236425, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139237359, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139237487, "dur": 1897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139239385, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139239461, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139239558, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139239680, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139239741, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751342139239889, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139240430, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139240558, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139240911, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139241008, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139241517, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139241973, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139242252, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139242473, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139243174, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139244995, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139245255, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139245957, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139246548, "dur": 1552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139248100, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139248270, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139249192, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751342139250242, "dur": 1416638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139187051, "dur": 33005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139220063, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139221025, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139221235, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139221393, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139221490, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139221723, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139222020, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139222085, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139222175, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139222263, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139222317, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139222520, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139222846, "dur": 450, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751342139223301, "dur": 11334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751342139234800, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139234901, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139236387, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139237350, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139237478, "dur": 1913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139239392, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139239466, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139239595, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139239740, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139239817, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139240424, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139240568, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139240894, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139241006, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139241498, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139241947, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139242249, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139242493, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139243177, "dur": 1798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139244976, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139245284, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139245966, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139246541, "dur": 1553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139248095, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139248272, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139249183, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751342139250238, "dur": 1416676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139187119, "dur": 32956, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139220082, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139221011, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139221167, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139221346, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139221463, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139221744, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139221863, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139222147, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751342139222420, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139222540, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751342139222644, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139222736, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751342139222915, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139223088, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751342139223463, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751342139223591, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139223677, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139225371, "dur": 1564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Description\\StateMacroDescriptor.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751342139224939, "dur": 2664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139227605, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139229146, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\WarnBeforeEditingAttribute.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751342139228487, "dur": 1939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139230427, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139231573, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139232359, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139233366, "dur": 1395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139234761, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139234915, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139236404, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139237345, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139237484, "dur": 1898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139239383, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139239465, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139239552, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139239741, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139239811, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139240415, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139240565, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139240903, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139241012, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139241515, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139241990, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139242266, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139242466, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139243185, "dur": 1787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139244973, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139245233, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139245959, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139246551, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139248123, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139248283, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139249174, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751342139250246, "dur": 1416653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139187193, "dur": 32897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139220096, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139221082, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139221195, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139221302, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139221371, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139221447, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139221702, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139221791, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139221906, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139222161, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139222320, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751342139222730, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751342139222917, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139222993, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751342139223374, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139223440, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751342139223541, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139225400, "dur": 2902, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\ProfileTable.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751342139224743, "dur": 3838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139229155, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Decorators\\ValueAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751342139228582, "dur": 2033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139230618, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139231815, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139232495, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139234222, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139234747, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139234894, "dur": 1513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139236408, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139237341, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139237521, "dur": 1865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139239387, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139239464, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139239559, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139239746, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139239856, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139240457, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139240591, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139240897, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139240998, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139241488, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139241949, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139242134, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139242244, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139242503, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139243187, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139244964, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139245224, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139245938, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139246527, "dur": 1579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139248107, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139248309, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139249176, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751342139250236, "dur": 1416666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139187264, "dur": 32839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139220109, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139221003, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139221159, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139221345, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139221532, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139222098, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139222239, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139222489, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139223166, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751342139223399, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751342139223972, "dur": 812, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\TimelineKeyboardNavigation.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751342139223532, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139225467, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139226791, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139228042, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139229141, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\FontFeatureCommonGPOS.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751342139228975, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139230456, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139231288, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139232128, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139232916, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139234307, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139234772, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139234934, "dur": 1510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139236444, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139237346, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139237442, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139237976, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751342139239261, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139239385, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139239651, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139240059, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751342139241356, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139241524, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139241961, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139242295, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139242447, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139243176, "dur": 1793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139244970, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139245239, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139245962, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139246575, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751342139246930, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751342139247974, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139248127, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139248287, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139249170, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139250224, "dur": 83331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751342139333557, "dur": 1333388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139186510, "dur": 33449, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139219965, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139220976, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139221136, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139221304, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139221357, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139221563, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139221937, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139222005, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139222361, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139222728, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751342139222959, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751342139223303, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751342139223445, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751342139223558, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139224729, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139225567, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139226458, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139227377, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139229175, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphNesterElement.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751342139228304, "dur": 1830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139230135, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139231089, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139231979, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139232801, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139233511, "dur": 1231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139234742, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139234892, "dur": 1510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139236403, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139237481, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751342139238025, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751342139239433, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139239739, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1751342139239873, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139240454, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139240585, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139240888, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139241005, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139241536, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139241977, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139242247, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139242497, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139243182, "dur": 1797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139244979, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139245268, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139245955, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139246556, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139248112, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139248321, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139249190, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751342139250244, "dur": 1416661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139187429, "dur": 32710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139220149, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139221053, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139221122, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139221294, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139221467, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139221734, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139221808, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139221880, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139222282, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751342139222540, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751342139222659, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139222765, "dur": 675, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751342139223442, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751342139223569, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139224827, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139226039, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139227117, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139229162, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\MemberUtility.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751342139228025, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139229820, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139230848, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139231717, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139232529, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139234283, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139234771, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139234923, "dur": 1473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139236397, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139237336, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139237466, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751342139237797, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139238595, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751342139239629, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139239726, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751342139239881, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139240481, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139240574, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139241001, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139241503, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139242001, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139242262, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139242476, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139243199, "dur": 1778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139244978, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139245247, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139245969, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139246553, "dur": 1578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139248132, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139248294, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139249197, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751342139250276, "dur": 1416699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139187541, "dur": 32616, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139220162, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139220962, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139221126, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139221318, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139221470, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139221773, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139221894, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139221982, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139222184, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139222415, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751342139222684, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751342139222851, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139222921, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751342139223030, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139223187, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751342139223545, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139223616, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139225189, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139226423, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139227348, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139229165, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerExitMListener.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751342139228214, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139230083, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139231578, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139232622, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139233534, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139234806, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139234895, "dur": 1520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139236416, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139237330, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139237446, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139237968, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751342139239560, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139239649, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751342139239741, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139240229, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751342139240490, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751342139242095, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139242264, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139242482, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139243172, "dur": 1786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139244961, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139245251, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139245984, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139246529, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139248104, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139248306, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139249182, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139250223, "dur": 80620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139333067, "dur": 440, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 23, "ts": 1751342139330845, "dur": 2667, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751342139333512, "dur": 1333420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139187612, "dur": 32558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139220171, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139220909, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139220973, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139221076, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139221161, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139221316, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139221511, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139221817, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139222125, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139222272, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751342139222362, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139222422, "dur": 475, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751342139222945, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1751342139223089, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751342139223218, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751342139223424, "dur": 500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751342139224126, "dur": 654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_EditorResourceManager.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751342139223927, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139225538, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139226360, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139227351, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139229147, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnBecameInvisibleMessageListener.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751342139228234, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139230295, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\MiscHelpers.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751342139229854, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139231407, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139232329, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139233136, "dur": 1612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139234749, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139234897, "dur": 1493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139236390, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139237333, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139237441, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751342139237719, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139237816, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751342139238576, "dur": 1027, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139239736, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139239815, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139240426, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139240602, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139240888, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139241010, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139241490, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139241957, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139242213, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139242453, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139243168, "dur": 1766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139244981, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139245227, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139245942, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139246546, "dur": 1567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139248114, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139248278, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139249216, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751342139250232, "dur": 1416640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751342140674099, "dur": 3780, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21056, "tid": 1040, "ts": 1751342140708814, "dur": 3060, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21056, "tid": 1040, "ts": 1751342140711929, "dur": 4401, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21056, "tid": 1040, "ts": 1751342140702488, "dur": 15272, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}