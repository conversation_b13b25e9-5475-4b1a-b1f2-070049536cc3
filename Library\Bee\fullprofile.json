{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17072, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17072, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17072, "tid": 1222, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17072, "tid": 1222, "ts": 1751282283123346, "dur": 1957, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283137730, "dur": 2923, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17072, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17072, "tid": 1, "ts": 1751282279859176, "dur": 9103, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751282279868283, "dur": 73122, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17072, "tid": 1, "ts": 1751282279941418, "dur": 69972, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283140673, "dur": 29, "ph": "X", "name": "", "args": {}}, {"pid": 17072, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279855181, "dur": 26744, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279881930, "dur": 3224560, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279885238, "dur": 8555, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279893802, "dur": 3273, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279897081, "dur": 618, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279897710, "dur": 57, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279897770, "dur": 135, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279897910, "dur": 4, "ph": "X", "name": "ProcessMessages 1451", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279897916, "dur": 81, "ph": "X", "name": "ReadAsync 1451", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898005, "dur": 4, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898012, "dur": 75, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898091, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898100, "dur": 132, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898244, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898347, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898351, "dur": 77, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898435, "dur": 3, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898441, "dur": 97, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898542, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898546, "dur": 83, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898636, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898642, "dur": 64, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898710, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898714, "dur": 83, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898804, "dur": 3, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898810, "dur": 98, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898913, "dur": 3, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279898917, "dur": 78, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899002, "dur": 4, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899009, "dur": 103, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899119, "dur": 4, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899125, "dur": 101, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899233, "dur": 3, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899239, "dur": 83, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899327, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899331, "dur": 90, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899428, "dur": 3, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899434, "dur": 89, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899527, "dur": 3, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899531, "dur": 73, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899611, "dur": 3, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899617, "dur": 79, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899701, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899704, "dur": 78, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899789, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899795, "dur": 91, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899891, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899895, "dur": 82, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899984, "dur": 3, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279899990, "dur": 80, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900075, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900079, "dur": 75, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900161, "dur": 3, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900167, "dur": 90, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900261, "dur": 2, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900265, "dur": 74, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900345, "dur": 4, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900351, "dur": 93, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900448, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900453, "dur": 73, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900535, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900541, "dur": 84, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900629, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900633, "dur": 79, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900718, "dur": 4, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900725, "dur": 88, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900818, "dur": 3, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900824, "dur": 78, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900909, "dur": 3, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279900915, "dur": 107, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901029, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901033, "dur": 88, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901128, "dur": 4, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901134, "dur": 80, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901219, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901222, "dur": 78, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901307, "dur": 4, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901313, "dur": 89, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901407, "dur": 2, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901411, "dur": 76, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901494, "dur": 3, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901500, "dur": 91, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901595, "dur": 2, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901599, "dur": 77, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901683, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901689, "dur": 86, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901780, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901784, "dur": 70, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901861, "dur": 3, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901867, "dur": 81, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901952, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279901956, "dur": 83, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902046, "dur": 3, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902052, "dur": 94, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902152, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902156, "dur": 78, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902241, "dur": 4, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902248, "dur": 72, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902325, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902328, "dur": 83, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902419, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902424, "dur": 92, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902521, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902525, "dur": 83, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902614, "dur": 4, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902621, "dur": 84, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902710, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902714, "dur": 78, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902799, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902805, "dur": 87, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902897, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902900, "dur": 83, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902990, "dur": 4, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279902997, "dur": 93, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903094, "dur": 3, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903100, "dur": 73, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903181, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903187, "dur": 79, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903270, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903274, "dur": 81, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903362, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279903368, "dur": 717, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904095, "dur": 4, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904102, "dur": 203, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904311, "dur": 6, "ph": "X", "name": "ProcessMessages 2433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904319, "dur": 126, "ph": "X", "name": "ReadAsync 2433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904454, "dur": 5, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904462, "dur": 228, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904698, "dur": 5, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904705, "dur": 162, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904876, "dur": 6, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279904885, "dur": 107, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905000, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905006, "dur": 99, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905112, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905118, "dur": 117, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905243, "dur": 4, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905250, "dur": 106, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905362, "dur": 4, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905369, "dur": 82, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905458, "dur": 3, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905464, "dur": 103, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905574, "dur": 4, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905581, "dur": 101, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905688, "dur": 4, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905695, "dur": 101, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905803, "dur": 4, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905810, "dur": 86, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905903, "dur": 7, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279905913, "dur": 116, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906036, "dur": 4, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906043, "dur": 108, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906158, "dur": 4, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906165, "dur": 125, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906295, "dur": 3, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906299, "dur": 72, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906378, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906384, "dur": 81, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906470, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906474, "dur": 67, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906550, "dur": 3, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906556, "dur": 73, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906634, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906637, "dur": 61, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906705, "dur": 3, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906711, "dur": 61, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906776, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906779, "dur": 65, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906850, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906856, "dur": 72, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906933, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279906937, "dur": 67, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907011, "dur": 3, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907017, "dur": 60, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907080, "dur": 3, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907085, "dur": 56, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907146, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907150, "dur": 58, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907214, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907219, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907307, "dur": 2, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907311, "dur": 67, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907385, "dur": 4, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907395, "dur": 81, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907481, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907485, "dur": 117, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907611, "dur": 3, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907618, "dur": 91, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907714, "dur": 3, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907718, "dur": 94, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907820, "dur": 3, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907826, "dur": 85, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907915, "dur": 3, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907920, "dur": 66, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279907993, "dur": 6, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908003, "dur": 62, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908070, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908073, "dur": 71, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908151, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908157, "dur": 76, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908237, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908242, "dur": 65, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908314, "dur": 3, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908320, "dur": 64, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908388, "dur": 9, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908404, "dur": 83, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908491, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908495, "dur": 57, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908559, "dur": 3, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908565, "dur": 83, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908653, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908657, "dur": 66, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908730, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908736, "dur": 76, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908817, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908823, "dur": 65, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908895, "dur": 4, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908901, "dur": 62, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908968, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279908972, "dur": 68, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909047, "dur": 3, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909052, "dur": 74, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909133, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909137, "dur": 74, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909219, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909225, "dur": 71, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909300, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909305, "dur": 64, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909376, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909386, "dur": 73, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909463, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909467, "dur": 67, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909541, "dur": 3, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909550, "dur": 76, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909631, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909635, "dur": 66, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909709, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909715, "dur": 74, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909794, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909799, "dur": 62, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909868, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909875, "dur": 81, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909960, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279909964, "dur": 68, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910039, "dur": 3, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910045, "dur": 73, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910123, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910127, "dur": 59, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910197, "dur": 3, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910202, "dur": 76, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910283, "dur": 5, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910290, "dur": 109, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910408, "dur": 4, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910415, "dur": 84, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910502, "dur": 4, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910509, "dur": 53, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910566, "dur": 2, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910570, "dur": 99, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910679, "dur": 4, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910685, "dur": 226, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910916, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279910921, "dur": 143, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911073, "dur": 7, "ph": "X", "name": "ProcessMessages 1333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911083, "dur": 91, "ph": "X", "name": "ReadAsync 1333", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911179, "dur": 3, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911184, "dur": 78, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911270, "dur": 4, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911277, "dur": 127, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911413, "dur": 5, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911421, "dur": 110, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911538, "dur": 5, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911546, "dur": 113, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911665, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911670, "dur": 82, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911759, "dur": 5, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911766, "dur": 81, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911852, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911856, "dur": 65, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911928, "dur": 4, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279911935, "dur": 81, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912020, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912024, "dur": 63, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912094, "dur": 3, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912101, "dur": 84, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912189, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912193, "dur": 85, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912285, "dur": 3, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912291, "dur": 72, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912367, "dur": 3, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912372, "dur": 61, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912438, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912442, "dur": 67, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912517, "dur": 5, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912525, "dur": 107, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912636, "dur": 2, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912640, "dur": 66, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912717, "dur": 4, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912723, "dur": 77, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912805, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912809, "dur": 57, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912873, "dur": 3, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912880, "dur": 92, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912976, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279912980, "dur": 65, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913054, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913060, "dur": 74, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913139, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913143, "dur": 65, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913215, "dur": 4, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913222, "dur": 130, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913356, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913360, "dur": 78, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913448, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913455, "dur": 67, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913525, "dur": 3, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913531, "dur": 60, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913596, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913601, "dur": 78, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913689, "dur": 3, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913695, "dur": 86, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913786, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913791, "dur": 74, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913872, "dur": 4, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913879, "dur": 83, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913967, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279913972, "dur": 64, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914043, "dur": 2, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914048, "dur": 84, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914137, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914141, "dur": 69, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914218, "dur": 3, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914228, "dur": 80, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914313, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914318, "dur": 70, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914395, "dur": 4, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914402, "dur": 81, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914487, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914492, "dur": 73, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914572, "dur": 4, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914579, "dur": 69, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914651, "dur": 3, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914657, "dur": 54, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914716, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914719, "dur": 69, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914796, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914806, "dur": 80, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914893, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914898, "dur": 87, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914992, "dur": 4, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279914999, "dur": 77, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915080, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915084, "dur": 67, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915158, "dur": 3, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915164, "dur": 70, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915237, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915243, "dur": 50, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915297, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915301, "dur": 63, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915371, "dur": 3, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915380, "dur": 71, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915456, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915461, "dur": 62, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915530, "dur": 3, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915536, "dur": 73, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915615, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915619, "dur": 68, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915695, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915701, "dur": 67, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915772, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915777, "dur": 65, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915849, "dur": 3, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915855, "dur": 82, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915941, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279915946, "dur": 61, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916014, "dur": 6, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916024, "dur": 68, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916096, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916101, "dur": 80, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916188, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916193, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916274, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916278, "dur": 63, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916349, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916355, "dur": 55, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916412, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916417, "dur": 48, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916469, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916472, "dur": 60, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916540, "dur": 3, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916545, "dur": 65, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916615, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916618, "dur": 65, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916691, "dur": 3, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916697, "dur": 72, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916773, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916777, "dur": 63, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916848, "dur": 3, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916854, "dur": 63, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916921, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279916925, "dur": 79, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917012, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917016, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917086, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917090, "dur": 413, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917512, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917520, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279917612, "dur": 628, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918247, "dur": 135, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918391, "dur": 32, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918433, "dur": 94, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918536, "dur": 6, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918547, "dur": 85, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918637, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918642, "dur": 83, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918731, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918736, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918826, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918836, "dur": 90, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918932, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279918940, "dur": 66, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919017, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919027, "dur": 79, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919125, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919132, "dur": 67, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919209, "dur": 6, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919219, "dur": 80, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919306, "dur": 4, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919313, "dur": 73, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919395, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919401, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919482, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919489, "dur": 76, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919571, "dur": 6, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919581, "dur": 73, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919660, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919667, "dur": 72, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919745, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919750, "dur": 62, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919820, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919824, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919902, "dur": 7, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919912, "dur": 73, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919991, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279919999, "dur": 90, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920093, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920098, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920173, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920179, "dur": 65, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920249, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920254, "dur": 53, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920312, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920316, "dur": 98, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920429, "dur": 8, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920443, "dur": 92, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920544, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920552, "dur": 70, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920632, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920641, "dur": 71, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920719, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920725, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920829, "dur": 6, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920843, "dur": 90, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920938, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279920942, "dur": 76, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921026, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921033, "dur": 57, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921095, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921104, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921179, "dur": 8, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921194, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921249, "dur": 5, "ph": "X", "name": "ProcessMessages 45", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921293, "dur": 87, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279921389, "dur": 636, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279922032, "dur": 102, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279922140, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279922144, "dur": 11451, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933602, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933609, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933679, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933682, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933743, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279933749, "dur": 304, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934059, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934063, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934142, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934145, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934205, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934279, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934286, "dur": 339, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934634, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934736, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279934742, "dur": 1790, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936539, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936547, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936614, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936618, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936679, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936683, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936820, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936868, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279936872, "dur": 623, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937505, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937512, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937576, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937582, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937661, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937666, "dur": 271, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937944, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279937948, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938026, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938032, "dur": 64, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938105, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938109, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938182, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938187, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938261, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938272, "dur": 318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938603, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938679, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938684, "dur": 266, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938957, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279938965, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939031, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939036, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939105, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939111, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939169, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939172, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939256, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939261, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939327, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939331, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939398, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939401, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939462, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939467, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939536, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939541, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939593, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939596, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939648, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939651, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939715, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939719, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939778, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939781, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939832, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939835, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939885, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939887, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939948, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279939952, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940020, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940022, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940082, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940087, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940152, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940155, "dur": 59, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940222, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940229, "dur": 70, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940304, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940307, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940422, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940480, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940485, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940542, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940622, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940674, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940678, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940736, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940739, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940793, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940796, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940846, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279940849, "dur": 571, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941425, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941430, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941479, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941482, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941690, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941751, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941813, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941818, "dur": 61, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941884, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941887, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941941, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941944, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279941998, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942003, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942080, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942085, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942155, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942158, "dur": 56, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942219, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942223, "dur": 51, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942283, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942287, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942355, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942358, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942418, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942421, "dur": 45, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942470, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942473, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942529, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942533, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942599, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942602, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942660, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942663, "dur": 312, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942981, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279942985, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943044, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943048, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943103, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943106, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943243, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943246, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943313, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943319, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943372, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943375, "dur": 213, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943596, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943649, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279943655, "dur": 1430, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945092, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945100, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945162, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945166, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945216, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945219, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945283, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945288, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945361, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945413, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945416, "dur": 192, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945617, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945672, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279945674, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946100, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946160, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946164, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946227, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946231, "dur": 424, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946663, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946668, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946738, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946746, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946803, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946806, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279946969, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947020, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947023, "dur": 671, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947701, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947705, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947772, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947775, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947846, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947850, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947905, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279947908, "dur": 183, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948100, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948154, "dur": 568, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948730, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948734, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948803, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279948806, "dur": 223, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949038, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949123, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949126, "dur": 494, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949628, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949632, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949697, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949700, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949773, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279949779, "dur": 1547, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279951332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279951335, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279951396, "dur": 1067, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282279952469, "dur": 2497030, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282449518, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282449525, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282449595, "dur": 2702, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282452303, "dur": 7920, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282460233, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282460238, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282460316, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282460324, "dur": 1730, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282462063, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282462067, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282462155, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282282462186, "dur": 623132, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283085331, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283085337, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283085416, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283085423, "dur": 1155, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283086585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283086588, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283086661, "dur": 50, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283086715, "dur": 2153, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283088876, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283088881, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283088972, "dur": 826, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17072, "tid": 12884901888, "ts": 1751282283089805, "dur": 16615, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283140707, "dur": 3732, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17072, "tid": 8589934592, "ts": 1751282279849506, "dur": 162001, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751282280011511, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17072, "tid": 8589934592, "ts": 1751282280011521, "dur": 2953, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283144444, "dur": 45, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17072, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17072, "tid": 4294967296, "ts": 1751282279813645, "dur": 3294656, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751282279822964, "dur": 14590, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751282283108614, "dur": 7455, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751282283112915, "dur": 164, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17072, "tid": 4294967296, "ts": 1751282283116353, "dur": 44, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283144493, "dur": 25, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751282279874927, "dur": 2465, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282279877411, "dur": 697, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282279878184, "dur": 88, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751282279878272, "dur": 1325, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282279882278, "dur": 9300, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279894093, "dur": 3597, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279897837, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279897995, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898324, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898515, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898606, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898772, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898878, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279898991, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899193, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899332, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899398, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899507, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899705, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899772, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899871, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279899949, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900160, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900222, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900440, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900600, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900715, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900811, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900887, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279900999, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_22F664961FC0158D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901112, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901197, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901305, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901493, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901590, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901664, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901766, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279901945, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902018, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902119, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902239, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902383, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902489, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902602, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902785, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902876, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279902984, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279903086, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279903269, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279903338, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279903438, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279904176, "dur": 142, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279904542, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279904803, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_900B338E31D9525C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905093, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905194, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905324, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905449, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_DC1879B0E64A8F15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905550, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905663, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905779, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279905997, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906117, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906359, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906462, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906539, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906636, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906768, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906834, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279906933, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907004, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907102, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907279, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907487, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907697, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907782, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907913, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279907997, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908137, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908240, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908311, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908376, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908472, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908657, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908731, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908791, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279908901, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909039, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909139, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909208, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909456, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909529, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909624, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909797, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279909947, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910022, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910117, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910361, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910495, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910586, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910778, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279910986, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911166, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911253, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911347, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911490, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911615, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911727, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279911828, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912023, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912189, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912269, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912367, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912441, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912508, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912606, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912689, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912788, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279912978, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913059, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913141, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913212, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913311, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913423, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913523, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913597, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913659, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913763, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279913878, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914029, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914122, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914197, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914292, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914399, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914459, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914568, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914661, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914736, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914795, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914886, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279914997, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915056, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915228, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915373, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915444, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915543, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915611, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915727, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915790, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915865, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279915931, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916056, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916196, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916267, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916370, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916440, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916588, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916672, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916754, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751282279916860, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751282279917023, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751282279879664, "dur": 37421, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282279917113, "dur": 3169664, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283086793, "dur": 839, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283087633, "dur": 438, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283088087, "dur": 68, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283088162, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283088669, "dur": 174, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283088885, "dur": 2464, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751282279879395, "dur": 37767, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279917193, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279917587, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279917679, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279918093, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279918215, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279918364, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279918471, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279918883, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279919311, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279919403, "dur": 425, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279919853, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279919913, "dur": 879, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751282279920794, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751282279920940, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751282279921798, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\SettingsProvider\\ProjectSettings\\LinkerPropertyProviderSettings.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751282279921083, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279922437, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279923352, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279924444, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279925541, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279927059, "dur": 1035, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\VariantCollection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751282279926415, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279928472, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279929393, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279930416, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279931064, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279933242, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279933688, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279934190, "dur": 2449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279936639, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279937559, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279937709, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751282279938414, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279939026, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751282279940249, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279940490, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279940848, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279941784, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279942139, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279942381, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279942655, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279943045, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279943337, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279945144, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279945390, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279946141, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279946769, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279947765, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279947941, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279948771, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751282279949670, "dur": 3137085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279881188, "dur": 36504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279917698, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279918050, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279918273, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279918442, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279918632, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279918883, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279918942, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279919138, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751282279919191, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279919279, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279919352, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279919455, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751282279919726, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279919872, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279919947, "dur": 489, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751282279920579, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751282279920857, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279920938, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751282279921185, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279922644, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279924853, "dur": 1465, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Windows\\Wizard.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751282279924224, "dur": 2491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279927094, "dur": 1065, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\AssetUpgrade\\TrackUpgrade.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751282279926715, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279928672, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279929743, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279931012, "dur": 1984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279932997, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279933734, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279934151, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279934236, "dur": 2378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279936615, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279937567, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279937694, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279938222, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751282279939637, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279939813, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279940034, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279940190, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279940322, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751282279940657, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751282279941306, "dur": 738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279942132, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279942375, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279942618, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279943093, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279943360, "dur": 1787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279945148, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279945353, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279946163, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279946753, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279947815, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279947889, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279948802, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751282279949680, "dur": 3137140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279881153, "dur": 36527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279917686, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279917960, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279918037, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279918161, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279918220, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279918707, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279919055, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279919115, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279919414, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279919585, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751282279919836, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751282279920151, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279920268, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279920429, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751282279920783, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279920893, "dur": 12667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751282279933562, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279933677, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279933776, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279934214, "dur": 2385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279936601, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279937580, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279937718, "dur": 1295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939013, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939141, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939378, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939549, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939756, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279939915, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279940026, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279940192, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279940325, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279940486, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279940566, "dur": 1199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279941765, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279942199, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279942424, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279942629, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279943073, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279943357, "dur": 1771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279945176, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279945406, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279946151, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279946712, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279947772, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279947937, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279948787, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751282279949668, "dur": 3137092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279879478, "dur": 37715, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279917198, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279917512, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279917647, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279917711, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279918366, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279918803, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279919077, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279919297, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279919376, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279919496, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751282279919660, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751282279919984, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751282279920226, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279920286, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751282279920924, "dur": 931, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751282279921857, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Filtering\\JsonFileFilterSchema.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751282279921857, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279923191, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279924150, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279925078, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279925915, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279927073, "dur": 1064, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextParsingUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751282279926794, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279928961, "dur": 1167, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\ControlPicker\\InputControlPicker.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751282279928739, "dur": 2040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279930779, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279932703, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279933704, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279934155, "dur": 2447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279936603, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279937557, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279937723, "dur": 1306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279939030, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279939171, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279939381, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279939588, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279939770, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279940077, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279940202, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279940312, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751282279940746, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751282279941830, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279942195, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279942386, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279942625, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279943077, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279943392, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279945160, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279945383, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279946147, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279946751, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279947756, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279947886, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279948769, "dur": 896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282279949666, "dur": 2510365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751282282460088, "dur": 625109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751282282460034, "dur": 625166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751282283085245, "dur": 1400, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751282279879855, "dur": 37485, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279917348, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279917590, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279917700, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279918008, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279918093, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279918317, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279918465, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279918735, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279919023, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279919124, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279919216, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751282279919408, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279919532, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279919589, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751282279919986, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279920093, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279920542, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279920766, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279920846, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279920915, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279920972, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279921037, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751282279921139, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279921217, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279922570, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279923727, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279924771, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279925706, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279927086, "dur": 1116, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\TimelineUndo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751282279926618, "dur": 2172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279928792, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279929923, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279930806, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279931605, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279932557, "dur": 1132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279933689, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279934161, "dur": 2431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279936593, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279937565, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279937730, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279939015, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279939138, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279939428, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279939554, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279939820, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279940042, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279940123, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279940198, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279940330, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279940486, "dur": 1277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279941764, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279942160, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279942388, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279942617, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279943060, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279943374, "dur": 1783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279945157, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279945394, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279946143, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279946710, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279947790, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279947906, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279948806, "dur": 891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751282279949697, "dur": 3137164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279879739, "dur": 37540, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279917284, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279917542, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279917967, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279918164, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279918318, "dur": 348, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279918676, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279919229, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279919343, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279919406, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279919672, "dur": 1238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751282279920963, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751282279921366, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279922653, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279923545, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279924445, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279925386, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279927105, "dur": 1077, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorDelayedAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751282279926290, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279928374, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279929639, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279930854, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279932237, "dur": 1454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279933692, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279934197, "dur": 2443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279936640, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279937552, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279937679, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279938148, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751282279939344, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279939773, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751282279940194, "dur": 2047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751282279942242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279942412, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279942640, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279943054, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279943382, "dur": 1769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279945153, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279945356, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279946152, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279946717, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279947759, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279947967, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279948780, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751282279949728, "dur": 3137173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279879839, "dur": 37453, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279917298, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279917637, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279917781, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279917924, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279918046, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279918156, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279918381, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279918448, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279918756, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751282279919310, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279919420, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751282279919639, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279919740, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751282279920003, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751282279920071, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751282279920250, "dur": 1167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751282279921420, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279923039, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279924173, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279925086, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279925905, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279927084, "dur": 1075, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ScrollbarEventHandler.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751282279926864, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279928837, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279929833, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279930791, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279931557, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279932294, "dur": 1402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279933696, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279934176, "dur": 2431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279936610, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279937573, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279937706, "dur": 1316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279939022, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279939145, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279939366, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279939551, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279940240, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279940354, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279940499, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279941766, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279942146, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279942402, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279942651, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279943043, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279943376, "dur": 1759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279945136, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279945364, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279946159, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279946763, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279947749, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279947888, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279948796, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751282279949684, "dur": 3137159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279879925, "dur": 37442, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279917374, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279917643, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279917748, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279918175, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279918295, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279918502, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279918616, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279918849, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279919035, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279919167, "dur": 1243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279920422, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279920704, "dur": 13253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279933959, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279934155, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279934265, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279934616, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279936637, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279936856, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279937668, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279937962, "dur": 1996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279939959, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279940323, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279940794, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279943221, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279943403, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279943622, "dur": 1410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279945187, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751282279945427, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751282279946191, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279946729, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279947784, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279947917, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279948765, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751282279949678, "dur": 3137174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279880036, "dur": 37351, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279917395, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751282279917631, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279917728, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751282279918041, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279918148, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751282279918329, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279918426, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751282279918595, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279918688, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751282279919039, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279919164, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751282279919324, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279919421, "dur": 906, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751282279920380, "dur": 911, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751282279921293, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279922545, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279923738, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279925081, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279925921, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279927072, "dur": 1146, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Compatibility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751282279926867, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279928896, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279929859, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279930759, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279931799, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279932441, "dur": 1226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279933732, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279934223, "dur": 2397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279936621, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279937574, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279937704, "dur": 1316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939021, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939147, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939373, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939561, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939706, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279939759, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279940017, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279940218, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279940365, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279940492, "dur": 1259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279941765, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279941844, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279942153, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279942445, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279942627, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279943041, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279943347, "dur": 1792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279945140, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279945367, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279946139, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279946708, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279947768, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279947932, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279948816, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751282279949693, "dur": 3137162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279880129, "dur": 37286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279917423, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279917673, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279917775, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279918041, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279918123, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279918337, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279918394, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279918877, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279919224, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279919281, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751282279919654, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279919838, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751282279920056, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279920151, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279920209, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279920408, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279920462, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279920678, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279920898, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751282279921095, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279922776, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279924093, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279925146, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279927092, "dur": 1104, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsConverterRegistrar.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751282279926387, "dur": 2348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279928736, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279930241, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279931448, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279932512, "dur": 1172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279933685, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279934200, "dur": 2418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279936619, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279937566, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279937744, "dur": 1282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939027, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939142, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939383, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939603, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939709, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279939761, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279940058, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279940257, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279940327, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279940481, "dur": 1275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279941757, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279942207, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279942400, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279942641, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279943059, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279943351, "dur": 1827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279945179, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279945392, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279946166, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279946757, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279947763, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279947963, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279948786, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751282279949677, "dur": 3137088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279880177, "dur": 37265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279917450, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279917697, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279917795, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279918000, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279918086, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279918281, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279918369, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279918617, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279918863, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279918925, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279919027, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279919355, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751282279919863, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279919920, "dur": 717, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751282279920640, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751282279920907, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279921808, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\Tests\\TestFixture\\ScopedDisposable.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751282279921047, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279922500, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279923791, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279925337, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279927071, "dur": 1138, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Timeline.deprecated.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751282279926626, "dur": 2456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279930212, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\DrawPendingChangesEmptyState.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751282279929084, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279930970, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279932267, "dur": 1427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279933695, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279934192, "dur": 2396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279936589, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279937576, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279937709, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939011, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939150, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939437, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939563, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939701, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279939764, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279940062, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279940204, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279940328, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279940484, "dur": 1273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279941758, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279942219, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279942367, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279942614, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279943062, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279943350, "dur": 1814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279945164, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279945399, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279946156, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279946742, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279947775, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279947923, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279948803, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751282279949686, "dur": 3137087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279880230, "dur": 37237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279917475, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279917729, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279917845, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279918101, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279918301, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279918482, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279918621, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279918830, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279918914, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751282279919236, "dur": 880, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751282279920125, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279920220, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279920315, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279920593, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751282279920964, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751282279921055, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279922384, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279923833, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279925113, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279927043, "dur": 1035, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Reflection\\fsMetaProperty.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751282279926327, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279928711, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279930027, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279931588, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279932664, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279933677, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279934149, "dur": 2467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279936617, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279937578, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279937716, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279939017, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279939143, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279939371, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279939577, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279939754, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279940024, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279940163, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279940233, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279940343, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279940542, "dur": 1220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279941762, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279942124, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279942393, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279942632, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279943057, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279943344, "dur": 1787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279945131, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279945360, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279946154, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279946726, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279947754, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279947884, "dur": 893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279948778, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751282279949683, "dur": 3137123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279880369, "dur": 37118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279917493, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279917755, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279917946, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279918072, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279918264, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279918349, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279918504, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279918596, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279918924, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279919139, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279919198, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279919329, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751282279919507, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279919781, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279919929, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751282279920028, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751282279920150, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279920247, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279920323, "dur": 538, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751282279920897, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279920997, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751282279921086, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279921778, "dur": 785, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\ItemGui\\ISelectable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751282279921172, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279922988, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279924168, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279925107, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279927053, "dur": 1100, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseOverMessageListener.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751282279925931, "dur": 2369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279928301, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279929584, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279931665, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751282279930981, "dur": 2961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279933943, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279934173, "dur": 2431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279936605, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279937577, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279937714, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279939019, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279939185, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279939384, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279939572, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279939768, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279940012, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279940191, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279940321, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279940488, "dur": 1271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279941759, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279942127, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279942369, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279942621, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279943052, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279943341, "dur": 1841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279945182, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279945373, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279946199, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279946714, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279947767, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279947954, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279948773, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751282279949713, "dur": 3137162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279880467, "dur": 37046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279917522, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279917777, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279917941, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279918119, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279918196, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279918451, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279918622, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279918876, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279918949, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279919213, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279919375, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279919551, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751282279919864, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279919949, "dur": 549, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751282279920560, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751282279920622, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751282279920838, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751282279921823, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\TimelineWindow_PreviewPlayMode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751282279921090, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279922809, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279924314, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279925682, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279927078, "dur": 1110, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\MemoryHelpers.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751282279927005, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279929347, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279931068, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279933266, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279933701, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279934150, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279934245, "dur": 2345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279936591, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279937549, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279937679, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751282279938065, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751282279939258, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279939417, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279939567, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279939757, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279940010, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279940196, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279940334, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279940491, "dur": 1263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279941755, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279941992, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279942162, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279942414, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279942656, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279943097, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279943367, "dur": 1779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279945146, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279945396, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279946137, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279946734, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279947789, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279947913, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279948774, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751282279949687, "dur": 3137116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279880576, "dur": 36970, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279917555, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279918187, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279918267, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279918445, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279918506, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279918586, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279918982, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279919038, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279919251, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279919338, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279919401, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279919731, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751282279919857, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751282279920012, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751282279920183, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279920242, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751282279920755, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279920829, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751282279921077, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279922263, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279923203, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279924145, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279925302, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279927103, "dur": 1894, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Events\\FrameDelayedCallback.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751282279926195, "dur": 2803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279928999, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279930011, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279930899, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279931636, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279933312, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279933709, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279934166, "dur": 2428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279936595, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279937561, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279937665, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279938322, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279938598, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279939839, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279940058, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279940232, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279940521, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279941410, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279942479, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279942664, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279943050, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279943335, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279943412, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279943636, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279945410, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279945647, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279946583, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279946779, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279947004, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279947720, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279947925, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279948129, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279948812, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751282279949046, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279949663, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282279950946, "dur": 110, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751282279952006, "dur": 2497494, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751282282460008, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1751282282459988, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1751282282460243, "dur": 1870, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1751282282462118, "dur": 624714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279880707, "dur": 36862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279917577, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279918000, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279918136, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279918197, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279918418, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279918556, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279918820, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279919068, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279919264, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279919357, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279919548, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751282279919763, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279919989, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279920066, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751282279920603, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751282279920772, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751282279920914, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751282279921021, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751282279921161, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279922380, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279923262, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279924095, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279925099, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279926030, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279927078, "dur": 1069, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\State\\IInputStateCallbackReceiver.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751282279927078, "dur": 1978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279929056, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279930034, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279931032, "dur": 2547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279933580, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279933682, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279934146, "dur": 2451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279936598, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279937551, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279937682, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279937936, "dur": 1665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279939610, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751282279940616, "dur": 1088, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279941801, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751282279942058, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279942187, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751282279942924, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279943086, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279943373, "dur": 1788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279945162, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279945397, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279946167, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279946746, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279947751, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279947885, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279948763, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282279949664, "dur": 67618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751282280017284, "dur": 3069585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279880785, "dur": 36803, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279917594, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279917802, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279918508, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279918605, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279918987, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279919059, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279919302, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279919514, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751282279920189, "dur": 510, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751282279920702, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751282279920957, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751282279921238, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279922982, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279924329, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279925533, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279927068, "dur": 1067, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\MarkerList.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751282279926679, "dur": 2176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279928856, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279930759, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\UGUI\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751282279930237, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279931837, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279932075, "dur": 1630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279933706, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279934206, "dur": 2435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279936641, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279937563, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279937720, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279939072, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279939155, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279939374, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279939551, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279939755, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279940015, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279940197, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279940315, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279940760, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751282279941853, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279941982, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279942150, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279942392, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279942637, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279943049, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279943364, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279945193, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279945368, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279946135, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279946706, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279946787, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751282279947028, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751282279947804, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279947893, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279948782, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751282279949696, "dur": 3137162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279880861, "dur": 36743, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279917610, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279917966, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279918053, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279918357, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279918517, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279918625, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279918842, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279919014, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279919403, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279919769, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751282279920094, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279920256, "dur": 511, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751282279920857, "dur": 470, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751282279921330, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279922702, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279923632, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279924507, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279925340, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279927084, "dur": 1126, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Comparables.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751282279926238, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279928306, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279929289, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279930239, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279931134, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279932910, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279933679, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279934185, "dur": 2395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279936638, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279937555, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279937704, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279938005, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751282279939886, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279940068, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751282279940497, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751282279941606, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279942271, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279942383, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279942676, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279943065, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279943358, "dur": 1796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279945155, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279945385, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279946165, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279946756, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279947770, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279947953, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279948776, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282279949662, "dur": 64853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282280016809, "dur": 452, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 18, "ts": 1751282280014517, "dur": 2749, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751282280017267, "dur": 3069597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279880960, "dur": 36663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279917630, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751282279918017, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751282279918179, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751282279918308, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751282279918640, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279918895, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751282279919326, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279919657, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751282279919916, "dur": 706, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751282279920625, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751282279920853, "dur": 351, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751282279921791, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Sequence\\RectangleZoom.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751282279921205, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279922695, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279924018, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279925289, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279927102, "dur": 1083, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\FlexibleDictionary.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751282279926489, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279928663, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279929700, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279930700, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279931505, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279932265, "dur": 1442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279933708, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279934164, "dur": 2419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279936644, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279937587, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279937713, "dur": 1295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939016, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939084, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939182, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939368, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939548, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939708, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279939760, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279940022, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279940195, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279940341, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279940495, "dur": 1272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279941767, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279942123, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279942297, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279942404, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279942666, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279943055, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279943348, "dur": 1785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279945134, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279945355, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279946161, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279946745, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279947747, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279947891, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279948761, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279948822, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751282279949691, "dur": 3137148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279881039, "dur": 36621, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279917668, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279917965, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279918025, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279918169, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279918284, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279918651, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279918985, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279919091, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279919297, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279919390, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279919560, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279919662, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751282279919761, "dur": 530, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751282279920413, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751282279920720, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751282279920977, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751282279921790, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\ControlPlayableUtility.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751282279921160, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279922803, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279924862, "dur": 666, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\PointerEventUnit.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751282279924057, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279927036, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionEnter2DMessageListener.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751282279925987, "dur": 2685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279928673, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279929773, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279930728, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279931547, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279932515, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279933674, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279934181, "dur": 2446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279936628, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279937571, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279937701, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751282279938213, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279938605, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751282279939981, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279940160, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279940214, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279940332, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279940511, "dur": 1258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279941770, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279942130, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279942365, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279942623, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279943038, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279943340, "dur": 1809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279945150, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279945381, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279946150, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279946722, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279947795, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279947899, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279948767, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751282279949689, "dur": 3137157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279879556, "dur": 37657, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279917221, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279917458, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279917529, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279917757, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279917916, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279918118, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279918195, "dur": 569, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279918769, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279919036, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279919131, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751282279919496, "dur": 586, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751282279920086, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751282279920220, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279920299, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751282279920461, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751282279920637, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751282279921801, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\Scopes\\PropertyScope.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751282279921120, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279922881, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279923956, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279924928, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279925810, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279927055, "dur": 1178, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\UserSettingsRepository.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751282279926918, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279929385, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279930691, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279931855, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279932014, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279932074, "dur": 1596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279933723, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279934168, "dur": 2443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279936612, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279937711, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279939024, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279939139, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279939369, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279939556, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279939766, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279940053, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279940162, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279940225, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279940353, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279940505, "dur": 1244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279941761, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279941862, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279942135, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279942370, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279942620, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279943080, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279943355, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279945165, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279945411, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279946162, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279946728, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279947758, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279947929, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279948784, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751282279949692, "dur": 3137099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279879632, "dur": 37632, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279917272, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279917508, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279917592, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279917759, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279918072, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279918178, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279918359, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279918464, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279918963, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279919121, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279919378, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279919580, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279920175, "dur": 485, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751282279920664, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751282279920940, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279921013, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751282279921109, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279921816, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Recording\\TimelineRecording.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751282279921191, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279922909, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279924405, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279925752, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279927093, "dur": 1077, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\StringHelpers.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751282279926942, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279928870, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279929957, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279930873, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279931679, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279932987, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279933686, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279934158, "dur": 2438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279936596, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279937554, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279937670, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279937997, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751282279938994, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279939556, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279940024, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751282279940388, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279940704, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751282279941929, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279942170, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279942396, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279942633, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279943061, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279943353, "dur": 1784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279945138, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279945358, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279946179, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279946744, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279947761, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279947945, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279948791, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751282279949682, "dur": 3137117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279881254, "dur": 36474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279917734, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279917926, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279917992, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279918167, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279918333, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279918508, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279918636, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279918907, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279919108, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279919177, "dur": 752, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279920016, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751282279920248, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279920393, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751282279920564, "dur": 704, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751282279921272, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279923064, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279924374, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279925573, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279927063, "dur": 1204, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Mono.Reflection\\MethodBodyReader.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751282279926895, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279929359, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279930692, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279931818, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279931990, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279932147, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279933703, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279934171, "dur": 2434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279936606, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279937602, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279937688, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279937971, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751282279938910, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939097, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939149, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939376, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939579, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939715, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279939775, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279940046, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279940188, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279940320, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279940490, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279941760, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279942171, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1751282279942234, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279942415, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279942658, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279943087, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279943377, "dur": 1795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279945172, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279945412, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279946149, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279946749, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279947774, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279947926, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279948815, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751282279949078, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751282279949673, "dur": 3137095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279881324, "dur": 36425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279917751, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279918009, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279918184, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279918354, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279918436, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279918628, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279918930, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279919106, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279919245, "dur": 940, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279920206, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279920309, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279920378, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751282279920537, "dur": 812, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751282279921351, "dur": 1217, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_SpriteAssetImporter.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751282279921351, "dur": 2144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279923496, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279924511, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279925439, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279927098, "dur": 1095, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsReflectedConverter.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751282279926413, "dur": 2414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279928828, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279930187, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279931383, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279932526, "dur": 1172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279933699, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279934152, "dur": 2434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279936587, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279937569, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279937705, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751282279937991, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751282279938969, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939144, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939285, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939377, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939576, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939703, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279939769, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279940059, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279940191, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279940323, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279940482, "dur": 1286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279941768, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279942138, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279942376, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279942616, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279943047, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279943388, "dur": 1768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279945156, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279945389, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279946145, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279946735, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279947752, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279947918, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279948804, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751282279949674, "dur": 3137121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751282283097514, "dur": 7697, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17072, "tid": 1222, "ts": 1751282283146636, "dur": 3509, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17072, "tid": 1222, "ts": 1751282283150204, "dur": 4462, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17072, "tid": 1222, "ts": 1751282283133019, "dur": 23066, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}