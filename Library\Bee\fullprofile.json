{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21056, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21056, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21056, "tid": 682, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21056, "tid": 682, "ts": 1751339947553183, "dur": 1481, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947562132, "dur": 3476, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21056, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21056, "tid": 1, "ts": 1751339945944561, "dur": 9082, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751339945953648, "dur": 81161, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21056, "tid": 1, "ts": 1751339946034823, "dur": 52303, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947565622, "dur": 127, "ph": "X", "name": "", "args": {}}, {"pid": 21056, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945940711, "dur": 34903, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945975618, "dur": 1555848, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945977381, "dur": 5799, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945983191, "dur": 3246, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945986442, "dur": 561, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987009, "dur": 34, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987046, "dur": 72, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987123, "dur": 3, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987128, "dur": 62, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987194, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987197, "dur": 62, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987263, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987267, "dur": 61, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987332, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987336, "dur": 63, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987403, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987407, "dur": 68, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987479, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987482, "dur": 63, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987550, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987554, "dur": 143, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987704, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987775, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987779, "dur": 68, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987851, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987854, "dur": 66, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987925, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945987929, "dur": 67, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988000, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988003, "dur": 96, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988115, "dur": 5, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988128, "dur": 112, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988248, "dur": 4, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988254, "dur": 91, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988350, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988355, "dur": 82, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988444, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988448, "dur": 67, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988520, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988524, "dur": 56, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988584, "dur": 2, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988587, "dur": 66, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988657, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988660, "dur": 64, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988729, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988732, "dur": 56, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988793, "dur": 2, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988796, "dur": 52, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988852, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988855, "dur": 50, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988910, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988914, "dur": 48, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988966, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945988969, "dur": 49, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989022, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989025, "dur": 54, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989084, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989086, "dur": 46, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989139, "dur": 2, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989142, "dur": 114, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989260, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989263, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989318, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989321, "dur": 51, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989376, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989379, "dur": 50, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989433, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989437, "dur": 50, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989491, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989495, "dur": 50, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989550, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989552, "dur": 51, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989608, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989611, "dur": 48, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989664, "dur": 2, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989667, "dur": 47, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989718, "dur": 2, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989721, "dur": 55, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989781, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989784, "dur": 42, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989830, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989833, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989891, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989944, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945989947, "dur": 49, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990000, "dur": 2, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990003, "dur": 47, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990054, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990057, "dur": 54, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990116, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990119, "dur": 50, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990174, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990177, "dur": 45, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990226, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990229, "dur": 46, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990279, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990282, "dur": 47, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990333, "dur": 2, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990336, "dur": 58, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990398, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990402, "dur": 49, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990456, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990459, "dur": 49, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990512, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990515, "dur": 49, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990568, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990572, "dur": 54, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990630, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990633, "dur": 55, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990692, "dur": 2, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990696, "dur": 56, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990756, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990760, "dur": 51, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990815, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990818, "dur": 51, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990874, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990878, "dur": 47, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990929, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990932, "dur": 57, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990993, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945990996, "dur": 56, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991057, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991061, "dur": 47, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991113, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991118, "dur": 55, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991177, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991180, "dur": 50, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991235, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991238, "dur": 49, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991291, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991295, "dur": 44, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991343, "dur": 2, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991346, "dur": 50, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991400, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991403, "dur": 49, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991457, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991460, "dur": 53, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991518, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991521, "dur": 53, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991578, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991581, "dur": 53, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991639, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991642, "dur": 51, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991698, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991701, "dur": 51, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991757, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991760, "dur": 45, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991810, "dur": 2, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991813, "dur": 51, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991869, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991872, "dur": 52, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991928, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991933, "dur": 56, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991994, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945991997, "dur": 52, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992054, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992057, "dur": 50, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992112, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992115, "dur": 60, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992179, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992182, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992232, "dur": 2, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992235, "dur": 52, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992291, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992294, "dur": 53, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992352, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992356, "dur": 52, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992412, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992415, "dur": 50, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992469, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992473, "dur": 51, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992528, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992531, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992584, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992588, "dur": 50, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992642, "dur": 2, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992645, "dur": 51, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992701, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992705, "dur": 51, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992760, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992763, "dur": 43, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992811, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992815, "dur": 60, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992879, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992883, "dur": 50, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992937, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945992941, "dur": 71, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993016, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993020, "dur": 113, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993138, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993142, "dur": 67, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993213, "dur": 2, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993217, "dur": 60, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993281, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993285, "dur": 56, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993347, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993351, "dur": 68, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993423, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993427, "dur": 49, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993480, "dur": 4, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993486, "dur": 377, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993872, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993947, "dur": 2, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945993951, "dur": 55, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994011, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994014, "dur": 59, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994078, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994081, "dur": 52, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994138, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994142, "dur": 93, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994240, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994245, "dur": 69, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994318, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994321, "dur": 72, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994398, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994402, "dur": 64, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994471, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994475, "dur": 76, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994556, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994559, "dur": 66, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994630, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994636, "dur": 62, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994703, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994706, "dur": 53, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994765, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994769, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994851, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994854, "dur": 68, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994927, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994931, "dur": 64, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945994999, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995003, "dur": 69, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995077, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995081, "dur": 64, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995149, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995153, "dur": 69, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995227, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995230, "dur": 71, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995306, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995310, "dur": 67, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995382, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995385, "dur": 64, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995454, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995458, "dur": 69, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995532, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995536, "dur": 92, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995632, "dur": 2, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995636, "dur": 68, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995709, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995713, "dur": 68, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995786, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995791, "dur": 63, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995858, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995863, "dur": 60, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995928, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945995932, "dur": 70, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996008, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996013, "dur": 71, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996088, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996092, "dur": 65, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996163, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996167, "dur": 86, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996262, "dur": 4, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996269, "dur": 80, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996354, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996358, "dur": 82, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996444, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996448, "dur": 67, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996520, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996524, "dur": 72, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996600, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996604, "dur": 69, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996678, "dur": 3, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996683, "dur": 60, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996748, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996751, "dur": 68, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996824, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996827, "dur": 67, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996899, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996904, "dur": 66, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996975, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945996980, "dur": 68, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997052, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997056, "dur": 59, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997120, "dur": 2, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997124, "dur": 60, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997188, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997192, "dur": 90, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997287, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997292, "dur": 83, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997379, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997383, "dur": 70, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997458, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997461, "dur": 67, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997532, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997537, "dur": 62, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997604, "dur": 2, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997607, "dur": 73, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997684, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997689, "dur": 72, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997766, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997770, "dur": 76, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997851, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997855, "dur": 62, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997921, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997925, "dur": 59, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997988, "dur": 2, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945997992, "dur": 75, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998071, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998075, "dur": 65, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998145, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998149, "dur": 65, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998219, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998223, "dur": 71, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998298, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998304, "dur": 62, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998371, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998375, "dur": 70, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998450, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998453, "dur": 67, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998525, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998528, "dur": 68, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998600, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998605, "dur": 65, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998675, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998679, "dur": 65, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998748, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998752, "dur": 60, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998816, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998820, "dur": 68, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998891, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998895, "dur": 70, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998970, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945998974, "dur": 68, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999046, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999051, "dur": 66, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999121, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999126, "dur": 66, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999196, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999200, "dur": 59, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999265, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999269, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999349, "dur": 2, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999353, "dur": 67, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999425, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999429, "dur": 68, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999501, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999505, "dur": 67, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999577, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999580, "dur": 60, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999644, "dur": 2, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999648, "dur": 69, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999723, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999726, "dur": 66, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999796, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999800, "dur": 64, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999869, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999873, "dur": 66, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999943, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339945999947, "dur": 64, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000016, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000020, "dur": 113, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000137, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000142, "dur": 79, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000226, "dur": 3, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000230, "dur": 62, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000297, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000301, "dur": 53, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000358, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000362, "dur": 47, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000413, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000416, "dur": 56, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000479, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000484, "dur": 59, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000547, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000551, "dur": 54, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000609, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000613, "dur": 53, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000671, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000675, "dur": 55, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000734, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000737, "dur": 58, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000799, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000803, "dur": 54, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000861, "dur": 2, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000865, "dur": 51, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000921, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000925, "dur": 53, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000982, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946000986, "dur": 58, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001048, "dur": 4, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001054, "dur": 55, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001114, "dur": 2, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001120, "dur": 56, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001180, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001184, "dur": 54, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001243, "dur": 2, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001247, "dur": 53, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001305, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001308, "dur": 53, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001366, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001371, "dur": 52, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001427, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001430, "dur": 52, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001487, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001491, "dur": 55, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001551, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001557, "dur": 54, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001616, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001620, "dur": 54, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001679, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001682, "dur": 52, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001739, "dur": 2, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001743, "dur": 54, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001804, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001809, "dur": 56, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001869, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001873, "dur": 52, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001929, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001933, "dur": 54, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001992, "dur": 2, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946001996, "dur": 70, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002070, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002074, "dur": 52, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002131, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002135, "dur": 42, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002181, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002184, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002243, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002246, "dur": 49, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002300, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002303, "dur": 55, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002363, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002367, "dur": 48, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002419, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002423, "dur": 62, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002489, "dur": 2, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002493, "dur": 53, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002550, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002554, "dur": 51, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002610, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002613, "dur": 46, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002663, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002667, "dur": 54, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002726, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002730, "dur": 60, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002795, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002798, "dur": 55, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002858, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002861, "dur": 55, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002921, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002925, "dur": 52, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002981, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946002987, "dur": 54, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003046, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003050, "dur": 68, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003122, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003125, "dur": 69, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003199, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003202, "dur": 70, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003278, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003281, "dur": 71, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003357, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003360, "dur": 64, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003428, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003433, "dur": 70, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003507, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003511, "dur": 65, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003581, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003585, "dur": 85, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003675, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003679, "dur": 66, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003749, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003753, "dur": 67, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003825, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003829, "dur": 70, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003904, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003908, "dur": 65, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003977, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946003982, "dur": 89, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004075, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004079, "dur": 136, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004220, "dur": 3, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004225, "dur": 64, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004294, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004298, "dur": 69, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004371, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004375, "dur": 70, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004449, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004454, "dur": 72, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004530, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004534, "dur": 228, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004767, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004772, "dur": 103, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004880, "dur": 4, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004887, "dur": 72, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004963, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946004967, "dur": 66, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005038, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005043, "dur": 63, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005113, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005117, "dur": 71, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005193, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005198, "dur": 65, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005268, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005272, "dur": 60, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005336, "dur": 2, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005340, "dur": 59, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005404, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005408, "dur": 360, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005779, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946005785, "dur": 691, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946006484, "dur": 675, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007169, "dur": 152, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007326, "dur": 21, "ph": "X", "name": "ProcessMessages 1668", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007349, "dur": 62, "ph": "X", "name": "ReadAsync 1668", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007416, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007420, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007491, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007495, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007561, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007570, "dur": 71, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007648, "dur": 9, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007660, "dur": 71, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007737, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007741, "dur": 59, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007809, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007815, "dur": 88, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007909, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007914, "dur": 72, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946007995, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008003, "dur": 74, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008083, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008090, "dur": 80, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008177, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008184, "dur": 65, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008254, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008259, "dur": 66, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008333, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008340, "dur": 63, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008408, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008413, "dur": 58, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008475, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008481, "dur": 80, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008574, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008584, "dur": 64, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008653, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008658, "dur": 54, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008716, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008720, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008766, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008770, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008831, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008836, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008945, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946008956, "dur": 91, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009056, "dur": 8, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009068, "dur": 94, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009169, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009175, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009242, "dur": 2, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009287, "dur": 55, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009347, "dur": 388, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009741, "dur": 58, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009804, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946009807, "dur": 1538, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946011351, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946011356, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946011416, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946011421, "dur": 732, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946012159, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946012164, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946012224, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946012229, "dur": 18490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946030726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946030729, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946030855, "dur": 23, "ph": "X", "name": "ProcessMessages 1940", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946030880, "dur": 576, "ph": "X", "name": "ReadAsync 1940", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031464, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031468, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031536, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031540, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031597, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031600, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031693, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031764, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031767, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031821, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031825, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031878, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946031881, "dur": 318, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032208, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032247, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032252, "dur": 724, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032983, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946032987, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033057, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033060, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033112, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033115, "dur": 399, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033524, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033588, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033591, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033647, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033970, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946033973, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946034025, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946034027, "dur": 1062, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035096, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035104, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035164, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035167, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035224, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035230, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035298, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035301, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035357, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035360, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035525, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946035575, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036155, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036159, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036223, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036226, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036277, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036279, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036622, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036680, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946036683, "dur": 547, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946037246, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946037256, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946037327, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946037330, "dur": 1569, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946038905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946039004, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946039154, "dur": 853, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946040012, "dur": 568825, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946608851, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946608858, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946608973, "dur": 3880, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946612861, "dur": 6482, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946619353, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946619358, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946619427, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946619432, "dur": 1900, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946621341, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946621346, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946621432, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339946621471, "dur": 888457, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947509943, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947509950, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947510284, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947510294, "dur": 1008, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947511309, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947511314, "dur": 330, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947511652, "dur": 36, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947511694, "dur": 2161, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947513864, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947513869, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947513992, "dur": 936, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21056, "tid": 12884901888, "ts": 1751339947514937, "dur": 16440, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947565754, "dur": 11387, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339945935213, "dur": 152037, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339946087255, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21056, "tid": 8589934592, "ts": 1751339946087268, "dur": 2250, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947577145, "dur": 41, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21056, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339945901291, "dur": 1633194, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339945909759, "dur": 14081, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339947535007, "dur": 12450, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339947542756, "dur": 268, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21056, "tid": 4294967296, "ts": 1751339947547719, "dur": 34, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947577200, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751339945969324, "dur": 2484, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339945971827, "dur": 769, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339945972682, "dur": 98, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751339945972780, "dur": 1325, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339945976640, "dur": 5073, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945984103, "dur": 3672, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945987789, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945987856, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945987975, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988048, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988121, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988193, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988496, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988659, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988722, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988788, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945988949, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989078, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989148, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989247, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989392, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989461, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989541, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989650, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989723, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945989821, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990023, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_7B1D397D2193763B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990102, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990185, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990256, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990336, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990408, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990480, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990652, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_FF44434556825F42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990731, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990810, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945990900, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991016, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991088, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991247, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991305, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991362, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991428, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991484, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991544, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991721, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991786, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991908, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945991971, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992131, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_39D3266F6029657A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992194, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992253, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992370, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992431, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992494, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992683, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992802, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945992919, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993098, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993160, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993275, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993487, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993616, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993771, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945993934, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945994085, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945994673, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945994746, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945994807, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945994868, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995050, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995119, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995216, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995276, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995424, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_900B338E31D9525C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995646, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995743, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995813, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945995950, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996104, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996342, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996448, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996508, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996732, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996831, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945996893, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997099, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997158, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997265, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997325, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997558, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997624, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997721, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945997782, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998023, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998122, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998184, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998417, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998480, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998577, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998783, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998891, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945998952, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999013, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999186, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999251, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999347, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999407, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999487, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999628, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999694, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999790, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999853, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339945999914, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000092, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000156, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000311, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000450, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000515, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000609, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000669, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000837, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946000934, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001040, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001099, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001217, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001277, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001421, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001480, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001540, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001598, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001656, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001714, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001861, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001919, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946001978, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002036, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002188, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002298, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002357, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002416, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002560, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002617, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002676, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002736, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002797, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002861, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946002993, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003055, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003112, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003229, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003291, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003479, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003540, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003599, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003659, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003725, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003781, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339946003844, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004075, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004174, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004233, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004327, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004419, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004479, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004572, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004631, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004724, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004784, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946004952, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005088, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005186, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005246, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005341, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005606, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005679, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005779, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751339946005936, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751339946006000, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751339945974154, "dur": 31980, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339946006150, "dur": 1506039, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947512191, "dur": 1252, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947513718, "dur": 181, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947514023, "dur": 80, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947514302, "dur": 201, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947514548, "dur": 2626, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751339945973573, "dur": 32612, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946006203, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946006497, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946006661, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946006821, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946007128, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946007208, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946007386, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946007437, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946007518, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946007783, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751339946008347, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751339946008635, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946008703, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339946008827, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339946009274, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339946009494, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751339946009874, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946010910, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946011822, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946012678, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946013793, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946015408, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751339946014667, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946016274, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946017170, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946018048, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946018904, "dur": 1583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946020487, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946021415, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946021647, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946022797, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946023746, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946023860, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946025199, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946025352, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946025742, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946025792, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946026116, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946026187, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946026365, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946026698, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946026814, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946027277, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946028042, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946028279, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946028468, "dur": 937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946029406, "dur": 2859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946032265, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946032530, "dur": 1250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946033781, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946034312, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946035884, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946036052, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946036984, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751339946038007, "dur": 1474237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339945974587, "dur": 31902, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946006498, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946006784, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946006892, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946007065, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946007157, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946007411, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946007495, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946007814, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946007923, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946008159, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751339946008304, "dur": 627, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751339946008936, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946008988, "dur": 539, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751339946009533, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946009589, "dur": 613, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751339946010212, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946012826, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946012928, "dur": 8562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751339946021659, "dur": 1142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946022801, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946023737, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946023857, "dur": 1337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946025194, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946025315, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946025379, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946025796, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946026113, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946026191, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946026389, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946026702, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946026806, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946027330, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946027446, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751339946028666, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946029403, "dur": 2866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946032270, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946032536, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946033793, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946034346, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751339946034712, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751339946035754, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946035907, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946036087, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946036940, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751339946038009, "dur": 1474232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339945973795, "dur": 32481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946006284, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946006510, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946006643, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946006938, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946007180, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946007353, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946007460, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946007669, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946007886, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751339946008017, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751339946008277, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751339946008712, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751339946008997, "dur": 984, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751339946009985, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946011247, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.State\\Framework\\Graph\\HasStateGraph.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751339946011247, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946012565, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946013442, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946014337, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946015439, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\Observables\\ForDeviceEventObservable.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751339946015335, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946017016, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946018031, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946018742, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946020500, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946021423, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946021627, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946022803, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946023725, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946023810, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946024076, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946024736, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751339946025647, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946025849, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751339946026123, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751339946027130, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946027309, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946028028, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946028261, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946028491, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946029381, "dur": 2876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946032257, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946032533, "dur": 1239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946033772, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946034327, "dur": 1574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946035902, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946036127, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946036962, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751339946038002, "dur": 1474225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339945975100, "dur": 31488, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946006594, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946006776, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946006876, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946006977, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946007035, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946007143, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946007335, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946007452, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946007690, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946007759, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946007882, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946007964, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946008144, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946008447, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751339946008920, "dur": 703, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751339946009838, "dur": 1102, "ph": "X", "name": "File", "args": {"detail": "Assets\\TC\\Editor\\TextureCompressorWindow.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751339946009626, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946011564, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946012432, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946013611, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946015368, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelinePlayable.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751339946014879, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946016877, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946018229, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946019400, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946019925, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946021440, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946021649, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946022814, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946023744, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946023854, "dur": 1311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946025171, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946025250, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946025330, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946025788, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026071, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026128, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026189, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026386, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026704, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946026820, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946027080, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946028117, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946028296, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946028449, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946029422, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946029765, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946032384, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946032576, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946032925, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946034345, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946034707, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946036096, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946036294, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946036975, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751339946037344, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946037982, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946039245, "dur": 117, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751339946040305, "dur": 569301, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751339946619812, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751339946619793, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751339946620075, "dur": 2034, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751339946622115, "dur": 890160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339945975172, "dur": 31439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946006619, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946006918, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946007026, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946007194, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946007262, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946007413, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946007833, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946008004, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751339946008308, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751339946008422, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751339946008764, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751339946008868, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946008980, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946009049, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946009232, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751339946009438, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946009497, "dur": 289, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751339946009788, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946011292, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946013250, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946015418, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\GraphElement.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751339946014513, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946016402, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946017363, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946018358, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946019192, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946020318, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946021417, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946021640, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946022791, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946023885, "dur": 1302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946025188, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946025332, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946025841, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026078, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026131, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026203, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026367, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026695, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946026813, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946027270, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946028029, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946028254, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946028426, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946029373, "dur": 2903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946032276, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946032542, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946033785, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946034326, "dur": 1574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946035901, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946036139, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946036965, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751339946037997, "dur": 1474205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339945974033, "dur": 32332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946006370, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946006559, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946006759, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946006975, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946007167, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946007360, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946007479, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946007690, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946007958, "dur": 548, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751339946008528, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339946008588, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946008721, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339946008903, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751339946009250, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339946009421, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751339946010105, "dur": 813, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Items\\ClipItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751339946009857, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946012060, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946012889, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946013698, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946015436, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\GuidCollection.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751339946014787, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946016511, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946017735, "dur": 1277, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\FindWorkspace.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751339946017450, "dur": 2023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946019515, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946019615, "dur": 1806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946021422, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946021637, "dur": 1147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946022785, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946023735, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946023851, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946025235, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946025374, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946025744, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946025801, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946026114, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946026190, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946026381, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946026715, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946026821, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946027271, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946028024, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946028149, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946028268, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946028439, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946029371, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946029427, "dur": 2839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946032267, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946032521, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946033767, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946034335, "dur": 1573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946035908, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946036107, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946036954, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751339946038014, "dur": 1474253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339945974195, "dur": 32196, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946006397, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946006672, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946007210, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946007340, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946007753, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946007926, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946008001, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751339946008331, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339946008637, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946008697, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339946008862, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751339946009290, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339946009398, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751339946009509, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751339946009913, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946011306, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946013053, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946015411, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\Extensions\\XString.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751339946014626, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946016591, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946018117, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946019322, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946019674, "dur": 1736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946021411, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946021613, "dur": 1159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946022833, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946023740, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946023846, "dur": 1338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946025185, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946025331, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946025805, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946026121, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946026207, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946026370, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946026692, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946026810, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946027258, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946027325, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946028035, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946028286, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946028445, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946029393, "dur": 2875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946032268, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946032554, "dur": 1219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946033774, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946034332, "dur": 1560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946035892, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946036056, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946036957, "dur": 1037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751339946037994, "dur": 1474204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339945974104, "dur": 32274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946006383, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946006606, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946006921, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946007038, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946007204, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946007260, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946007466, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946007709, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946007763, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946007914, "dur": 429, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751339946008425, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339946008588, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946008645, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339946009083, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946009235, "dur": 487, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751339946009724, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946010900, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946012126, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946013123, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946014200, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946015400, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\BranchPoint.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751339946016346, "dur": 1464, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\SettingsDictionary.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751339946015130, "dur": 2942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946018073, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946018980, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946019741, "dur": 1657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946021398, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946021608, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946022805, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946023723, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946023815, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946024268, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946024336, "dur": 1619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751339946025957, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946026419, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751339946026711, "dur": 1592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751339946028304, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946028496, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946029432, "dur": 2862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946032294, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946032547, "dur": 1256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946033803, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946034334, "dur": 1547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946035882, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946036050, "dur": 897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946036948, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946037989, "dur": 52357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946093935, "dur": 808, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1751339946090348, "dur": 4402, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751339946094751, "dur": 1417563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339945974242, "dur": 32165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946006414, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946006650, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946007012, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946007087, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946007209, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946007281, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946007386, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946007769, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946007913, "dur": 2275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946010210, "dur": 1830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946012042, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946012122, "dur": 9160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946021283, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946021450, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946021669, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946022829, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946023028, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946023807, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946024327, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946026554, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946026695, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946026800, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946027273, "dur": 1995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946029422, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946029760, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946032129, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946032284, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751339946032610, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751339946033640, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946033801, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946034320, "dur": 1547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946035930, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946036069, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946036989, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751339946038011, "dur": 1474240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339945974308, "dur": 32126, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946006439, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946006611, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946006693, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946006982, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946007057, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946007245, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946007313, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946007397, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946007705, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946008053, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946008447, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751339946008830, "dur": 837, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751339946009670, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946010848, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946011990, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946013142, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946014067, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946015435, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751339946014966, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946016524, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946017423, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946018294, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946019183, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946020468, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946021419, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946021632, "dur": 1177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946022809, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946023747, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946023821, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751339946024132, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751339946025073, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946025174, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946025259, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946025340, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946025793, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946026125, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946026177, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946026371, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946026721, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946026815, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946027258, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946028036, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946028289, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946028443, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946029430, "dur": 2821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946032251, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946032532, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946033791, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946034309, "dur": 1576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946035886, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946036089, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946036967, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751339946038027, "dur": 1474256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339945974440, "dur": 32009, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946006454, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946006679, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946006905, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946006996, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946007143, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946007275, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946007649, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946007737, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946007931, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946008067, "dur": 570, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946008710, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751339946008824, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751339946009003, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751339946009522, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751339946009888, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946010857, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946011665, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946012537, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946014010, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946015433, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ScrollbarEventHandler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751339946014981, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946016473, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946017479, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946018351, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946019186, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946020785, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946021431, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946021648, "dur": 1182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946022830, "dur": 898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946023728, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946023835, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751339946024386, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751339946025927, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946026135, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946026205, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946026369, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946026697, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946026812, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946027275, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946028044, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946028142, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946028305, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946028452, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946029389, "dur": 2866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946032255, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946032595, "dur": 1207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946033802, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946034311, "dur": 1560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946035937, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946036058, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946036947, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946037991, "dur": 56805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751339946094797, "dur": 1417439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339945973666, "dur": 32562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946006234, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946006449, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946006533, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946006744, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946006817, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946007068, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946007312, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946007503, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946007765, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946008107, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751339946008220, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946008318, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946008784, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946008880, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946008965, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946009324, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946009544, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751339946009652, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946010259, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946011310, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Ports\\InvalidOutputWidget.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751339946011265, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946012664, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Control\\ToggleFlow.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751339946012506, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946014057, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946015419, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Audio\\AudioMixerProperties.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751339946014943, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946016535, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946017443, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946018715, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946021409, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946021610, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946022839, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946023871, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946025192, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946025390, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946025790, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946026114, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946026193, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946026363, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946026708, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946026817, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946027266, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946028052, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946028293, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946028447, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946029397, "dur": 2850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946032248, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946032539, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946033782, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946034318, "dur": 1586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946035905, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946036098, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946036956, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751339946038016, "dur": 1474193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339945974506, "dur": 31959, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946006471, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946006666, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946006755, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946007042, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946007121, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946007280, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946007481, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946007746, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946007952, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946008016, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946008124, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946008253, "dur": 443, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751339946008699, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339946008791, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339946008865, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946008916, "dur": 732, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751339946010174, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\ProjectGeneration\\ProjectGeneration.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751339946009649, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946010869, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946011773, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946012686, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946013390, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946015403, "dur": 784, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerExitMListener.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751339946014344, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946016488, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946017846, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946018955, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946020024, "dur": 1403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946021428, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946021642, "dur": 1156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946022799, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946023719, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946023810, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946024279, "dur": 856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946025187, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751339946026049, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946026247, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751339946026547, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751339946027867, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946027999, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946028079, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946028291, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946028473, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946029400, "dur": 2861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946032261, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946032517, "dur": 1280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946033797, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946034303, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946035880, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946036054, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946036945, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751339946037999, "dur": 1474184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339945974706, "dur": 31805, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946006518, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946006740, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946006925, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946006989, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946007149, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946007307, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946007473, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946007703, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946007825, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946008005, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946008058, "dur": 516, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751339946008585, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946008694, "dur": 663, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751339946009640, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946010939, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946011876, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946012684, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946013444, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946014438, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946015410, "dur": 694, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\State\\InputStateHistory.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751339946015410, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946016955, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946017906, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946018923, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946019888, "dur": 1538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946021426, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946021645, "dur": 1143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946022789, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946023739, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946023844, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946025190, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946025337, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946025807, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946026124, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946026228, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946026407, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946026743, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946026809, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946027254, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946027634, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946028026, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946028258, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946028431, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946029408, "dur": 2887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946032295, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946032538, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946033776, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946034323, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946035895, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946036150, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946036939, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751339946038003, "dur": 1474230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339945974840, "dur": 31697, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946006551, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946006791, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946006928, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946007172, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946007419, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946007677, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946007785, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946008018, "dur": 438, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751339946008527, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339946008688, "dur": 778, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339946009594, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751339946010288, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751339946009690, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946011267, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946012240, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946013281, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Utilities\\BackupUtility.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751339946012937, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946015416, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Connections\\GraphConnectionCollection.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751339946014731, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946016342, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946017324, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946018277, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946019177, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946020081, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946021408, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946021607, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946022813, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946023726, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946023846, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751339946024142, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751339946025075, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946025191, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946025791, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946026111, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946026181, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946026377, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946026693, "dur": 1492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946028193, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946028274, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946028441, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946029384, "dur": 2857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946032300, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946032544, "dur": 1234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946033779, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946034347, "dur": 1556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946035903, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946036117, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946036959, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751339946038035, "dur": 1474159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339945974969, "dur": 31601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946006579, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946007066, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946007268, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946007333, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946007697, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946007810, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946008026, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751339946008217, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751339946008503, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339946008563, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946008674, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339946009133, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946009220, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339946009350, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946009409, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751339946010238, "dur": 1254, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\TimelineKeyboardNavigation.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751339946009687, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946011932, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946013177, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946015427, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\EditorBindingUtility.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751339946014657, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946016638, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946017977, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946019191, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946020840, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946021401, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946021602, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946022778, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946023720, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946023852, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946024473, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946025226, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751339946026210, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946026401, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751339946026692, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751339946027909, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946028030, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946028321, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946028428, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946029387, "dur": 2884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946032272, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946032526, "dur": 1269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946033795, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946034315, "dur": 1563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946035879, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946036057, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946036961, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751339946038047, "dur": 1474224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339945973871, "dur": 32456, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946006335, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946006519, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946006597, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946006973, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946007034, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946007098, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946007299, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946007462, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946007777, "dur": 599, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946008526, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339946008662, "dur": 550, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339946009263, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339946009358, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339946009513, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751339946009568, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751339946009670, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946011634, "dur": 934, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\StartUpMethodExecutor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751339946010722, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946012570, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946013804, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946015438, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Attributes\\VisualScriptingHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751339946014837, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946016417, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946017315, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946018223, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946019279, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946019493, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946019594, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946021405, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946021609, "dur": 1194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946022804, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946023823, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946024218, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751339946026001, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946026188, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751339946026506, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751339946027286, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946027628, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946028046, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946028284, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946028433, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946029407, "dur": 2851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946032259, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946032517, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946032582, "dur": 1203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946033786, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946034316, "dur": 1595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946035912, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946036077, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946036964, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751339946038005, "dur": 1474200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339945973948, "dur": 32400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946006356, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946006581, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946006654, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946006765, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946006968, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946007048, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946007232, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946007404, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946007760, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946007904, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946007999, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751339946008360, "dur": 643, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751339946009010, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946009144, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946009215, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751339946009585, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751339946010078, "dur": 1035, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Undo\\ApplyDefaultUndoAttribute.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751339946009696, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946011654, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946012400, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946013135, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946014013, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946015437, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\Signals\\SignalAsset.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751339946014931, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946016447, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946017346, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946018233, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946019123, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946019791, "dur": 1628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946021420, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946021635, "dur": 1171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946022806, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946023733, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946023849, "dur": 1321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946025243, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946025364, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946025755, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946025809, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946026119, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946026196, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946026377, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946026712, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946026820, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946027302, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946028037, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946028249, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946028425, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946028689, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946029428, "dur": 2856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946032285, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946032535, "dur": 1272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946033808, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946034325, "dur": 1571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946035897, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946036181, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946036951, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751339946038021, "dur": 1474226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339945975281, "dur": 31348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946006635, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946007176, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946007261, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946007422, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946007472, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946007536, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946007889, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946007955, "dur": 1470, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946009447, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946009502, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751339946009707, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946011118, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946012024, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946012782, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946013879, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946015422, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsWeakReferenceConverter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751339946014694, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946016348, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946017245, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946018111, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946019001, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946019732, "dur": 1664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946021454, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946021619, "dur": 1168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946022787, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946023710, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946023831, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946024112, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751339946025289, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946025799, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026066, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026129, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026195, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026387, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026700, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946026804, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946027247, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751339946027942, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946028117, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946028288, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946028464, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946029410, "dur": 2853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946032264, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946032551, "dur": 1245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946033796, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946034330, "dur": 1557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946035888, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946036100, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946036977, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751339946037364, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751339946037995, "dur": 1474196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339945975354, "dur": 31291, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946006651, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946006959, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946007147, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946007285, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946007426, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946007725, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946007910, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946008045, "dur": 502, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946008737, "dur": 655, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751339946009449, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946009499, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751339946009948, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946011337, "dur": 1451, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphOutputInspector.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751339946011337, "dur": 2314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946013652, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946015430, "dur": 666, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphElementData.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751339946014508, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946016098, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946016975, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946017875, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946018781, "dur": 2000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946020782, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946021413, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946021622, "dur": 1170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946022793, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946023731, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946023843, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751339946024203, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751339946025176, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946025360, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946025806, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946026130, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946026197, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946026373, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946026718, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946026829, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946027278, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946028027, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946028263, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946028430, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946029424, "dur": 2849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946032275, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946032540, "dur": 1230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946033771, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946034299, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946034353, "dur": 1540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946035893, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946036171, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946036955, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751339946038040, "dur": 1474221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339945975407, "dur": 31272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946006685, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007016, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007186, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007346, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007509, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007792, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946007960, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946008051, "dur": 543, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751339946008605, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946008659, "dur": 1105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751339946010787, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Sequence\\RectangleTool.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751339946009766, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946011489, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946012237, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946013339, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946014215, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946015423, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Common\\IOnboardingSection.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751339946015243, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946016759, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946017713, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946018691, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946020579, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946021430, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946021643, "dur": 1139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946022783, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946023730, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946023858, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946025190, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946025386, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946025797, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026064, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026118, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026185, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026374, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026691, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946026807, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946027255, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946028028, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946028260, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946028460, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946029404, "dur": 2845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946032250, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946032523, "dur": 1229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946033821, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946034307, "dur": 1587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946035894, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946036161, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946036968, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946037992, "dur": 581842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751339946619894, "dur": 890668, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 21, "ts": 1751339946619839, "dur": 890728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 21, "ts": 1751339947510613, "dur": 1439, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751339945975452, "dur": 31242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946006701, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946006941, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946007017, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946007227, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946007400, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946007726, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946007946, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946008061, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751339946009078, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946009142, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339946009254, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751339946009419, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751339946009745, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946011321, "dur": 1443, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\States\\StateAnalyser.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339946010982, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946013132, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946014122, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946015425, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Dropdown.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751339946015087, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946016819, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946017870, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946018933, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751339946018802, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946020735, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946021425, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946021661, "dur": 1146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946022808, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946023742, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946023856, "dur": 1311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946025218, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946025366, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946025792, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026077, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026137, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026208, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026365, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026435, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026699, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946026837, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946027257, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946028038, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946028311, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946028472, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946029435, "dur": 2818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946032253, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946032519, "dur": 1268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946033788, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946034356, "dur": 1519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946035876, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946036053, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946036929, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946036981, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751339946038000, "dur": 1474179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339945975551, "dur": 31153, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946006708, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946006929, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946006993, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946007082, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946007266, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946007440, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946007752, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946008024, "dur": 777, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751339946009007, "dur": 1001, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751339946010010, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946010989, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946012080, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946013740, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946015432, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\FontFeatureCommon.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751339946015118, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946017015, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946018400, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946019654, "dur": 1798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946021453, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946021616, "dur": 1194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946022811, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946023741, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946023853, "dur": 1335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946025189, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946025339, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946025794, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946026114, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946026179, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946026378, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946026719, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946026828, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946027274, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946027986, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946028058, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946028299, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946028470, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946029390, "dur": 2848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946032307, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946032528, "dur": 1221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946033806, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946034328, "dur": 1561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946035890, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946036048, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946036935, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751339946038012, "dur": 1474202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339945975654, "dur": 31090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946006746, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946006905, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946007002, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946007150, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946007242, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946007392, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946007459, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946007777, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946008021, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946008183, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946008337, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946008401, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946008540, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946008729, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946009075, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946009240, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946009327, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946009427, "dur": 635, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751339946010063, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946011098, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946012045, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946012971, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946013779, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946015393, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorWideAttribute.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751339946014652, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946016218, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946017102, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946018011, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946018920, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946019815, "dur": 1579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946021451, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946021664, "dur": 1130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946022795, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946023707, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946023818, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751339946024125, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751339946024955, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946025341, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946025459, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946025802, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946026222, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946026375, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946026727, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946026831, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946027268, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946028086, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946028297, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946028471, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946029383, "dur": 2862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946032246, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946032525, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946033769, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946034321, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946035899, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946036190, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946036950, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751339946038015, "dur": 1474276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751339947523605, "dur": 7364, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21056, "tid": 682, "ts": 1751339947580136, "dur": 3552, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21056, "tid": 682, "ts": 1751339947583735, "dur": 4056, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21056, "tid": 682, "ts": 1751339947558797, "dur": 30385, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}