using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System;

namespace TextureCompressorTool
{
    /// <summary>
    /// Advanced AI-powered texture analysis engine
    /// </summary>
    public static class TextureAnalyzer
    {
        public enum TextureContentType
        {
            Photo,
            Artwork,
            UI,
            Normal,
            Heightmap,
            Mask,
            Icon,
            Pattern,
            Noise,
            Gradient
        }

        public enum OptimizationPriority
        {
            Quality,
            Size,
            Performance,
            Balanced
        }

        public class AnalysisResult
        {
            public TextureContentType contentType;
            public float complexityScore;
            public float noiseLevel;
            public float colorVariance;
            public bool hasTransparency;
            public bool hasGradients;
            public bool hasSharpEdges;
            public bool isMonochrome;
            public bool isPowerOfTwo;
            public float compressionEfficiency;
            public TextureImporterFormat recommendedFormat;
            public int recommendedSize;
            public string analysisNotes;
            public float qualityScore;
            public Dictionary<string, float> metrics;
        }

        /// <summary>
        /// Perform advanced AI-powered texture analysis
        /// </summary>
        public static AnalysisResult AnalyzeTexture(Texture2D texture, OptimizationPriority priority = OptimizationPriority.Balanced)
        {
            if (texture == null) return null;

            var result = new AnalysisResult
            {
                metrics = new Dictionary<string, float>()
            };

            try
            {
                // Make texture readable for analysis
                var path = AssetDatabase.GetAssetPath(texture);
                var importer = AssetImporter.GetAtPath(path) as TextureImporter;
                bool wasReadable = importer.isReadable;
                
                if (!wasReadable)
                {
                    importer.isReadable = true;
                    importer.SaveAndReimport();
                }

                // Perform pixel-level analysis
                AnalyzePixelData(texture, result);
                
                // Analyze texture properties
                AnalyzeTextureProperties(texture, result);
                
                // Determine content type using AI heuristics
                DetermineContentType(result);
                
                // Calculate optimization recommendations
                CalculateRecommendations(texture, result, priority);
                
                // Generate analysis notes
                GenerateAnalysisNotes(result);

                // Restore original readable state
                if (!wasReadable)
                {
                    importer.isReadable = false;
                    importer.SaveAndReimport();
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Texture analysis failed for {texture.name}: {e.Message}");
                result.analysisNotes = "Analysis failed - texture may not be readable";
            }

            return result;
        }

        private static void AnalyzePixelData(Texture2D texture, AnalysisResult result)
        {
            try
            {
                var pixels = texture.GetPixels();
                if (pixels == null || pixels.Length == 0) return;

                float totalR = 0, totalG = 0, totalB = 0, totalA = 0;
                float minR = 1, maxR = 0, minG = 1, maxG = 0, minB = 1, maxB = 0;
                int transparentPixels = 0;

                
                // Sample pixels for performance (max 10000 pixels)
                int sampleStep = Mathf.Max(1, pixels.Length / 10000);
                int sampledPixels = 0;

                for (int i = 0; i < pixels.Length; i += sampleStep)
                {
                    var pixel = pixels[i];
                    
                    totalR += pixel.r;
                    totalG += pixel.g;
                    totalB += pixel.b;
                    totalA += pixel.a;
                    
                    minR = Mathf.Min(minR, pixel.r);
                    maxR = Mathf.Max(maxR, pixel.r);
                    minG = Mathf.Min(minG, pixel.g);
                    maxG = Mathf.Max(maxG, pixel.g);
                    minB = Mathf.Min(minB, pixel.b);
                    maxB = Mathf.Max(maxB, pixel.b);
                    
                    if (pixel.a < 0.99f) transparentPixels++;
                    
                    sampledPixels++;
                }

                float avgR = totalR / sampledPixels;
                float avgG = totalG / sampledPixels;
                float avgB = totalB / sampledPixels;
                float avgA = totalA / sampledPixels;

                // Calculate metrics
                result.hasTransparency = transparentPixels > sampledPixels * 0.01f; // More than 1% transparent
                result.colorVariance = (maxR - minR) + (maxG - minG) + (maxB - minB);
                result.isMonochrome = Mathf.Abs(avgR - avgG) < 0.1f && Mathf.Abs(avgG - avgB) < 0.1f;
                
                // Noise level estimation
                result.noiseLevel = CalculateNoiseLevel(pixels, texture.width, texture.height, sampleStep);
                
                // Complexity score
                result.complexityScore = result.colorVariance * 0.4f + result.noiseLevel * 0.6f;
                
                // Store detailed metrics
                result.metrics["avgRed"] = avgR;
                result.metrics["avgGreen"] = avgG;
                result.metrics["avgBlue"] = avgB;
                result.metrics["avgAlpha"] = avgA;
                result.metrics["colorRange"] = result.colorVariance;
                result.metrics["transparencyRatio"] = (float)transparentPixels / sampledPixels;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Pixel analysis failed: {e.Message}");
            }
        }

        private static float CalculateNoiseLevel(Color[] pixels, int width, int height, int sampleStep)
        {
            float noiseSum = 0;
            int comparisons = 0;
            
            // Sample neighboring pixels to detect noise
            for (int y = 1; y < height - 1; y += sampleStep)
            {
                for (int x = 1; x < width - 1; x += sampleStep)
                {
                    int index = y * width + x;
                    if (index >= pixels.Length) continue;
                    
                    var center = pixels[index];
                    var right = pixels[index + 1];
                    var down = pixels[index + width];
                    
                    float diff1 = ColorDifference(center, right);
                    float diff2 = ColorDifference(center, down);
                    
                    noiseSum += (diff1 + diff2) * 0.5f;
                    comparisons++;
                    
                    if (comparisons > 1000) break; // Limit for performance
                }
                if (comparisons > 1000) break;
            }
            
            return comparisons > 0 ? noiseSum / comparisons : 0;
        }

        private static float ColorDifference(Color a, Color b)
        {
            return Mathf.Abs(a.r - b.r) + Mathf.Abs(a.g - b.g) + Mathf.Abs(a.b - b.b);
        }

        private static void AnalyzeTextureProperties(Texture2D texture, AnalysisResult result)
        {
            result.isPowerOfTwo = IsPowerOfTwo(texture.width) && IsPowerOfTwo(texture.height);
            
            // Analyze gradients and edges
            result.hasGradients = result.colorVariance > 0.3f && result.noiseLevel < 0.2f;
            result.hasSharpEdges = result.noiseLevel > 0.4f && result.colorVariance > 0.5f;
            
            // Calculate quality score
            result.qualityScore = CalculateQualityScore(texture, result);
        }

        private static bool IsPowerOfTwo(int value)
        {
            return (value & (value - 1)) == 0;
        }

        private static float CalculateQualityScore(Texture2D texture, AnalysisResult result)
        {
            float score = 1.0f;
            
            // Penalize non-power-of-two textures
            if (!result.isPowerOfTwo) score -= 0.1f;
            
            // Reward appropriate complexity
            if (result.complexityScore > 0.3f && result.complexityScore < 0.7f) score += 0.1f;
            
            // Consider resolution appropriateness
            int maxDim = Mathf.Max(texture.width, texture.height);
            if (maxDim > 2048 && result.complexityScore < 0.4f) score -= 0.2f; // Oversized simple texture
            if (maxDim < 256 && result.complexityScore > 0.7f) score -= 0.1f; // Undersized complex texture
            
            return Mathf.Clamp01(score);
        }

        private static void DetermineContentType(AnalysisResult result)
        {
            // AI-powered content type detection using heuristics
            if (result.isMonochrome && result.noiseLevel < 0.2f)
            {
                result.contentType = TextureContentType.Mask;
            }
            else if (result.colorVariance < 0.2f && result.noiseLevel < 0.1f)
            {
                result.contentType = TextureContentType.UI;
            }
            else if (result.hasGradients && !result.hasSharpEdges)
            {
                result.contentType = TextureContentType.Gradient;
            }
            else if (result.noiseLevel > 0.6f)
            {
                result.contentType = TextureContentType.Noise;
            }
            else if (result.complexityScore > 0.7f && result.colorVariance > 0.6f)
            {
                result.contentType = TextureContentType.Photo;
            }
            else if (result.hasSharpEdges && result.colorVariance > 0.4f)
            {
                result.contentType = TextureContentType.Artwork;
            }
            else if (result.colorVariance > 0.3f && result.noiseLevel < 0.3f)
            {
                result.contentType = TextureContentType.Pattern;
            }
            else
            {
                result.contentType = TextureContentType.Artwork; // Default
            }
        }

        private static void CalculateRecommendations(Texture2D texture, AnalysisResult result, OptimizationPriority priority)
        {
            // Recommend format based on content type and priority
            switch (result.contentType)
            {
                case TextureContentType.Photo:
                    result.recommendedFormat = result.hasTransparency ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                    break;
                case TextureContentType.UI:
                case TextureContentType.Icon:
                    result.recommendedFormat = priority == OptimizationPriority.Quality ? 
                        TextureImporterFormat.RGBA32 : TextureImporterFormat.DXT5;
                    break;
                case TextureContentType.Normal:
                    result.recommendedFormat = TextureImporterFormat.DXT5;
                    break;
                case TextureContentType.Mask:
                    result.recommendedFormat = result.isMonochrome ? 
                        TextureImporterFormat.Alpha8 : TextureImporterFormat.DXT1;
                    break;
                default:
                    result.recommendedFormat = result.hasTransparency ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                    break;
            }

            // Recommend size based on complexity and priority
            int currentMaxDim = Mathf.Max(texture.width, texture.height);
            
            switch (priority)
            {
                case OptimizationPriority.Quality:
                    result.recommendedSize = currentMaxDim;
                    break;
                case OptimizationPriority.Size:
                    result.recommendedSize = Mathf.Min(512, currentMaxDim);
                    break;
                case OptimizationPriority.Performance:
                    result.recommendedSize = result.complexityScore > 0.5f ? 
                        Mathf.Min(1024, currentMaxDim) : Mathf.Min(512, currentMaxDim);
                    break;
                case OptimizationPriority.Balanced:
                default:
                    if (result.complexityScore > 0.7f)
                        result.recommendedSize = Mathf.Min(1024, currentMaxDim);
                    else if (result.complexityScore > 0.4f)
                        result.recommendedSize = Mathf.Min(512, currentMaxDim);
                    else
                        result.recommendedSize = Mathf.Min(256, currentMaxDim);
                    break;
            }

            // Ensure power of two
            result.recommendedSize = GetNearestPowerOfTwo(result.recommendedSize);
            
            // Calculate compression efficiency
            result.compressionEfficiency = CalculateCompressionEfficiency(result);
        }

        private static int GetNearestPowerOfTwo(int value)
        {
            int power = 1;
            while (power < value) power *= 2;
            
            // Choose between current power and previous power based on which is closer
            int prevPower = power / 2;
            return (value - prevPower) < (power - value) ? prevPower : power;
        }

        private static float CalculateCompressionEfficiency(AnalysisResult result)
        {
            float efficiency = 1.0f;
            
            // Different content types compress differently
            switch (result.contentType)
            {
                case TextureContentType.Photo:
                    efficiency = 0.3f; // Photos compress well
                    break;
                case TextureContentType.UI:
                case TextureContentType.Icon:
                    efficiency = 0.6f; // UI elements need quality
                    break;
                case TextureContentType.Noise:
                    efficiency = 0.8f; // Noise doesn't compress well
                    break;
                case TextureContentType.Gradient:
                    efficiency = 0.4f; // Gradients compress reasonably
                    break;
                default:
                    efficiency = 0.5f;
                    break;
            }
            
            // Adjust based on complexity
            efficiency += (1.0f - result.complexityScore) * 0.2f;
            
            return Mathf.Clamp01(efficiency);
        }

        private static void GenerateAnalysisNotes(AnalysisResult result)
        {
            var notes = new List<string>();
            
            notes.Add($"Content Type: {result.contentType}");
            notes.Add($"Complexity: {result.complexityScore:F2}");
            notes.Add($"Quality Score: {result.qualityScore:F2}");
            
            if (result.hasTransparency)
                notes.Add("Contains transparency");
            
            if (result.isMonochrome)
                notes.Add("Monochrome texture");
            
            if (!result.isPowerOfTwo)
                notes.Add("⚠️ Non-power-of-two dimensions");
            
            if (result.noiseLevel > 0.5f)
                notes.Add("High noise level detected");
            
            if (result.colorVariance < 0.2f)
                notes.Add("Low color variation - good for compression");
            
            if (result.compressionEfficiency < 0.4f)
                notes.Add("✅ Excellent compression candidate");
            else if (result.compressionEfficiency > 0.7f)
                notes.Add("⚠️ May not compress well");
            
            result.analysisNotes = string.Join("\n", notes);
        }
    }
}