using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace TextureCompressorTool
{
    /// <summary>
    /// Settings class for the Texture Compressor Tool
    /// </summary>
    public class TextureCompressorSettings : ScriptableObject
    {
        private const string SettingsPath = "Assets/TC/Editor/TextureCompressorSettings.asset";

        [Header("Compression Settings")]
        [Tooltip("Maximum texture size for compressed textures")]
        public int maxTextureSize = 1024;

        [Tooltip("Compression quality (0-100)")]
        [Range(0, 100)]
        public int compressionQuality = 50;

        [Tooltip("Use crunch compression when available")]
        public bool useCrunchCompression = true;

        [Header("Platform Settings")]
        public List<PlatformSetting> platformSettings = new List<PlatformSetting>();

        [Header("Build Settings")]
        [Tooltip("Automatically compress textures when building the project")]
        public bool compressOnBuild = false;

        [System.Serializable]
        public class PlatformSetting
        {
            public string platformName;
            public bool overrideDefault = false;

            [Header("Texture Settings")]
            public int maxTextureSize = 1024;

            [Tooltip("Resize algorithm to use when resizing textures")]
            public TextureResizeAlgorithm resizeAlgorithm = TextureResizeAlgorithm.Mitchell;

            [Tooltip("Texture format to use for this platform")]
            public TextureImporterFormat format = TextureImporterFormat.Automatic;

            [Tooltip("Compression quality (Normal, Fast, Best, etc.)")]
            public TextureImporterCompression textureCompression = TextureImporterCompression.Compressed;

            [Tooltip("Compression quality value (0-100)")]
            [Range(0, 100)]
            public int compressionQuality = 50;

            [Tooltip("Use crunch compression when available")]
            public bool crunchedCompression = true;
        }

        /// <summary>
        /// Gets the settings instance, creating it if it doesn't exist
        /// </summary>
        public static TextureCompressorSettings GetOrCreateSettings()
        {
            var settings = AssetDatabase.LoadAssetAtPath<TextureCompressorSettings>(SettingsPath);
            if (settings == null)
            {
                settings = CreateInstance<TextureCompressorSettings>();

                // Add default platform settings
                settings.platformSettings.Add(new PlatformSetting { platformName = "Android", maxTextureSize = 1024 });
                settings.platformSettings.Add(new PlatformSetting { platformName = "iPhone", maxTextureSize = 1024 });
                settings.platformSettings.Add(new PlatformSetting { platformName = "WebGL", maxTextureSize = 1024 });
                settings.platformSettings.Add(new PlatformSetting { platformName = "Standalone", maxTextureSize = 1024 });

                // Ensure the directory exists
                string directory = System.IO.Path.GetDirectoryName(SettingsPath);
                if (!AssetDatabase.IsValidFolder(directory))
                {
                    string[] folders = directory.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                AssetDatabase.CreateAsset(settings, SettingsPath);
                AssetDatabase.SaveAssets();
            }

            return settings;
        }

        /// <summary>
        /// Gets the platform settings for a specific platform
        /// </summary>
        public PlatformSetting GetPlatformSettings(string platformName)
        {
            return platformSettings.Find(p => p.platformName == platformName);
        }

        /// <summary>
        /// Applies the settings to a texture importer
        /// </summary>
        public void ApplyToImporter(TextureImporter importer, string platformName = null)
        {
            // Apply default settings
            TextureImporterPlatformSettings defaultSettings = new TextureImporterPlatformSettings
            {
                name = "DefaultTexturePlatform",
                maxTextureSize = maxTextureSize,
                format = TextureImporterFormat.Automatic,
                textureCompression = TextureImporterCompression.Compressed,
                compressionQuality = compressionQuality,
                crunchedCompression = useCrunchCompression,
                allowsAlphaSplitting = false,
                overridden = true
            };

            importer.SetPlatformTextureSettings(defaultSettings);

            // Apply platform-specific settings if available
            if (!string.IsNullOrEmpty(platformName))
            {
                var platformSetting = GetPlatformSettings(platformName);
                if (platformSetting != null && platformSetting.overrideDefault)
                {
                    TextureImporterPlatformSettings platformSettings = new TextureImporterPlatformSettings
                    {
                        name = platformName,
                        maxTextureSize = platformSetting.maxTextureSize,
                        format = platformSetting.format,
                        textureCompression = platformSetting.textureCompression,
                        compressionQuality = platformSetting.compressionQuality,
                        crunchedCompression = platformSetting.crunchedCompression,
                        allowsAlphaSplitting = false,
                        overridden = true
                    };

                    importer.SetPlatformTextureSettings(platformSettings);
                }
            }

            // Mark the asset as dirty so it gets reimported
            EditorUtility.SetDirty(importer);
            AssetDatabase.ImportAsset(importer.assetPath);
        }
    }
}