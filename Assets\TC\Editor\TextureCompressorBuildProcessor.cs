using UnityEngine;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using System.Collections.Generic;

namespace TextureCompressorTool
{
    /// <summary>
    /// Build processor that automatically compresses textures during the build process
    /// </summary>
    public class TextureCompressorBuildProcessor : IPreprocessBuildWithReport
    {
        public int callbackOrder => 0;

        public void OnPreprocessBuild(BuildReport report)
        {
            var settings = TextureCompressorSettings.GetOrCreateSettings();
            
            // Skip if compression on build is disabled
            if (!settings.compressOnBuild)
                return;
            
            Debug.Log("Texture Compressor: Compressing textures for build...");
            
            // Find all textures in the project
            string[] guids = AssetDatabase.FindAssets("t:texture2D");
            List<string> processedPaths = new List<string>();
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                
                // Skip textures in packages
                if (path.StartsWith("Packages/"))
                    continue;
                
                // Skip textures that have already been processed
                if (processedPaths.Contains(path))
                    continue;
                
                processedPaths.Add(path);
                
                // Get the texture importer
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                if (importer == null)
                    continue;
                
                // Skip cursor textures and sprites that might be part of atlases
                if (importer.textureType == TextureImporterType.Cursor)
                    continue;
                
                // Skip textures that are already optimally compressed
                if (importer.textureType == TextureImporterType.Lightmap || 
                    importer.textureType == TextureImporterType.DirectionalLightmap)
                    continue;
                
                // Apply compression settings based on the build target
                string platformName = GetPlatformName(report.summary.platform);
                settings.ApplyToImporter(importer, platformName);
            }
            
            // Import all modified assets
            AssetDatabase.Refresh();
            
            Debug.Log($"Texture Compressor: Compressed {processedPaths.Count} textures for build.");
        }

        private string GetPlatformName(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return "Android";
                case BuildTarget.iOS:
                    return "iPhone";
                case BuildTarget.WebGL:
                    return "WebGL";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return "Standalone";
                case BuildTarget.StandaloneOSX:
                    return "Standalone";
                case BuildTarget.StandaloneLinux64:
                    return "Standalone";
                default:
                    return "DefaultTexturePlatform";
            }
        }
    }
}
