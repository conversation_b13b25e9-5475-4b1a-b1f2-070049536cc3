# 🎨 Complete Texture Formats Guide

This guide covers all texture compression formats supported by Unity and when to use them.

## 📱 Mobile Formats

### ASTC (Adaptive Scalable Texture Compression)
**Best for: Modern mobile devices (iOS, Android)**

- **ASTC 4x4**: Highest quality, larger file size
- **ASTC 5x5**: Very good quality, balanced size
- **ASTC 6x6**: Good quality, smaller size ⭐ **Recommended for most mobile textures**
- **ASTC 8x8**: Fair quality, small size
- **ASTC 10x10**: Lower quality, very small size
- **ASTC 12x12**: Lowest quality, maximum compression

**Pros:**
- Excellent quality-to-size ratio
- Supports alpha channel
- Variable compression rates
- Supported on modern iOS and Android devices

**Cons:**
- Not supported on older devices
- Requires OpenGL ES 3.2+ or Metal

### ETC2 (Ericsson Texture Compression 2)
**Best for: Android devices**

- **ETC2 RGB4**: 4 bits per pixel, no alpha
- **ETC2 RGBA8**: 8 bits per pixel, with alpha
- **ETC2 RGBA8 Crunched**: Compressed version with additional size reduction

**Pros:**
- Widely supported on Android
- Good compression ratio
- Part of OpenGL ES 3.0 standard

**Cons:**
- Limited to Android platform
- Lower quality than ASTC

### PVRTC (PowerVR Texture Compression)
**Best for: Older iOS devices**

- **PVRTC RGB2**: 2 bits per pixel, no alpha
- **PVRTC RGB4**: 4 bits per pixel, no alpha
- **PVRTC RGBA2**: 2 bits per pixel, with alpha
- **PVRTC RGBA4**: 4 bits per pixel, with alpha

**Pros:**
- Supported on all iOS devices
- Very small file sizes

**Cons:**
- Lower quality than ASTC
- Only works on PowerVR GPUs (iOS)
- Requires square, power-of-2 textures

## 🖥️ Desktop Formats

### DXT/S3TC (DirectX Texture Compression)
**Best for: PC, Xbox, WebGL**

- **DXT1**: 4 bits per pixel, no alpha, good for diffuse textures
- **DXT5**: 8 bits per pixel, with alpha, good for textures with transparency
- **DXT1 Crunched**: DXT1 with additional compression
- **DXT5 Crunched**: DXT5 with additional compression

**Pros:**
- Widely supported on desktop
- Good quality-to-performance ratio
- Hardware accelerated

**Cons:**
- Limited to desktop platforms
- Fixed compression ratios

### BC (Block Compression) - Modern Desktop
**Best for: Modern PC, Xbox Series X/S**

- **BC4**: Single channel compression (grayscale)
- **BC5**: Two channel compression (normal maps) ⭐ **Best for normal maps**
- **BC6H**: HDR compression for high dynamic range textures
- **BC7**: High quality RGBA compression ⭐ **Best quality for desktop**

**Pros:**
- Excellent quality
- Optimized for modern GPUs
- BC7 provides best quality compression

**Cons:**
- Requires DirectX 11+ support
- Larger file sizes than DXT

## 🌐 Web Formats

### WebGL Optimized
**Best for: Web deployment**

- **DXT1 Crunched**: Best for web textures without alpha
- **DXT5 Crunched**: Best for web textures with alpha
- **ETC2**: Alternative for WebGL 2.0

**Pros:**
- Optimized for web download
- Smaller file sizes
- Good browser support

**Cons:**
- Slightly lower quality due to additional compression

## 🎯 HDR Formats

### High Dynamic Range
**Best for: HDR rendering, skyboxes**

- **ASTC HDR 4x4 to 12x12**: Mobile HDR compression
- **BC6H**: Desktop HDR compression
- **RGBA Half**: 16-bit per channel HDR
- **RGBA Float**: 32-bit per channel HDR

**Pros:**
- Supports high dynamic range
- Good for realistic lighting

**Cons:**
- Larger file sizes
- Requires HDR-capable hardware

## 📊 Uncompressed Formats

### High Quality Uncompressed
**Best for: When quality is paramount**

- **RGBA32**: 32-bit RGBA, highest quality
- **RGB24**: 24-bit RGB, no alpha
- **RGBA16**: 16-bit RGBA, good quality
- **RGB16**: 16-bit RGB, no alpha
- **Alpha8**: 8-bit alpha only
- **R8**: 8-bit single channel

**Pros:**
- Perfect quality
- No compression artifacts
- Universal support

**Cons:**
- Very large file sizes
- High memory usage

## 🎯 Format Selection Guide

### By Platform Priority:

**Mobile (iOS):**
1. ASTC 6x6 (modern devices)
2. ASTC 4x4 (high quality)
3. PVRTC RGBA4 (compatibility)

**Mobile (Android):**
1. ASTC 6x6 (modern devices)
2. ETC2 RGBA8 (standard)
3. ETC2 RGB4 (no alpha)

**Desktop (PC):**
1. BC7 (highest quality)
2. DXT5 (with alpha)
3. DXT1 (no alpha)
4. BC5 (normal maps)

**Web (WebGL):**
1. DXT5 Crunched (with alpha)
2. DXT1 Crunched (no alpha)
3. ETC2 (WebGL 2.0)

### By Texture Type:

**UI Elements:**
- Mobile: ASTC 6x6
- Desktop: DXT5 or BC7
- Web: DXT5 Crunched

**Diffuse Textures:**
- Mobile: ASTC 6x6 or ETC2 RGB4
- Desktop: DXT1 or BC7
- Web: DXT1 Crunched

**Normal Maps:**
- Mobile: ASTC 6x6
- Desktop: BC5 ⭐ **Best choice**
- Web: DXT5

**Skyboxes/HDR:**
- Mobile: ASTC HDR 6x6
- Desktop: BC6H
- Web: RGBA Half

## ⚡ Performance Tips

1. **Use ASTC for mobile** - Best quality-to-size ratio
2. **Use BC7 for modern desktop** - Highest quality
3. **Use BC5 for normal maps** - Optimized for normal data
4. **Use Crunched formats for web** - Smaller downloads
5. **Test on target devices** - Verify format support
6. **Consider texture size** - Larger textures benefit more from compression

## 🔧 Unity Integration

The Texture Compressor tool automatically selects the best format based on:
- Target platform
- Texture content (alpha, normal maps, etc.)
- Texture size
- Usage context (UI, sprites, etc.)

Use the **Smart Format Recommendation** feature for automatic optimization!
