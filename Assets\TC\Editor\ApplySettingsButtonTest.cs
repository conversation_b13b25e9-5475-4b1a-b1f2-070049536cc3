using UnityEngine;
using UnityEditor;
using TextureCompressorTool;

/// <summary>
/// Test to verify the Apply Settings button functionality
/// </summary>
public static class ApplySettingsButtonTest
{
    [MenuItem("Tools/Texture Compressor/🧪 Test Apply Settings Button")]
    public static void TestApplySettingsButton()
    {
        Debug.Log("🧪 Testing Apply Settings Button...");
        
        // Test the new Apply Settings button functionality
        TestApplySettingsLogic();
        
        // Show result dialog
        string message = @"✅ Apply Settings Button Added Successfully!

NEW FEATURE:
⚡ Apply Settings button in Settings tab

LOCATION:
Settings tab → Compression Settings section → Green ""⚡ Apply Settings"" button

HOW IT WORKS:
1. Configure your settings in the Settings tab:
   • Max Texture Size (32-8192)
   • Compression Quality (0-100)
   • Crunch Compression (On/Off)
   • Generate Mipmaps (On/Off)
   • Filter Mode, Wrap Mode, etc.

2. Click ""⚡ Apply Settings"" button

3. Confirmation dialog shows:
   • Number of textures to process
   • Settings that will be applied
   • Platform target

4. Click ""Apply"" to confirm

5. Settings applied to all filtered textures!

FEATURES:
✅ Applies to all currently filtered textures
✅ Shows confirmation dialog with settings preview
✅ Progress bar during processing
✅ Detailed console logs for each texture
✅ Success/failure count at the end
✅ Automatic platform-specific format selection
✅ Respects current platform selection

BENEFITS:
🚀 One-click application of all settings
🎯 No need to go to Batch Operations tab
⚡ Direct from Settings tab
📊 Clear feedback and progress tracking
🔧 Applies optimal format for each texture

Try it now: Go to Settings tab → Configure settings → Click Apply Settings!";

        EditorUtility.DisplayDialog("Apply Settings Button Test", message, "OK");
    }
    
    private static void TestApplySettingsLogic()
    {
        Debug.Log("⚡ Testing Apply Settings Button Logic:");
        
        Debug.Log("BUTTON FEATURES:");
        Debug.Log("• Location: Settings tab, after Compression Settings");
        Debug.Log("• Style: Green button, 35px height, 200px width");
        Debug.Log("• Action: Calls ApplyCurrentSettings() method");
        
        Debug.Log("\nAPPLY SETTINGS PROCESS:");
        Debug.Log("1. Gets filtered textures from current scan");
        Debug.Log("2. Shows confirmation dialog with settings preview");
        Debug.Log("3. Applies settings to each texture individually");
        Debug.Log("4. Uses current platform for optimal format selection");
        Debug.Log("5. Shows progress bar during processing");
        Debug.Log("6. Displays final success/failure count");
        
        Debug.Log("\nSETTINGS APPLIED:");
        Debug.Log("• Max Texture Size → Applied to importer and platform settings");
        Debug.Log("• Compression Quality → Applied to platform settings");
        Debug.Log("• Crunch Compression → Applied to platform settings");
        Debug.Log("• Generate Mipmaps → Applied to importer");
        Debug.Log("• Filter Mode → Applied to importer");
        Debug.Log("• Wrap Mode → Applied to importer");
        Debug.Log("• sRGB Texture → Applied to importer");
        Debug.Log("• Alpha Is Transparency → Applied to importer");
        Debug.Log("• Streaming Mipmaps → Applied to importer");
        
        Debug.Log("\n✅ Apply Settings button ready to use!");
    }
    
    [MenuItem("Tools/Texture Compressor/📋 Show Apply Settings Info")]
    public static void ShowApplySettingsInfo()
    {
        string info = @"⚡ APPLY SETTINGS BUTTON FEATURE

LOCATION:
Settings tab → Compression Settings section → ""⚡ Apply Settings"" button

PURPOSE:
Allows you to immediately apply your current settings to all filtered textures 
without having to go to the Batch Operations tab.

HOW TO USE:

1. SCAN FOR TEXTURES:
   • Go to Scene Scan or Project Scan tab
   • Click scan to find textures

2. CONFIGURE SETTINGS:
   • Go to Settings tab
   • Set Max Texture Size (e.g., 512)
   • Set Compression Quality (e.g., 75)
   • Configure other options as needed

3. APPLY SETTINGS:
   • Click the green ""⚡ Apply Settings"" button
   • Review the confirmation dialog
   • Click ""Apply"" to confirm

4. MONITOR PROGRESS:
   • Watch the progress bar
   • Check console for detailed logs
   • See final success count

WHAT GETS APPLIED:

TEXTURE IMPORTER SETTINGS:
• maxTextureSize
• mipmapEnabled
• filterMode
• wrapMode
• sRGBTexture
• alphaIsTransparency
• streamingMipmaps
• streamingMipmapsPriority

PLATFORM-SPECIFIC SETTINGS:
• maxTextureSize (platform override)
• compressionQuality
• crunchedCompression
• format (optimal for platform and texture type)

SMART FEATURES:

1. PLATFORM-AWARE:
   • Uses GetCurrentPlatformName() for correct platform
   • Applies optimal format for selected platform
   • Sets platformSettings.overridden = true

2. TEXTURE-AWARE:
   • Analyzes each texture (alpha, size, type, usage)
   • Selects optimal format using GetOptimalFormatForTexture()
   • Respects texture type (sprite, UI, normal map, etc.)

3. USER-FRIENDLY:
   • Confirmation dialog with settings preview
   • Progress bar with texture names
   • Detailed console logging
   • Success/failure count
   • Error handling for individual textures

EXAMPLE WORKFLOW:
1. Project Scan → Find 50 textures
2. Settings tab → Set Max Size: 512, Quality: 75
3. Click ""⚡ Apply Settings""
4. Confirm → ""Apply to 50 textures? Max Size: 512, Quality: 75""
5. Processing → ""Applying settings to texture1.png... (1/50)""
6. Complete → ""✅ Successfully applied settings to 50/50 textures!""

BENEFITS:
✅ One-click application from Settings tab
✅ No need to switch to Batch Operations
✅ Clear preview of what will be applied
✅ Detailed feedback and logging
✅ Handles errors gracefully
✅ Works with any filtered texture set";

        Debug.Log(info);
        EditorGUIUtility.systemCopyBuffer = info;
        
        EditorUtility.DisplayDialog("Apply Settings Info", 
            "Complete Apply Settings information has been logged to console and copied to clipboard!", 
            "OK");
    }
    
    [MenuItem("Tools/Texture Compressor/🔍 Debug Apply Settings Process")]
    public static void DebugApplySettingsProcess()
    {
        var window = EditorWindow.GetWindow<TextureCompressorWindow>();
        
        // Use reflection to access current settings
        var fields = new System.Collections.Generic.Dictionary<string, System.Reflection.FieldInfo>
        {
            ["maxTextureSize"] = typeof(TextureCompressorWindow).GetField("maxTextureSize", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["compressionQuality"] = typeof(TextureCompressorWindow).GetField("compressionQuality", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["useCrunchCompression"] = typeof(TextureCompressorWindow).GetField("useCrunchCompression", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["generateMipmaps"] = typeof(TextureCompressorWindow).GetField("generateMipmaps", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["selectedPlatform"] = typeof(TextureCompressorWindow).GetField("selectedPlatform", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["filterMode"] = typeof(TextureCompressorWindow).GetField("filterMode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance),
            ["wrapMode"] = typeof(TextureCompressorWindow).GetField("wrapMode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
        };
        
        Debug.Log("🔍 CURRENT SETTINGS THAT WILL BE APPLIED:");
        
        try
        {
            int maxTextureSize = (int)fields["maxTextureSize"].GetValue(window);
            int compressionQuality = (int)fields["compressionQuality"].GetValue(window);
            bool useCrunchCompression = (bool)fields["useCrunchCompression"].GetValue(window);
            bool generateMipmaps = (bool)fields["generateMipmaps"].GetValue(window);
            var selectedPlatform = (BuildTarget)fields["selectedPlatform"].GetValue(window);
            var filterMode = (FilterMode)fields["filterMode"].GetValue(window);
            var wrapMode = (TextureWrapMode)fields["wrapMode"].GetValue(window);
            
            Debug.Log($"📱 Target Platform: {selectedPlatform}");
            Debug.Log($"📏 Max Texture Size: {maxTextureSize}");
            Debug.Log($"⚙️ Compression Quality: {compressionQuality}");
            Debug.Log($"🗜️ Crunch Compression: {(useCrunchCompression ? "Enabled" : "Disabled")}");
            Debug.Log($"🔍 Generate Mipmaps: {(generateMipmaps ? "Enabled" : "Disabled")}");
            Debug.Log($"🎨 Filter Mode: {filterMode}");
            Debug.Log($"🔄 Wrap Mode: {wrapMode}");
            
            Debug.Log("\n💡 These settings will be applied when you click '⚡ Apply Settings'!");
            Debug.Log("💡 Format will be automatically selected based on platform and texture properties.");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Could not access settings via reflection: {e.Message}");
        }
    }
}
