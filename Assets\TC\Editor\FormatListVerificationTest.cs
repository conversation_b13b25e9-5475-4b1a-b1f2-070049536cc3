using UnityEngine;
using UnityEditor;
using TextureCompressorTool;

/// <summary>
/// Test to verify that format list matches the user's screenshot exactly
/// </summary>
public static class FormatListVerificationTest
{
    [MenuItem("Tools/Texture Compressor/🧪 Verify Format List")]
    public static void VerifyFormatList()
    {
        Debug.Log("🧪 Verifying Format List Against Screenshot...");
        
        // Expected formats from user's screenshot (in exact order)
        string[] expectedFormats = new string[]
        {
            "RGBA Compressed ASTC 12x12 block",
            "RGBA Compressed ASTC 10x10 block", 
            "RGBA Compressed ASTC 8x8 block",
            "RGBA Compressed ASTC 6x6 block",
            "RGBA Compressed ASTC 5x5 block",
            "RGBA Compressed ASTC 4x4 block",
            "RGBA Compressed ETC2 8 bits",
            "RGB + 1-bit Alpha Compressed ETC2 4 bits",
            "RGBA Compressed PVRTC 4 bits",
            "RGBA Compressed PVRTC 2 bits",
            "RGBA Compressed DXT5|BC3",
            "RGBA Crunched ETC2",
            "RGBA Crunched DXT5|BC3",
            "RGBA 64 bit",
            "RGBA 16 bit", 
            "RGBA 32 bit",
            "RGB Compressed ETC2 4 bits",
            "RGB Compressed ETC 4 bits",
            "RGB Compressed PVRTC 4 bits",
            "RGB Compressed PVRTC 2 bits",
            "RGB Compressed DXT1|BC1",
            "RGB Crunched ETC",
            "RGB Crunched DXT1|BC1",
            "RGB 48 bit",
            "RGB 16 bit",
            "RGB 24 bit",
            "RG Compressed EAC 8 bit",
            "RG 32 bit",
            "R Compressed EAC 4 bit",
            "R 8",
            "R 16 bit",
            "Alpha 8",
            "RGBA Float",
            "RGBA Half",
            "RG Float",
            "R Float",
            "RGBA Compressed ASTC HDR 12x12 block",
            "RGBA Compressed ASTC HDR 10x10 block",
            "RGBA Compressed ASTC HDR 8x8 block"
        };
        
        // Get current format list from the tool
        var window = EditorWindow.GetWindow<TextureCompressorWindow>();
        var formatField = typeof(TextureCompressorWindow).GetField("allTextureFormats", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (formatField == null)
        {
            Debug.LogError("❌ Could not access allTextureFormats field");
            return;
        }
        
        string[] currentFormats = (string[])formatField.GetValue(window);
        
        if (currentFormats == null)
        {
            Debug.LogError("❌ allTextureFormats is null");
            return;
        }
        
        Debug.Log($"📊 Expected formats: {expectedFormats.Length}");
        Debug.Log($"📊 Current formats: {currentFormats.Length}");
        
        // Check if lists match exactly
        bool listsMatch = true;
        int maxLength = Mathf.Max(expectedFormats.Length, currentFormats.Length);
        
        for (int i = 0; i < maxLength; i++)
        {
            string expected = i < expectedFormats.Length ? expectedFormats[i] : "[MISSING]";
            string current = i < currentFormats.Length ? currentFormats[i] : "[MISSING]";
            
            if (expected != current)
            {
                Debug.LogError($"❌ Mismatch at index {i}:");
                Debug.LogError($"   Expected: '{expected}'");
                Debug.LogError($"   Current:  '{current}'");
                listsMatch = false;
            }
            else if (i < expectedFormats.Length && i < currentFormats.Length)
            {
                Debug.Log($"✅ Match at index {i}: '{expected}'");
            }
        }
        
        if (listsMatch && expectedFormats.Length == currentFormats.Length)
        {
            Debug.Log("🎉 SUCCESS: Format list matches screenshot exactly!");
            
            string successMessage = @"✅ FORMAT LIST VERIFICATION SUCCESSFUL!

The texture format list now matches your screenshot exactly:

TOTAL FORMATS: " + expectedFormats.Length + @"

KEY FORMATS INCLUDED:
• RGBA Compressed ASTC (12x12, 10x10, 8x8, 6x6, 5x5, 4x4)
• RGBA Compressed ETC2 8 bits
• RGB + 1-bit Alpha Compressed ETC2 4 bits  
• RGBA Compressed PVRTC (4 bits, 2 bits)
• RGBA Compressed DXT5|BC3
• RGBA Crunched (ETC2, DXT5|BC3)
• RGBA bit formats (64, 32, 16 bit)
• RGB Compressed formats (ETC2, ETC, PVRTC, DXT1|BC1)
• RGB Crunched formats (ETC, DXT1|BC1)
• RGB bit formats (48, 24, 16 bit)
• RG Compressed EAC 8 bit
• R Compressed EAC 4 bit
• Alpha 8
• HDR formats (RGBA Float, RGBA Half, RG Float, R Float)
• ASTC HDR formats (12x12, 10x10, 8x8)

All formats are in the exact order shown in your screenshot!";

            EditorUtility.DisplayDialog("Format List Verified", successMessage, "OK");
        }
        else
        {
            Debug.LogError("❌ FAILED: Format list does not match screenshot");
            
            string errorMessage = $@"❌ FORMAT LIST MISMATCH

Expected: {expectedFormats.Length} formats
Current:  {currentFormats.Length} formats

Check the Console for detailed mismatch information.
The format list needs to be updated to match your screenshot exactly.";

            EditorUtility.DisplayDialog("Format List Mismatch", errorMessage, "OK");
        }
    }
    
    [MenuItem("Tools/Texture Compressor/📋 Show Screenshot Format List")]
    public static void ShowScreenshotFormatList()
    {
        string formatList = @"📸 FORMATS FROM YOUR SCREENSHOT:

1. RGBA Compressed ASTC 12x12 block
2. RGBA Compressed ASTC 10x10 block
3. RGBA Compressed ASTC 8x8 block
4. RGBA Compressed ASTC 6x6 block
5. RGBA Compressed ASTC 5x5 block
6. RGBA Compressed ASTC 4x4 block
7. RGBA Compressed ETC2 8 bits
8. RGB + 1-bit Alpha Compressed ETC2 4 bits
9. RGBA Compressed PVRTC 4 bits
10. RGBA Compressed PVRTC 2 bits
11. RGBA Compressed DXT5|BC3
12. RGBA Crunched ETC2
13. RGBA Crunched DXT5|BC3
14. RGBA 64 bit
15. RGBA 16 bit
16. RGBA 32 bit
17. RGB Compressed ETC2 4 bits
18. RGB Compressed ETC 4 bits
19. RGB Compressed PVRTC 4 bits
20. RGB Compressed PVRTC 2 bits
21. RGB Compressed DXT1|BC1
22. RGB Crunched ETC
23. RGB Crunched DXT1|BC1
24. RGB 48 bit
25. RGB 16 bit
26. RGB 24 bit
27. RG Compressed EAC 8 bit
28. RG 32 bit
29. R Compressed EAC 4 bit
30. R 8
31. R 16 bit
32. Alpha 8
33. RGBA Float
34. RGBA Half
35. RG Float
36. R Float
37. RGBA Compressed ASTC HDR 12x12 block
38. RGBA Compressed ASTC HDR 10x10 block
39. RGBA Compressed ASTC HDR 8x8 block

TOTAL: 39 formats

These are the exact formats visible in your screenshot, 
in the exact order they appear.";

        Debug.Log(formatList);
        EditorGUIUtility.systemCopyBuffer = formatList;
        
        EditorUtility.DisplayDialog("Screenshot Format List", 
            "Complete format list from your screenshot has been logged to console and copied to clipboard!", 
            "OK");
    }
    
    [MenuItem("Tools/Texture Compressor/🔍 Debug Current Format List")]
    public static void DebugCurrentFormatList()
    {
        var window = EditorWindow.GetWindow<TextureCompressorWindow>();
        var formatField = typeof(TextureCompressorWindow).GetField("allTextureFormats", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (formatField == null)
        {
            Debug.LogError("❌ Could not access allTextureFormats field");
            return;
        }
        
        string[] currentFormats = (string[])formatField.GetValue(window);
        
        if (currentFormats == null)
        {
            Debug.LogError("❌ allTextureFormats is null");
            return;
        }
        
        Debug.Log("🔍 CURRENT FORMAT LIST:");
        Debug.Log($"Total formats: {currentFormats.Length}");
        
        for (int i = 0; i < currentFormats.Length; i++)
        {
            Debug.Log($"{i + 1:D2}. {currentFormats[i]}");
        }
        
        // Also copy to clipboard
        string formatListText = "CURRENT FORMAT LIST:\n";
        for (int i = 0; i < currentFormats.Length; i++)
        {
            formatListText += $"{i + 1:D2}. {currentFormats[i]}\n";
        }
        
        EditorGUIUtility.systemCopyBuffer = formatListText;
        Debug.Log("📋 Format list copied to clipboard!");
    }
}
