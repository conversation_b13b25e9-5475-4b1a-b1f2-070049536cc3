
=== Mon Jun 30 10:46:46 2025

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.modules.ai@1.0.0
  com.unity.modules.androidjni@1.0.0
  com.unity.modules.animation@1.0.0
  com.unity.modules.assetbundle@1.0.0
  com.unity.modules.audio@1.0.0
  com.unity.modules.cloth@1.0.0
  com.unity.modules.director@1.0.0
  com.unity.modules.imageconversion@1.0.0
  com.unity.modules.imgui@1.0.0
  com.unity.modules.jsonserialize@1.0.0
  com.unity.modules.particlesystem@1.0.0
  com.unity.modules.physics@1.0.0
  com.unity.modules.physics2d@1.0.0
  com.unity.modules.screencapture@1.0.0
  com.unity.modules.terrain@1.0.0
  com.unity.modules.terrainphysics@1.0.0
  com.unity.modules.tilemap@1.0.0
  com.unity.modules.ui@1.0.0
  com.unity.modules.uielements@1.0.0
  com.unity.modules.umbra@1.0.0
  com.unity.modules.unityanalytics@1.0.0
  com.unity.modules.unitywebrequest@1.0.0
  com.unity.modules.unitywebrequestassetbundle@1.0.0
  com.unity.modules.unitywebrequestaudio@1.0.0
  com.unity.modules.unitywebrequesttexture@1.0.0
  com.unity.modules.unitywebrequestwww@1.0.0
  com.unity.modules.vehicles@1.0.0
  com.unity.modules.video@1.0.0
  com.unity.modules.vr@1.0.0
  com.unity.modules.wind@1.0.0
  com.unity.modules.xr@1.0.0
  com.unity.modules.accessibility@1.0.0
  com.unity.multiplayer.center@1.0.0
The following packages were updated:
  com.unity.collab-proxy from version 2.3.1 to 2.5.2
  com.unity.timeline from version 1.8.6 to 1.8.7
  com.unity.visualscripting from version 1.9.2 to 1.9.5
