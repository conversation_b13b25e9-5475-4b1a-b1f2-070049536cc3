Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker1.log
-srvPort
52612
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20912]  Target information:

Player connection [20912]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3830583524 [EditorId] 3830583524 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20912]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3830583524 [EditorId] 3830583524 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20912] Host joined multi-casting on [***********:54997]...
Player connection [20912] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56936
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006710 seconds.
- Loaded All Assemblies, in  0.731 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 390 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.012 seconds
Domain Reload Profiling: 1739ms
	BeginReloadAssembly (261ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (285ms)
		LoadAssemblies (258ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (279ms)
			TypeCache.Refresh (277ms)
				TypeCache.ScanAssembly (253ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1012ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (941ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (543ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (196ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.335 seconds
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 2267ms
	BeginReloadAssembly (298ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (884ms)
		LoadAssemblies (704ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (362ms)
			TypeCache.Refresh (287ms)
				TypeCache.ScanAssembly (259ms)
			BuildScriptInfoCaches (63ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (738ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (136ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (1.0 MB). Loaded Objects now: 4192.
Memory consumption went from 83.0 MB to 82.0 MB.
Total: 8.236300 ms (FindLiveObjects: 0.561100 ms CreateObjectMapping: 0.362800 ms MarkObjects: 5.829000 ms  DeleteObjects: 1.478100 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3698 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 80.5 MB to 79.6 MB.
Total: 10.007300 ms (FindLiveObjects: 0.617800 ms CreateObjectMapping: 0.399200 ms MarkObjects: 7.409600 ms  DeleteObjects: 1.579500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3699 unused Assets / (0.8 MB). Loaded Objects now: 4194.
Memory consumption went from 80.5 MB to 79.7 MB.
Total: 8.678900 ms (FindLiveObjects: 0.592500 ms CreateObjectMapping: 0.396600 ms MarkObjects: 6.144100 ms  DeleteObjects: 1.544200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3695 unused Assets / (0.9 MB). Loaded Objects now: 4190.
Memory consumption went from 80.5 MB to 79.6 MB.
Total: 11.742600 ms (FindLiveObjects: 0.646000 ms CreateObjectMapping: 0.424700 ms MarkObjects: 9.099000 ms  DeleteObjects: 1.570200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.060 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 1988ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (591ms)
		LoadAssemblies (500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (242ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (730ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (502ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.9 MB). Loaded Objects now: 4191.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 8.149600 ms (FindLiveObjects: 0.557300 ms CreateObjectMapping: 0.358300 ms MarkObjects: 5.871500 ms  DeleteObjects: 1.361500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3697 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.2 MB to 81.3 MB.
Total: 9.294800 ms (FindLiveObjects: 0.666900 ms CreateObjectMapping: 0.418900 ms MarkObjects: 6.695100 ms  DeleteObjects: 1.512900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3697 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.2 MB to 81.3 MB.
Total: 10.907300 ms (FindLiveObjects: 0.624400 ms CreateObjectMapping: 0.404600 ms MarkObjects: 8.350200 ms  DeleteObjects: 1.526100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.031 seconds
Refreshing native plugins compatible for Editor in 2.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 1985ms
	BeginReloadAssembly (323ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (496ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (956ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (732ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.0 MB). Loaded Objects now: 4194.
Memory consumption went from 82.1 MB to 81.1 MB.
Total: 8.238800 ms (FindLiveObjects: 0.541700 ms CreateObjectMapping: 0.348300 ms MarkObjects: 5.929700 ms  DeleteObjects: 1.417900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.051 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 1986ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (563ms)
		LoadAssemblies (501ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (736ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.6 MB). Loaded Objects now: 4194.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 13.512400 ms (FindLiveObjects: 0.890500 ms CreateObjectMapping: 0.688600 ms MarkObjects: 10.669500 ms  DeleteObjects: 1.260600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3699 unused Assets / (0.9 MB). Loaded Objects now: 4195.
Memory consumption went from 82.2 MB to 81.4 MB.
Total: 11.217800 ms (FindLiveObjects: 1.148100 ms CreateObjectMapping: 0.427000 ms MarkObjects: 7.467700 ms  DeleteObjects: 2.173200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.066 seconds
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.968 seconds
Domain Reload Profiling: 2033ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (613ms)
		LoadAssemblies (510ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (969ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (717ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (0.9 MB). Loaded Objects now: 4195.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 12.569600 ms (FindLiveObjects: 0.734100 ms CreateObjectMapping: 0.396600 ms MarkObjects: 9.962400 ms  DeleteObjects: 1.474200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.088 seconds
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.010 seconds
Domain Reload Profiling: 2096ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (607ms)
		LoadAssemblies (538ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (243ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1011ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (742ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (514ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (0.9 MB). Loaded Objects now: 4195.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 8.204800 ms (FindLiveObjects: 0.580800 ms CreateObjectMapping: 0.378000 ms MarkObjects: 5.802000 ms  DeleteObjects: 1.442100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.034 seconds
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.908 seconds
Domain Reload Profiling: 1940ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (570ms)
		LoadAssemblies (496ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (909ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (689ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (134ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3706 unused Assets / (0.9 MB). Loaded Objects now: 4195.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 8.884100 ms (FindLiveObjects: 0.678700 ms CreateObjectMapping: 0.424200 ms MarkObjects: 6.253600 ms  DeleteObjects: 1.525500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3701 unused Assets / (0.9 MB). Loaded Objects now: 4197.
Memory consumption went from 82.3 MB to 81.4 MB.
Total: 11.089500 ms (FindLiveObjects: 0.631200 ms CreateObjectMapping: 0.437800 ms MarkObjects: 8.440300 ms  DeleteObjects: 1.577700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (1.0 MB). Loaded Objects now: 4198.
Memory consumption went from 82.3 MB to 81.3 MB.
Total: 14.915600 ms (FindLiveObjects: 1.212300 ms CreateObjectMapping: 0.914400 ms MarkObjects: 10.210600 ms  DeleteObjects: 2.575500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3703 unused Assets / (1.2 MB). Loaded Objects now: 4199.
Memory consumption went from 82.3 MB to 81.0 MB.
Total: 14.023200 ms (FindLiveObjects: 0.744600 ms CreateObjectMapping: 0.407900 ms MarkObjects: 10.310800 ms  DeleteObjects: 2.557300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.131 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.950 seconds
Domain Reload Profiling: 2080ms
	BeginReloadAssembly (371ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (612ms)
		LoadAssemblies (526ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (280ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (254ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (731ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3710 unused Assets / (0.6 MB). Loaded Objects now: 4199.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 8.052600 ms (FindLiveObjects: 0.628000 ms CreateObjectMapping: 0.374700 ms MarkObjects: 5.841500 ms  DeleteObjects: 1.207300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.9 MB). Loaded Objects now: 4201.
Memory consumption went from 82.3 MB to 81.3 MB.
Total: 11.829800 ms (FindLiveObjects: 0.593200 ms CreateObjectMapping: 0.360200 ms MarkObjects: 8.876500 ms  DeleteObjects: 1.997200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.2 MB). Loaded Objects now: 4201.
Memory consumption went from 82.3 MB to 81.1 MB.
Total: 15.655700 ms (FindLiveObjects: 0.903200 ms CreateObjectMapping: 0.560800 ms MarkObjects: 11.938300 ms  DeleteObjects: 2.251000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.097 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.902 seconds
Domain Reload Profiling: 1998ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (614ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (258ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (680ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (469ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.6 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 8.546100 ms (FindLiveObjects: 0.694600 ms CreateObjectMapping: 0.385700 ms MarkObjects: 6.198000 ms  DeleteObjects: 1.265900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 4201.
Memory consumption went from 79.6 MB to 79.6 MB.
Total: 10.039200 ms (FindLiveObjects: 0.511700 ms CreateObjectMapping: 0.199100 ms MarkObjects: 9.309900 ms  DeleteObjects: 0.016300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 4201.
Memory consumption went from 79.6 MB to 79.6 MB.
Total: 6.991100 ms (FindLiveObjects: 0.341600 ms CreateObjectMapping: 0.147000 ms MarkObjects: 6.490400 ms  DeleteObjects: 0.011000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.129 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.944 seconds
Domain Reload Profiling: 2071ms
	BeginReloadAssembly (354ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (631ms)
		LoadAssemblies (526ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (308ms)
			TypeCache.Refresh (130ms)
				TypeCache.ScanAssembly (112ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (944ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (745ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (518ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.6 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 8.452900 ms (FindLiveObjects: 0.650900 ms CreateObjectMapping: 0.427200 ms MarkObjects: 6.092400 ms  DeleteObjects: 1.281200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.7 MB). Loaded Objects now: 4201.
Memory consumption went from 82.3 MB to 81.6 MB.
Total: 9.720200 ms (FindLiveObjects: 0.591100 ms CreateObjectMapping: 0.383500 ms MarkObjects: 7.464300 ms  DeleteObjects: 1.279400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (0.8 MB). Loaded Objects now: 4201.
Memory consumption went from 82.3 MB to 81.5 MB.
Total: 9.703100 ms (FindLiveObjects: 0.640700 ms CreateObjectMapping: 0.381000 ms MarkObjects: 7.340800 ms  DeleteObjects: 1.339000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.050 seconds
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.902 seconds
Domain Reload Profiling: 1950ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (577ms)
		LoadAssemblies (489ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.6 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 7.874100 ms (FindLiveObjects: 0.584300 ms CreateObjectMapping: 0.387900 ms MarkObjects: 5.728700 ms  DeleteObjects: 1.172000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.586 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.931 seconds
Domain Reload Profiling: 2517ms
	BeginReloadAssembly (589ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (850ms)
		LoadAssemblies (806ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (287ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (248ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (932ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (724ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (497ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.5 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 9.982300 ms (FindLiveObjects: 0.922900 ms CreateObjectMapping: 0.718800 ms MarkObjects: 6.522600 ms  DeleteObjects: 1.814900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.046 seconds
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.896 seconds
Domain Reload Profiling: 1941ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (585ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (235ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (896ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (689ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (477ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.8 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.3 MB.
Total: 8.096100 ms (FindLiveObjects: 0.570900 ms CreateObjectMapping: 0.366300 ms MarkObjects: 5.748600 ms  DeleteObjects: 1.409000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.039 seconds
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.905 seconds
Domain Reload Profiling: 1943ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (575ms)
		LoadAssemblies (500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (705ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3712 unused Assets / (0.9 MB). Loaded Objects now: 4201.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 7.955400 ms (FindLiveObjects: 0.579400 ms CreateObjectMapping: 0.372900 ms MarkObjects: 5.647800 ms  DeleteObjects: 1.354200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.033 seconds
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.969 seconds
Domain Reload Profiling: 2001ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (568ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (970ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (769ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (549ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3714 unused Assets / (0.9 MB). Loaded Objects now: 4203.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 8.340200 ms (FindLiveObjects: 0.582000 ms CreateObjectMapping: 0.394200 ms MarkObjects: 5.906500 ms  DeleteObjects: 1.455500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3708 unused Assets / (0.9 MB). Loaded Objects now: 4204.
Memory consumption went from 82.3 MB to 81.4 MB.
Total: 22.072700 ms (FindLiveObjects: 0.840400 ms CreateObjectMapping: 0.698700 ms MarkObjects: 18.746700 ms  DeleteObjects: 1.784400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3708 unused Assets / (0.7 MB). Loaded Objects now: 4204.
Memory consumption went from 82.3 MB to 81.6 MB.
Total: 11.004200 ms (FindLiveObjects: 0.889200 ms CreateObjectMapping: 0.507500 ms MarkObjects: 7.929400 ms  DeleteObjects: 1.676400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.159 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.934 seconds
Domain Reload Profiling: 2092ms
	BeginReloadAssembly (390ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (620ms)
		LoadAssemblies (560ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (733ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (486ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3716 unused Assets / (1.0 MB). Loaded Objects now: 4205.
Memory consumption went from 82.1 MB to 81.1 MB.
Total: 14.653700 ms (FindLiveObjects: 0.705500 ms CreateObjectMapping: 0.391000 ms MarkObjects: 10.798500 ms  DeleteObjects: 2.757100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.130 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.940 seconds
Domain Reload Profiling: 2070ms
	BeginReloadAssembly (376ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (75ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (610ms)
		LoadAssemblies (528ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (257ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (941ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (496ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3716 unused Assets / (0.9 MB). Loaded Objects now: 4205.
Memory consumption went from 82.1 MB to 81.3 MB.
Total: 9.405900 ms (FindLiveObjects: 0.553400 ms CreateObjectMapping: 0.393400 ms MarkObjects: 7.174100 ms  DeleteObjects: 1.284000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.072 seconds
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.951 seconds
Domain Reload Profiling: 2021ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (599ms)
		LoadAssemblies (504ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (256ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (731ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3717 unused Assets / (0.6 MB). Loaded Objects now: 4206.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 8.419300 ms (FindLiveObjects: 0.615600 ms CreateObjectMapping: 0.387600 ms MarkObjects: 5.982200 ms  DeleteObjects: 1.431700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.107 seconds
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 2062ms
	BeginReloadAssembly (351ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (610ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (254ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (750ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (525ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3717 unused Assets / (0.5 MB). Loaded Objects now: 4206.
Memory consumption went from 82.1 MB to 81.6 MB.
Total: 8.152400 ms (FindLiveObjects: 0.669800 ms CreateObjectMapping: 0.457200 ms MarkObjects: 5.852800 ms  DeleteObjects: 1.170700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.348 seconds
Refreshing native plugins compatible for Editor in 1.84 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.133 seconds
Domain Reload Profiling: 2482ms
	BeginReloadAssembly (401ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (108ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (751ms)
		LoadAssemblies (674ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (326ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (284ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1134ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (566ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3718 unused Assets / (1.0 MB). Loaded Objects now: 4207.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 9.567300 ms (FindLiveObjects: 0.970700 ms CreateObjectMapping: 0.423100 ms MarkObjects: 6.535800 ms  DeleteObjects: 1.635100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.081 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.938 seconds
Domain Reload Profiling: 2018ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (588ms)
		LoadAssemblies (506ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (246ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (939ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (730ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (515ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3719 unused Assets / (0.8 MB). Loaded Objects now: 4208.
Memory consumption went from 82.1 MB to 81.3 MB.
Total: 8.116400 ms (FindLiveObjects: 0.563900 ms CreateObjectMapping: 0.372300 ms MarkObjects: 5.834600 ms  DeleteObjects: 1.343900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.055 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.910 seconds
Domain Reload Profiling: 1964ms
	BeginReloadAssembly (351ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (564ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (707ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3719 unused Assets / (1.1 MB). Loaded Objects now: 4208.
Memory consumption went from 82.1 MB to 81.0 MB.
Total: 15.325300 ms (FindLiveObjects: 0.919800 ms CreateObjectMapping: 0.723500 ms MarkObjects: 10.926200 ms  DeleteObjects: 2.752400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.007 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.907 seconds
Domain Reload Profiling: 1914ms
	BeginReloadAssembly (313ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (566ms)
		LoadAssemblies (477ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (232ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (908ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (706ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3720 unused Assets / (0.6 MB). Loaded Objects now: 4209.
Memory consumption went from 82.1 MB to 81.5 MB.
Total: 8.515300 ms (FindLiveObjects: 0.692400 ms CreateObjectMapping: 0.400000 ms MarkObjects: 6.099200 ms  DeleteObjects: 1.322600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.082 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.881 seconds
Domain Reload Profiling: 1963ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (609ms)
		LoadAssemblies (483ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (252ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (678ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3720 unused Assets / (1.1 MB). Loaded Objects now: 4209.
Memory consumption went from 82.1 MB to 81.0 MB.
Total: 9.504800 ms (FindLiveObjects: 0.592000 ms CreateObjectMapping: 0.404500 ms MarkObjects: 5.876000 ms  DeleteObjects: 2.630100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.056 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.935 seconds
Domain Reload Profiling: 1990ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (523ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (225ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (734ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (498ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3721 unused Assets / (0.8 MB). Loaded Objects now: 4210.
Memory consumption went from 82.1 MB to 81.3 MB.
Total: 8.155500 ms (FindLiveObjects: 0.796500 ms CreateObjectMapping: 0.370400 ms MarkObjects: 5.651800 ms  DeleteObjects: 1.335400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3714 unused Assets / (0.9 MB). Loaded Objects now: 4210.
Memory consumption went from 82.3 MB to 81.4 MB.
Total: 9.946700 ms (FindLiveObjects: 0.711400 ms CreateObjectMapping: 0.428600 ms MarkObjects: 7.267500 ms  DeleteObjects: 1.537900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.055 seconds
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.952 seconds
Domain Reload Profiling: 2006ms
	BeginReloadAssembly (343ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (570ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (953ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (744ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (519ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3721 unused Assets / (0.9 MB). Loaded Objects now: 4210.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 8.540800 ms (FindLiveObjects: 0.632100 ms CreateObjectMapping: 0.393800 ms MarkObjects: 6.021600 ms  DeleteObjects: 1.492000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.035 seconds
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.912 seconds
Domain Reload Profiling: 1946ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (556ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (912ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (495ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3722 unused Assets / (0.6 MB). Loaded Objects now: 4211.
Memory consumption went from 82.1 MB to 81.6 MB.
Total: 7.964200 ms (FindLiveObjects: 0.576100 ms CreateObjectMapping: 0.369900 ms MarkObjects: 5.726400 ms  DeleteObjects: 1.290600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.114 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.928 seconds
Domain Reload Profiling: 2040ms
	BeginReloadAssembly (361ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (607ms)
		LoadAssemblies (528ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (929ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (733ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (501ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3722 unused Assets / (0.6 MB). Loaded Objects now: 4211.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 8.484200 ms (FindLiveObjects: 0.878500 ms CreateObjectMapping: 0.730600 ms MarkObjects: 5.569300 ms  DeleteObjects: 1.302700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.035 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.875 seconds
Domain Reload Profiling: 1909ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (554ms)
		LoadAssemblies (481ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (875ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (671ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (467ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.6 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.5 MB.
Total: 7.999800 ms (FindLiveObjects: 0.614200 ms CreateObjectMapping: 0.396200 ms MarkObjects: 5.632900 ms  DeleteObjects: 1.355300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.145 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.905 seconds
Domain Reload Profiling: 2049ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (636ms)
		LoadAssemblies (553ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (296ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (267ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (698ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (478ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.6 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 8.263600 ms (FindLiveObjects: 0.657500 ms CreateObjectMapping: 0.384100 ms MarkObjects: 5.922000 ms  DeleteObjects: 1.298100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.068 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.948 seconds
Domain Reload Profiling: 2015ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (42ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (589ms)
		LoadAssemblies (488ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (241ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (711ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (505ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.6 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 7.995000 ms (FindLiveObjects: 0.581800 ms CreateObjectMapping: 0.364300 ms MarkObjects: 5.754000 ms  DeleteObjects: 1.293500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.120 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 2075ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (598ms)
		LoadAssemblies (528ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (245ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (741ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (515ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (1.1 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.1 MB.
Total: 9.222200 ms (FindLiveObjects: 0.536800 ms CreateObjectMapping: 0.328900 ms MarkObjects: 5.715500 ms  DeleteObjects: 2.638900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.120 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.950 seconds
Domain Reload Profiling: 2070ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (644ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (303ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (268ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (951ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (704ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (495ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.7 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.5 MB.
Total: 8.270400 ms (FindLiveObjects: 0.592800 ms CreateObjectMapping: 0.351000 ms MarkObjects: 5.860200 ms  DeleteObjects: 1.464900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.100 seconds
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.911 seconds
Domain Reload Profiling: 2009ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (263ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (715ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (505ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (1.0 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.1 MB.
Total: 15.065800 ms (FindLiveObjects: 0.928700 ms CreateObjectMapping: 0.737300 ms MarkObjects: 10.897100 ms  DeleteObjects: 2.500600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.148 seconds
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 2032ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (101ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (522ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (257ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (885ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (686ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.6 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 8.431000 ms (FindLiveObjects: 0.573100 ms CreateObjectMapping: 0.324000 ms MarkObjects: 6.097600 ms  DeleteObjects: 1.435200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.018 seconds
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.933 seconds
Domain Reload Profiling: 1952ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (566ms)
		LoadAssemblies (479ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (933ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (725ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (509ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.8 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.4 MB.
Total: 7.654600 ms (FindLiveObjects: 0.543500 ms CreateObjectMapping: 0.389400 ms MarkObjects: 5.459300 ms  DeleteObjects: 1.261300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.997 seconds
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.898 seconds
Domain Reload Profiling: 1894ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (549ms)
		LoadAssemblies (471ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (226ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (898ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (700ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (469ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (1.1 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.0 MB.
Total: 15.057500 ms (FindLiveObjects: 0.852800 ms CreateObjectMapping: 0.706200 ms MarkObjects: 10.839500 ms  DeleteObjects: 2.656800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.054 seconds
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.895 seconds
Domain Reload Profiling: 1948ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (559ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (228ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (896ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (684ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (467ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (1.1 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.1 MB.
Total: 14.802100 ms (FindLiveObjects: 0.845100 ms CreateObjectMapping: 0.696200 ms MarkObjects: 10.621100 ms  DeleteObjects: 2.636300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.031 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.899 seconds
Domain Reload Profiling: 1929ms
	BeginReloadAssembly (336ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (561ms)
		LoadAssemblies (491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (899ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (691ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (464ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.6 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 7.927600 ms (FindLiveObjects: 0.584900 ms CreateObjectMapping: 0.362500 ms MarkObjects: 5.750400 ms  DeleteObjects: 1.228100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.036 seconds
Refreshing native plugins compatible for Editor in 2.08 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.931 seconds
Domain Reload Profiling: 1966ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (579ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (223ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (932ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (713ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (492ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.9 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.3 MB.
Total: 8.392400 ms (FindLiveObjects: 0.638400 ms CreateObjectMapping: 0.378300 ms MarkObjects: 5.916300 ms  DeleteObjects: 1.458200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.045 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.940 seconds
Domain Reload Profiling: 1984ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (576ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (940ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (718ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (467ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.5 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.6 MB.
Total: 7.478400 ms (FindLiveObjects: 0.538600 ms CreateObjectMapping: 0.323300 ms MarkObjects: 5.412800 ms  DeleteObjects: 1.202500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.084 seconds
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.894 seconds
Domain Reload Profiling: 1977ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (605ms)
		LoadAssemblies (520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (244ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (894ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (672ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (459ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (1.1 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.1 MB.
Total: 15.997200 ms (FindLiveObjects: 0.952900 ms CreateObjectMapping: 0.728600 ms MarkObjects: 11.553400 ms  DeleteObjects: 2.760500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.061 seconds
Refreshing native plugins compatible for Editor in 2.12 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.946 seconds
Domain Reload Profiling: 2007ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (488ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (235ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (709ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (164ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3723 unused Assets / (0.8 MB). Loaded Objects now: 4212.
Memory consumption went from 82.2 MB to 81.4 MB.
Total: 8.084800 ms (FindLiveObjects: 0.562500 ms CreateObjectMapping: 0.398800 ms MarkObjects: 5.830500 ms  DeleteObjects: 1.291900 ms)

Prepare: number of updated asset objects reloaded= 0
