using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;
using System.IO;
using System;
using UnityEngine.Rendering;

namespace TextureCompressorTool
{
    public enum TextureUsage
    {
        Unknown,
        Sprite,
        UI,
        Material,
        Terrain,
        Particle,
        Lightmap,
        NormalMap,
        Cursor
    }

    [System.Serializable]
    public class TextureInfo
    {
        public Texture2D texture;
        public string path;
        public string name;
        public long originalSize;
        public long compressedSize;
        public TextureImporterFormat originalFormat;
        public TextureImporterFormat currentFormat;
        public TextureImporterFormat recommendedFormat = TextureImporterFormat.Automatic;
        public int width;
        public int height;
        public bool hasAlpha;
        public bool processed = false;
        public float compressionRatio = 1f;
        public TextureUsage usage = TextureUsage.Unknown;
        public string usageDescription = "";
        public float qualityScore = 1f;
        public bool isOversized = false;
        public bool isUncompressed = false;
        public int mipmapCount;
        public bool hasMipmaps;
        public FilterMode filterMode;
        public TextureWrapMode wrapMode;
        public bool sRGBTexture;
        public bool alphaIsTransparency;
        public bool streamingMipmaps;
        public int streamingMipmapsPriority;
        
        // AI Analysis results
        public string contentType = "Unknown";
        public float complexityScore = 0f;
        public float noiseLevel = 0f;
        public float gradientLevel = 0f;
        public float edgeLevel = 0f;
        public string analysisNotes = "";
        
        // Additional properties from nested class
        public long estimatedSize;
        public bool isSprite;
        public bool isUI;
        public bool isTerrain;
        public bool isParticle;
        public bool isLightmap;
        public bool isReadable;
        public int maxSize;
        public string category;
        public List<GameObject> usedBy = new List<GameObject>();
        public DateTime lastModified;
        public string fileSize;
        public bool selected;
    }

    /// <summary>
    /// Advanced Editor window for the Texture Compressor Tool
    /// </summary>
    public class TextureCompressorWindow : EditorWindow
    {
        [MenuItem("Window/Texture Compressor Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<TextureCompressorWindow>("Advanced Texture Compressor");
            window.minSize = new Vector2(800, 600);
        }

        // UI State
        private Vector2 scrollPosition;
        private Vector2 settingsScrollPosition;
        private string searchFilter = "";
        private int selectedTab = 0;
        private readonly string[] tabNames = { "Scene Scan", "Project Scan", "Batch Operations", "Analytics", "AI Assistant", "Preview", "Performance", "Workflows", "Cloud", "Settings" };
        
        // Advanced Features
        private TexturePreviewSystem.ComparisonView comparisonView = new TexturePreviewSystem.ComparisonView();
        private TextureAnalyzer.OptimizationPriority aiOptimizationPriority = TextureAnalyzer.OptimizationPriority.Balanced;
        private OptimizationProfile selectedProfile;
        private List<OptimizationProfile> availableProfiles;

        private bool enableAIRecommendations = true;
        private bool enableRealTimePreview = true;
        private Dictionary<Texture2D, TextureAnalyzer.AnalysisResult> analysisCache = new Dictionary<Texture2D, TextureAnalyzer.AnalysisResult>();
        private Dictionary<Texture2D, TexturePreviewSystem.PreviewData> previewCache = new Dictionary<Texture2D, TexturePreviewSystem.PreviewData>();
        
        // Machine Learning
        private bool enableMachineLearning = true;

        private int maxMLRecommendations = 5;

        // Filters
        private bool showSpritesOnly = false;
        private bool showUIOnly = false;
        private bool showUncompressedOnly = false;
        private bool showOversizedOnly = false;
        private bool showAlphaTextures = false;

        private float minSizeFilter = 0;
        private float maxSizeFilter = 8192;

        // Scan settings
        private bool includeSprites = true;
        private bool includeUI = true;
        private bool includeMaterials = true;
        private bool includeTerrainTextures = true;
        private bool includeParticleTextures = true;
        private bool includeLightmapTextures = false;

        private bool scanSelectedFolders = false;
        private List<string> selectedFolders = new List<string>();

        // Advanced Compression settings
        private CompressionPreset selectedPreset = CompressionPreset.Balanced;
        private int maxTextureSize = 1024;
        private int compressionQuality = 50;
        private bool useCrunchCompression = true;
        private bool generateMipmaps = true;
        private FilterMode filterMode = FilterMode.Bilinear;
        private TextureWrapMode wrapMode = TextureWrapMode.Repeat;
        private bool sRGBTexture = true;
        private bool alphaIsTransparency = true;
        private bool streamingMipmaps = false;
        private int streamingMipmapsPriority = 0;

        // Platform-specific settings
        private BuildTarget selectedPlatform = BuildTarget.StandaloneWindows64;
        private Dictionary<BuildTarget, PlatformSettings> platformSettings = new Dictionary<BuildTarget, PlatformSettings>();

        // Results
        private List<TextureInfo> foundTextures = new List<TextureInfo>();
        private List<TextureInfo> filteredTextures = new List<TextureInfo>();
        private long totalOriginalSize = 0;
        private long totalCompressedSize = 0;
        private long totalSavedSize = 0;

        // Analytics
        private Dictionary<TextureImporterFormat, int> formatDistribution = new Dictionary<TextureImporterFormat, int>();
        private Dictionary<string, int> sizeDistribution = new Dictionary<string, int>();
        private List<TextureInfo> largestTextures = new List<TextureInfo>();
        private List<TextureInfo> mostCompressibleTextures = new List<TextureInfo>();

        // Batch operations
        private bool batchResize = false;
        private int batchMaxSize = 1024;
        private bool batchFormat = false;
        private TextureImporterFormat batchTargetFormat = TextureImporterFormat.DXT5;
        private bool batchQuality = false;
        private int batchQualityValue = 50;

        // Format selection
        private int selectedFormatIndex = 0;
        private string[] allTextureFormats = new string[]
        {
            // RGBA Compressed ASTC formats (exactly as shown in screenshot)
            "RGBA Compressed ASTC 12x12 block",
            "RGBA Compressed ASTC 10x10 block",
            "RGBA Compressed ASTC 8x8 block",
            "RGBA Compressed ASTC 6x6 block",
            "RGBA Compressed ASTC 5x5 block",
            "RGBA Compressed ASTC 4x4 block",

            // RGBA Compressed ETC2 formats
            "RGBA Compressed ETC2 8 bits",
            "RGB + 1-bit Alpha Compressed ETC2 4 bits",

            // RGBA Compressed PVRTC formats
            "RGBA Compressed PVRTC 4 bits",
            "RGBA Compressed PVRTC 2 bits",

            // RGBA Compressed DXT formats
            "RGBA Compressed DXT5|BC3",
            "RGBA Crunched ETC2",
            "RGBA Crunched DXT5|BC3",

            // RGBA bit formats
            "RGBA 64 bit",
            "RGBA 16 bit",
            "RGBA 32 bit",

            // RGB Compressed ETC2 formats
            "RGB Compressed ETC2 4 bits",
            "RGB Compressed ETC 4 bits",

            // RGB Compressed PVRTC formats
            "RGB Compressed PVRTC 4 bits",
            "RGB Compressed PVRTC 2 bits",

            // RGB Compressed DXT formats
            "RGB Compressed DXT1|BC1",
            "RGB Crunched ETC",
            "RGB Crunched DXT1|BC1",

            // RGB bit formats
            "RGB 48 bit",
            "RGB 16 bit",
            "RGB 24 bit",

            // RG Compressed EAC format
            "RG Compressed EAC 8 bit",
            "RG 32 bit",

            // R Compressed EAC format
            "R Compressed EAC 4 bit",
            "R 8",
            "R 16 bit",

            // Alpha format
            "Alpha 8",

            // HDR formats
            "RGBA Float",
            "RGBA Half",
            "RG Float",
            "R Float",

            // ASTC HDR formats (exactly as shown in screenshot)
            "RGBA Compressed ASTC HDR 12x12 block",
            "RGBA Compressed ASTC HDR 10x10 block",
            "RGBA Compressed ASTC HDR 8x8 block"
        };

        private TextureImporterFormat[] correspondingFormats = new TextureImporterFormat[]
        {
            // RGBA Compressed ASTC formats (matching screenshot order)
            TextureImporterFormat.ASTC_12x12,
            TextureImporterFormat.ASTC_10x10,
            TextureImporterFormat.ASTC_8x8,
            TextureImporterFormat.ASTC_6x6,
            TextureImporterFormat.ASTC_5x5,
            TextureImporterFormat.ASTC_4x4,

            // RGBA Compressed ETC2 formats
            TextureImporterFormat.ETC2_RGBA8,
            TextureImporterFormat.ETC2_RGB4_PUNCHTHROUGH_ALPHA,

            // RGBA Compressed PVRTC formats
            TextureImporterFormat.PVRTC_RGBA4,
            TextureImporterFormat.PVRTC_RGBA2,

            // RGBA Compressed DXT formats
            TextureImporterFormat.DXT5,
            TextureImporterFormat.ETC2_RGBA8Crunched,
            TextureImporterFormat.DXT5Crunched,

            // RGBA bit formats
            TextureImporterFormat.RGBA64,
            TextureImporterFormat.RGBA16,
            TextureImporterFormat.RGBA32,

            // RGB Compressed ETC2 formats
            TextureImporterFormat.ETC2_RGB4,
            TextureImporterFormat.ETC_RGB4,

            // RGB Compressed PVRTC formats
            TextureImporterFormat.PVRTC_RGB4,
            TextureImporterFormat.PVRTC_RGB2,

            // RGB Compressed DXT formats
            TextureImporterFormat.DXT1,
            TextureImporterFormat.ETC_RGB4Crunched,
            TextureImporterFormat.DXT1Crunched,

            // RGB bit formats
            TextureImporterFormat.RGB48,
            TextureImporterFormat.RGB16,
            TextureImporterFormat.RGB24,

            // RG Compressed EAC format
            TextureImporterFormat.EAC_RG,
            TextureImporterFormat.RG32,

            // R Compressed EAC format
            TextureImporterFormat.EAC_R,
            TextureImporterFormat.R8,
            TextureImporterFormat.R16,

            // Alpha format
            TextureImporterFormat.Alpha8,

            // HDR formats
            TextureImporterFormat.RGBAFloat,
            TextureImporterFormat.RGBAHalf,
            TextureImporterFormat.RGFloat,
            TextureImporterFormat.RFloat,

            // ASTC HDR formats (matching screenshot order)
            TextureImporterFormat.ASTC_HDR_12x12,
            TextureImporterFormat.ASTC_HDR_10x10,
            TextureImporterFormat.ASTC_HDR_8x8
        };

        // Progress tracking
        private bool isProcessing = false;
        private float processingProgress = 0f;
        private string processingStatus = "";

        public enum CompressionPreset
        {
            Uncompressed,
            HighQuality,
            Balanced,
            SmallSize,
            Mobile,
            Custom
        }



        [System.Serializable]
        public class PlatformSettings
        {
            public int maxTextureSize = 2048;
            public TextureImporterFormat format = TextureImporterFormat.Automatic;
            public int compressionQuality = 50;
            public bool crunchedCompression = false;
            public bool overridden = false;
        }

        private void OnGUI()
        {
            InitializePlatformSettings();
            InitializeAdvancedFeatures();
            InitializeModernStyles();

            // Modern dark theme background
            var originalColor = GUI.backgroundColor;
            GUI.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);

            EditorGUILayout.BeginVertical(modernBackgroundStyle);
            GUI.backgroundColor = originalColor;

            // Enhanced header with gradient and modern styling
            DrawModernHeader();

            // Enhanced progress bar if processing
            if (isProcessing)
            {
                DrawModernProgressBar();
            }

            // Modern tab system with custom styling
            DrawModernTabSystem();

            // Content area with modern styling
            EditorGUILayout.BeginVertical(modernContentStyle);

            switch (selectedTab)
            {
                case 0: DrawSceneScanTab(); break;
                case 1: DrawProjectScanTab(); break;
                case 2: DrawBatchOperationsTab(); break;
                case 3: DrawAnalyticsTab(); break;
                case 4: DrawAIAssistantTab(); break;
                case 5: DrawPreviewTab(); break;
                case 6: DrawPerformanceTab(); break;
                case 7: DrawWorkflowsTab(); break;
                case 8: DrawCloudTab(); break;
                case 9: DrawSettingsTab(); break;
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            // Draw modern notification overlay
            DrawModernNotification();
        }

        private void InitializeAdvancedFeatures()
        {
            if (availableProfiles == null)
            {
                availableProfiles = TextureOptimizationProfiles.GetProfiles();
                if (selectedProfile == null && availableProfiles.Count > 0)
                {
                    selectedProfile = availableProfiles[0];
                }
            }
        }

        private void DrawHeader()
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            
            var titleStyle = new GUIStyle(EditorStyles.largeLabel)
            {
                fontSize = 18,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter
            };
            
            EditorGUILayout.LabelField("🎨 Advanced Texture Compressor Tool", titleStyle);
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Quick stats bar
            if (foundTextures.Count > 0)
            {
                EditorGUILayout.BeginHorizontal("box");
                EditorGUILayout.LabelField($"📊 {foundTextures.Count} textures", GUILayout.Width(120));
                EditorGUILayout.LabelField($"💾 {FormatBytes(totalOriginalSize)}", GUILayout.Width(100));
                if (totalCompressedSize > 0)
                {
                    var savings = totalOriginalSize > 0 ? ((float)(totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100f : 0f;
                    EditorGUILayout.LabelField($"💰 {savings:F1}% saved", GUILayout.Width(100));
                }
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.Space();
        }

        private void DrawProgressBar()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField(processingStatus);
            var rect = EditorGUILayout.GetControlRect();
            EditorGUI.ProgressBar(rect, processingProgress, $"{processingProgress * 100:F0}%");
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        private void DrawSceneScanTab()
        {
            EditorGUILayout.BeginHorizontal();
            
            // Left panel - Scan settings
            EditorGUILayout.BeginVertical("box", GUILayout.Width(300));
            EditorGUILayout.LabelField("🔍 Scan Settings", EditorStyles.boldLabel);
            
            includeSprites = EditorGUILayout.Toggle("🖼️ Sprites", includeSprites);
            includeUI = EditorGUILayout.Toggle("🎮 UI Elements", includeUI);
            includeMaterials = EditorGUILayout.Toggle("🎨 Materials", includeMaterials);
            includeTerrainTextures = EditorGUILayout.Toggle("🏔️ Terrain", includeTerrainTextures);
            includeParticleTextures = EditorGUILayout.Toggle("✨ Particles", includeParticleTextures);
            includeLightmapTextures = EditorGUILayout.Toggle("💡 Lightmaps", includeLightmapTextures);
            
            EditorGUILayout.Space();
            
            // Enhanced scan buttons with modern styling
            EditorGUILayout.BeginHorizontal();

            var scanSceneStyle = CreateActionButtonStyle(new Color(0.2f, 0.7f, 0.9f), new Color(0.3f, 0.8f, 1f));
            scanSceneStyle.fontSize = 12;
            scanSceneStyle.fontStyle = FontStyle.Bold;

            if (GUILayout.Button("🔍 Scan Scene", scanSceneStyle, GUILayout.Height(45), GUILayout.MinWidth(150)))
            {
                ScanSceneWithPerformanceMonitoring();
            }

            var scanProjectStyle = CreateActionButtonStyle(new Color(0.9f, 0.6f, 0.2f), new Color(1f, 0.7f, 0.3f));
            scanProjectStyle.fontSize = 12;
            scanProjectStyle.fontStyle = FontStyle.Bold;

            if (GUILayout.Button("🗂️ Scan Project", scanProjectStyle, GUILayout.Height(45), GUILayout.MinWidth(150)))
            {
                ScanProjectWithPerformanceMonitoring();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Enhanced filters with modern styling
            EditorGUILayout.LabelField("🔧 Filters", modernTitleStyle);

            var searchStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 11,
                normal = { textColor = new Color(0.9f, 0.9f, 0.9f) },
                focused = { textColor = Color.white }
            };
            searchFilter = EditorGUILayout.TextField("🔍 Search", searchFilter, searchStyle);

            EditorGUILayout.BeginHorizontal();
            var toggleStyle = CreateModernToggleStyle(new Color(0.3f, 0.8f, 1f));
            showSpritesOnly = GUILayout.Toggle(showSpritesOnly, "🖼️ Sprites", toggleStyle);
            showUIOnly = GUILayout.Toggle(showUIOnly, "🎮 UI", toggleStyle);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            showUncompressedOnly = GUILayout.Toggle(showUncompressedOnly, "📦 Uncompressed", toggleStyle);
            showOversizedOnly = GUILayout.Toggle(showOversizedOnly, "⚠️ Oversized", toggleStyle);
            EditorGUILayout.EndHorizontal();

            showAlphaTextures = EditorGUILayout.Toggle("🔍 Has Alpha", showAlphaTextures, toggleStyle);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Size Range");
            EditorGUILayout.MinMaxSlider(ref minSizeFilter, ref maxSizeFilter, 32, 8192);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"{(int)minSizeFilter}px", GUILayout.Width(50));
            GUILayout.FlexibleSpace();
            EditorGUILayout.LabelField($"{(int)maxSizeFilter}px", GUILayout.Width(50));
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            
            // Right panel - Texture list
            EditorGUILayout.BeginVertical();
            DrawTextureList();
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        private void DrawProjectScanTab()
        {
            EditorGUILayout.LabelField("🗂️ Project Asset Scanner", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            // Folder selection
            EditorGUILayout.BeginVertical("box", GUILayout.Width(300));
            EditorGUILayout.LabelField("📁 Scan Folders", EditorStyles.boldLabel);
            
            scanSelectedFolders = EditorGUILayout.Toggle("Scan Selected Folders Only", scanSelectedFolders);
            
            if (scanSelectedFolders)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Folder"))
                {
                    string folder = EditorUtility.OpenFolderPanel("Select Folder", "Assets", "");
                    if (!string.IsNullOrEmpty(folder) && folder.Contains(Application.dataPath))
                    {
                        folder = "Assets" + folder.Substring(Application.dataPath.Length);
                        if (!selectedFolders.Contains(folder))
                            selectedFolders.Add(folder);
                    }
                }
                if (GUILayout.Button("Clear All"))
                {
                    selectedFolders.Clear();
                }
                EditorGUILayout.EndHorizontal();
                
                for (int i = 0; i < selectedFolders.Count; i++)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(selectedFolders[i]);
                    if (GUILayout.Button("❌", GUILayout.Width(25)))
                    {
                        selectedFolders.RemoveAt(i);
                        i--;
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("🔍 Scan Project Assets", GUILayout.Height(50), GUILayout.MinWidth(200)))
            {
                ScanProjectWithPerformanceMonitoring();
            }
            
            EditorGUILayout.EndVertical();
            
            // Results
            EditorGUILayout.BeginVertical();
            DrawTextureList();
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        private void DrawBatchOperationsTab()
        {
            try
            {
                EditorGUILayout.LabelField("⚡ Batch Operations", EditorStyles.boldLabel);
                EditorGUILayout.Space();

                EditorGUILayout.BeginHorizontal();
            
            // Enhanced batch settings with modern styling
            EditorGUILayout.BeginVertical(modernCardStyle, GUILayout.Width(380));
            EditorGUILayout.LabelField("🔧 Batch Settings", modernTitleStyle);
            
            // Compression presets
            EditorGUILayout.LabelField("Compression Preset");
            selectedPreset = (CompressionPreset)EditorGUILayout.EnumPopup(selectedPreset);
            ApplyCompressionPreset();
            
            EditorGUILayout.Space();
            
            // Platform selection
            EditorGUILayout.LabelField("🎯 Target Platform");
            selectedPlatform = (BuildTarget)EditorGUILayout.EnumPopup(selectedPlatform);
            
            EditorGUILayout.Space();
            
            // Batch operations (also applies to individual compress buttons)
            EditorGUILayout.LabelField("🔧 Compression Settings", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("✨ AUTO-APPLY: Batch operations automatically use your current UI settings!\n" +
                                   "• Override checkboxes = Force specific values\n" +
                                   "• No override = Use general settings from other tabs\n" +
                                   "• All settings apply to individual compress buttons too!", MessageType.Info);

            batchResize = EditorGUILayout.Toggle("📏 Override Max Size", batchResize);
            if (batchResize)
            {
                batchMaxSize = EditorGUILayout.IntPopup("Max Size", batchMaxSize,
                    new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096" },
                    new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096 });
            }

            batchFormat = EditorGUILayout.Toggle("🎨 Override Format", batchFormat);
            if (batchFormat)
            {
                EditorGUILayout.LabelField("Select Texture Format:");
                selectedFormatIndex = EditorGUILayout.Popup("Format", selectedFormatIndex, allTextureFormats);
                if (selectedFormatIndex >= 0 && selectedFormatIndex < correspondingFormats.Length)
                {
                    batchTargetFormat = correspondingFormats[selectedFormatIndex];
                }

                // Show format info panel
                EditorGUILayout.BeginVertical("box");
                EditorGUILayout.LabelField("📋 Format Information", EditorStyles.boldLabel);

                EditorGUILayout.LabelField($"Selected: {batchTargetFormat}", EditorStyles.miniLabel);

                // Show format properties
                bool supportsAlpha = FormatSupportsAlpha(batchTargetFormat);
                EditorGUILayout.LabelField($"Alpha Support: {(supportsAlpha ? "✅ Yes" : "❌ No")}", EditorStyles.miniLabel);

                string compressionType = GetCompressionType(batchTargetFormat);
                EditorGUILayout.LabelField($"Compression: {compressionType}", EditorStyles.miniLabel);

                string platformSupport = GetPlatformSupport(batchTargetFormat);
                EditorGUILayout.LabelField($"Platform Support: {platformSupport}", EditorStyles.miniLabel);

                string qualityInfo = GetQualityInfo(batchTargetFormat);
                EditorGUILayout.LabelField($"Quality: {qualityInfo}", EditorStyles.miniLabel);

                EditorGUILayout.Space();

                // Smart recommendation button
                if (GUILayout.Button("🧠 Smart Format Recommendation", GUILayout.Height(35), GUILayout.MinWidth(250)))
                {
                    try
                    {
                        var selectedTextures = GetSelectedTextures();
                        if (selectedTextures.Count > 0)
                        {
                            var recommendedFormat = GetSmartRecommendation(selectedTextures);

                            // Find the index of the recommended format
                            bool formatFound = false;
                            for (int i = 0; i < correspondingFormats.Length; i++)
                            {
                                if (correspondingFormats[i] == recommendedFormat)
                                {
                                    selectedFormatIndex = i;
                                    batchTargetFormat = recommendedFormat;
                                    formatFound = true;
                                    break;
                                }
                            }

                            if (formatFound)
                            {
                                Debug.Log($"Smart recommendation: {recommendedFormat} for {selectedTextures.Count} textures");
                                Repaint(); // Force GUI repaint
                            }
                            else
                            {
                                Debug.LogWarning($"Recommended format {recommendedFormat} not found in format list");
                            }
                        }
                        else
                        {
                            EditorUtility.DisplayDialog("No Selection", "Please select some textures first to get a smart recommendation.", "OK");
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Error in Smart Format Recommendation: {e.Message}");
                    }
                }

                EditorGUILayout.EndVertical();
            }
            
            batchQuality = EditorGUILayout.Toggle("⭐ Override Quality", batchQuality);
            if (batchQuality)
            {
                batchQualityValue = EditorGUILayout.IntSlider("Quality", batchQualityValue, 0, 100);
                EditorGUILayout.LabelField($"Quality: {batchQualityValue}% (0=Max Compression, 100=Best Quality)", EditorStyles.miniLabel);
            }
            
            EditorGUILayout.Space();
            
            // Action buttons
            EditorGUI.BeginDisabledGroup(GetSelectedTextures().Count == 0);

            var selectedButtonStyle = CreateActionButtonStyle(new Color(0.8f, 0.4f, 0.2f), new Color(1f, 0.5f, 0.3f));
            selectedButtonStyle.fontSize = 13;
            selectedButtonStyle.fontStyle = FontStyle.Bold;

            if (GUILayout.Button("⚡ Apply to Selected", selectedButtonStyle, GUILayout.Height(45), GUILayout.MinWidth(180)))
            {
                try
                {
                    ApplyBatchOperations(GetSelectedTextures());
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Error applying batch operations to selected textures: {e.Message}");
                    EditorUtility.DisplayDialog("Error", $"Failed to apply batch operations: {e.Message}", "OK");
                }
            }

            var allButtonStyle = CreateActionButtonStyle(new Color(0.6f, 0.2f, 0.8f), new Color(0.7f, 0.3f, 0.9f));
            allButtonStyle.fontSize = 13;
            allButtonStyle.fontStyle = FontStyle.Bold;

            if (GUILayout.Button("🔥 Apply to All Filtered", allButtonStyle, GUILayout.Height(45), GUILayout.MinWidth(180)))
            {
                try
                {
                    CompressAllTexturesWithPerformanceMonitoring(GetFilteredTextures());
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Error compressing all filtered textures: {e.Message}");
                    EditorUtility.DisplayDialog("Error", $"Failed to compress textures: {e.Message}", "OK");
                }
            }

            EditorGUI.EndDisabledGroup();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("💾 Create Backup", GUILayout.Height(35), GUILayout.MinWidth(140)))
            {
                CreateBackup();
            }

            if (GUILayout.Button("🔄 Restore Backup", GUILayout.Height(35), GUILayout.MinWidth(140)))
            {
                RestoreBackup();
            }

            EditorGUILayout.EndVertical(); // End the batch settings vertical box

            // Texture list with selection
            EditorGUILayout.BeginVertical();
            DrawSelectableTextureList();
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal(); // End the main horizontal layout
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error in DrawBatchOperationsTab: {e.Message}");
                // Ensure GUI layout is properly closed even on error
                GUIUtility.ExitGUI();
            }
        }

        private void DrawAnalyticsTab()
        {
            EditorGUILayout.LabelField("📊 Texture Analytics", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            if (foundTextures.Count == 0)
            {
                EditorGUILayout.HelpBox("No textures scanned yet. Use the Scene Scan or Project Scan tabs first.", MessageType.Info);
                return;
            }
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            // Overall statistics
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📈 Overall Statistics", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Total Textures: {foundTextures.Count}");
            EditorGUILayout.LabelField($"Total Size: {FormatBytes(totalOriginalSize)}");
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Compressed: {foundTextures.Count(t => t.processed)}");
            EditorGUILayout.LabelField($"Potential Savings: {FormatBytes(CalculatePotentialSavings())}");
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
            
            // Format distribution
            DrawFormatDistribution();
            EditorGUILayout.Space();
            
            // Size distribution
            DrawSizeDistribution();
            EditorGUILayout.Space();
            
            // Top largest textures
            DrawLargestTextures();
            EditorGUILayout.Space();
            
            // Most compressible textures
            DrawMostCompressibleTextures();
            
            EditorGUILayout.EndScrollView();
        }

        private void ApplyCurrentSettings()
        {
            // Get all textures that are currently visible/filtered
            var texturesToApply = GetFilteredTextures();

            if (texturesToApply.Count == 0)
            {
                EditorUtility.DisplayDialog("No Textures",
                    "No textures found to apply settings to.\n\nTip: Go to Scene Scan or Project Scan tab first to find textures.",
                    "OK");
                return;
            }

            // Confirm with user
            bool confirmed = EditorUtility.DisplayDialog("Apply Current Settings",
                $"Apply current settings to {texturesToApply.Count} texture(s)?\n\n" +
                $"Settings to apply:\n" +
                $"• Max Size: {maxTextureSize}\n" +
                $"• Quality: {compressionQuality}\n" +
                $"• Platform: {selectedPlatform}\n" +
                $"• Crunch Compression: {(useCrunchCompression ? "Enabled" : "Disabled")}\n" +
                $"• Generate Mipmaps: {(generateMipmaps ? "Enabled" : "Disabled")}",
                "Apply", "Cancel");

            if (!confirmed) return;

            // Apply settings using the batch operation logic
            StartProcessing("Applying Current Settings...");

            int successCount = 0;
            for (int i = 0; i < texturesToApply.Count; i++)
            {
                var textureInfo = texturesToApply[i];
                UpdateProgress((float)i / texturesToApply.Count, $"Applying settings to {textureInfo.texture.name}...");

                try
                {
                    string path = textureInfo.path;
                    TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

                    if (importer == null) continue;

                    // Apply current UI settings to the texture
                    string currentPlatform = GetCurrentPlatformName();
                    var platformSettings = importer.GetPlatformTextureSettings(currentPlatform);
                    platformSettings.overridden = true;

                    // Apply all current settings
                    importer.maxTextureSize = maxTextureSize;
                    platformSettings.maxTextureSize = maxTextureSize;
                    platformSettings.compressionQuality = compressionQuality;
                    platformSettings.crunchedCompression = useCrunchCompression;

                    // Apply optimal format for the platform
                    platformSettings.format = GetOptimalFormatForTexture(
                        textureInfo.hasAlpha,
                        textureInfo.width,
                        textureInfo.height,
                        importer.textureType,
                        textureInfo.isSprite,
                        textureInfo.isUI);

                    // Apply other settings
                    importer.mipmapEnabled = generateMipmaps;
                    importer.filterMode = filterMode;
                    importer.wrapMode = wrapMode;
                    importer.sRGBTexture = sRGBTexture;
                    importer.alphaIsTransparency = alphaIsTransparency;
                    importer.streamingMipmaps = streamingMipmaps;
                    if (streamingMipmaps)
                    {
                        importer.streamingMipmapsPriority = streamingMipmapsPriority;
                    }

                    // Apply platform settings
                    importer.SetPlatformTextureSettings(platformSettings);

                    // Save and reimport
                    importer.SaveAndReimport();

                    successCount++;

                    Debug.Log($"✅ Applied settings to {textureInfo.texture.name}: " +
                             $"MaxSize={maxTextureSize}, Quality={compressionQuality}, " +
                             $"Format={platformSettings.format}, Platform={currentPlatform}");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"❌ Failed to apply settings to {textureInfo.texture.name}: {e.Message}");
                }
            }

            FinishProcessing();
            AssetDatabase.Refresh();
            Repaint();

            // Show result
            string resultMessage = $"✅ Successfully applied settings to {successCount}/{texturesToApply.Count} textures!\n\n" +
                                  $"Settings applied:\n" +
                                  $"• Max Size: {maxTextureSize}\n" +
                                  $"• Quality: {compressionQuality}\n" +
                                  $"• Platform: {selectedPlatform}\n" +
                                  $"• Crunch: {(useCrunchCompression ? "Enabled" : "Disabled")}\n\n" +
                                  $"Check the Console for detailed logs.";

            EditorUtility.DisplayDialog("Settings Applied", resultMessage, "OK");
            ShowModernNotification($"🎉 Settings Applied!\n✅ {successCount}/{texturesToApply.Count} textures processed", Color.cyan, 4f);

            Debug.Log($"🎉 Apply Settings completed: {successCount}/{texturesToApply.Count} textures processed successfully.");
        }

        private void DrawSettingsTab()
        {
            EditorGUILayout.LabelField("⚙️ Advanced Settings", modernTitleStyle);
            EditorGUILayout.Space();

            settingsScrollPosition = EditorGUILayout.BeginScrollView(settingsScrollPosition);

            // Enhanced compression settings with modern styling
            EditorGUILayout.BeginVertical(modernCardStyle);
            EditorGUILayout.LabelField("🎨 Compression Settings", modernTitleStyle);
            
            maxTextureSize = EditorGUILayout.IntPopup("Max Texture Size", maxTextureSize,
                new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096", "8192" },
                new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 });
            
            compressionQuality = EditorGUILayout.IntSlider("Compression Quality", compressionQuality, 0, 100);
            useCrunchCompression = EditorGUILayout.Toggle("Use Crunch Compression", useCrunchCompression);
            generateMipmaps = EditorGUILayout.Toggle("Generate Mipmaps", generateMipmaps);
            
            filterMode = (FilterMode)EditorGUILayout.EnumPopup("Filter Mode", filterMode);
            wrapMode = (TextureWrapMode)EditorGUILayout.EnumPopup("Wrap Mode", wrapMode);
            
            sRGBTexture = EditorGUILayout.Toggle("sRGB Texture", sRGBTexture);
            alphaIsTransparency = EditorGUILayout.Toggle("Alpha Is Transparency", alphaIsTransparency);
            
            streamingMipmaps = EditorGUILayout.Toggle("Streaming Mipmaps", streamingMipmaps);
            if (streamingMipmaps)
            {
                streamingMipmapsPriority = EditorGUILayout.IntSlider("Priority", streamingMipmapsPriority, -128, 127);
            }
            
            EditorGUILayout.Space();

            // Enhanced Apply Settings Button with modern styling
            EditorGUILayout.Space(10);
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();

            var applyButtonStyle = CreateActionButtonStyle(new Color(0.2f, 0.8f, 0.3f), new Color(0.3f, 0.9f, 0.4f));
            applyButtonStyle.fontSize = 14;
            applyButtonStyle.fontStyle = FontStyle.Bold;

            if (GUILayout.Button("⚡ Apply Settings", applyButtonStyle, GUILayout.Height(50), GUILayout.MinWidth(250)))
            {
                ApplyCurrentSettings();
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Platform-specific settings
            DrawPlatformSettings();
            
            EditorGUILayout.EndScrollView();
        }

        private void InitializePlatformSettings()
        {
            if (platformSettings.Count == 0)
            {
                var platforms = new BuildTarget[]
                {
                    BuildTarget.StandaloneWindows64,
                    BuildTarget.Android,
                    BuildTarget.iOS,
                    BuildTarget.WebGL,
                    BuildTarget.StandaloneOSX
                };
                
                foreach (var platform in platforms)
                {
                    platformSettings[platform] = new PlatformSettings();
                }
            }
        }

        private void ApplyCompressionPreset()
        {
            switch (selectedPreset)
            {
                case CompressionPreset.Uncompressed:
                    maxTextureSize = 4096;
                    compressionQuality = 100;
                    useCrunchCompression = false;
                    break;
                case CompressionPreset.HighQuality:
                    maxTextureSize = 2048;
                    compressionQuality = 80;
                    useCrunchCompression = false;
                    break;
                case CompressionPreset.Balanced:
                    maxTextureSize = 1024;
                    compressionQuality = 50;
                    useCrunchCompression = true;
                    break;
                case CompressionPreset.SmallSize:
                    maxTextureSize = 512;
                    compressionQuality = 25;
                    useCrunchCompression = true;
                    break;
                case CompressionPreset.Mobile:
                    maxTextureSize = 512;
                    compressionQuality = 40;
                    useCrunchCompression = true;
                    break;
            }
        }

        private List<TextureInfo> GetFilteredTextures()
        {
            if (filteredTextures == null || filteredTextures.Count != foundTextures.Count)
            {
                filteredTextures = foundTextures.ToList();
            }

            var filtered = filteredTextures.AsEnumerable();

            if (!string.IsNullOrEmpty(searchFilter))
            {
                filtered = filtered.Where(t => t.texture.name.ToLower().Contains(searchFilter.ToLower()) ||
                                             t.path.ToLower().Contains(searchFilter.ToLower()));
            }

            if (showSpritesOnly)
            {
                filtered = filtered.Where(t => t.isSprite);
            }

            if (showUIOnly)
            {
                filtered = filtered.Where(t => t.isUI);
            }

            if (showUncompressedOnly)
            {
                filtered = filtered.Where(t => !t.processed);
            }

            if (showOversizedOnly)
            {
                filtered = filtered.Where(t => t.width > maxTextureSize || t.height > maxTextureSize);
            }

            if (showAlphaTextures)
            {
                filtered = filtered.Where(t => t.hasAlpha);
            }

            filtered = filtered.Where(t => t.width >= (int)minSizeFilter && t.width <= (int)maxSizeFilter);

            return filtered.ToList();
        }

        private List<TextureInfo> GetSelectedTextures()
        {
            return foundTextures.Where(t => t.selected).ToList();
        }

        private void DrawTextureList()
        {
            if (foundTextures.Count == 0)
            {
                EditorGUILayout.HelpBox("No textures found. Use the scan buttons to find textures.", MessageType.Info);
                return;
            }

            var filtered = GetFilteredTextures();
            
            EditorGUILayout.LabelField($"📋 Found Textures ({filtered.Count}/{foundTextures.Count})", EditorStyles.boldLabel);
            
            // Action buttons
            EditorGUILayout.BeginHorizontal();
            EditorGUI.BeginDisabledGroup(filtered.Count == 0);
            if (GUILayout.Button("🔥 Compress All Filtered", GUILayout.Height(35), GUILayout.MinWidth(180)))
            {
                CompressAllTextures(filtered);
            }
            if (GUILayout.Button("📊 Analyze All", GUILayout.Height(35), GUILayout.MinWidth(120)))
            {
                AnalyzeTextures(filtered);
            }
            EditorGUI.EndDisabledGroup();
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var textureInfo in filtered)
            {
                DrawAdvancedTextureInfo(textureInfo);
            }
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawSelectableTextureList()
        {
            if (foundTextures.Count == 0)
            {
                EditorGUILayout.HelpBox("No textures found. Use the scan buttons to find textures.", MessageType.Info);
                return;
            }

            var filtered = GetFilteredTextures();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"📋 Textures ({filtered.Count}/{foundTextures.Count})", EditorStyles.boldLabel);
            
            if (GUILayout.Button("Select All", GUILayout.Width(80)))
            {
                foreach (var texture in filtered)
                    texture.selected = true;
            }
            if (GUILayout.Button("Select None", GUILayout.Width(80)))
            {
                foreach (var texture in foundTextures)
                    texture.selected = false;
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var textureInfo in filtered)
            {
                DrawSelectableTextureInfo(textureInfo);
            }
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawAdvancedTextureInfo(TextureInfo textureInfo)
        {
            EditorGUILayout.BeginVertical(modernCardStyle);

            EditorGUILayout.BeginHorizontal();

            // Enhanced texture preview with modern styling
            if (textureInfo.texture != null)
            {
                var preview = AssetPreview.GetAssetPreview(textureInfo.texture);
                if (preview != null)
                {
                    // Preview with rounded corners effect
                    var previewRect = GUILayoutUtility.GetRect(72, 72);
                    var borderRect = new Rect(previewRect.x - 2, previewRect.y - 2, previewRect.width + 4, previewRect.height + 4);

                    // Border with gradient
                    var borderGradient = CreateGradientTexture(new Color(0.3f, 0.3f, 0.3f), new Color(0.1f, 0.1f, 0.1f));
                    GUI.DrawTexture(borderRect, borderGradient);

                    GUI.DrawTexture(previewRect, preview);
                }
                else
                {
                    var noPreviewStyle = new GUIStyle(GUI.skin.box)
                    {
                        alignment = TextAnchor.MiddleCenter,
                        normal = {
                            background = CreateSolidTexture(new Color(0.15f, 0.15f, 0.15f)),
                            textColor = new Color(0.6f, 0.6f, 0.6f)
                        },
                        fontSize = 10
                    };
                    GUILayout.Box("No Preview", noPreviewStyle, GUILayout.Width(72), GUILayout.Height(72));
                }
            }

            EditorGUILayout.BeginVertical();

            // Enhanced texture name and path with modern styling
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(textureInfo.texture.name, modernTitleStyle);

            GUILayout.FlexibleSpace();

            // Enhanced status indicators with colors
            if (textureInfo.processed)
            {
                var processedStyle = new GUIStyle(modernIconStyle) { normal = { textColor = Color.green } };
                GUILayout.Label("✅", processedStyle, GUILayout.Width(20));
            }
            if (textureInfo.hasAlpha)
            {
                var alphaStyle = new GUIStyle(modernIconStyle) { normal = { textColor = Color.cyan } };
                GUILayout.Label("🔍", alphaStyle, GUILayout.Width(20));
            }
            if (textureInfo.width > maxTextureSize || textureInfo.height > maxTextureSize)
            {
                var warningStyle = new GUIStyle(modernIconStyle) { normal = { textColor = Color.yellow } };
                GUILayout.Label("⚠️", warningStyle, GUILayout.Width(20));
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.LabelField(textureInfo.path, modernSubtitleStyle);

            // Enhanced detailed info with modern layout
            EditorGUILayout.BeginHorizontal(modernBoxStyle);

            var infoStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                normal = { textColor = new Color(0.8f, 0.9f, 1f) },
                fontSize = 11,
                fontStyle = FontStyle.Bold
            };

            EditorGUILayout.LabelField($"📐 {textureInfo.width}x{textureInfo.height}", infoStyle, GUILayout.Width(110));
            EditorGUILayout.LabelField($"💾 {FormatBytes(textureInfo.originalSize)}", infoStyle, GUILayout.Width(90));
            EditorGUILayout.LabelField($"🎨 {textureInfo.currentFormat}", infoStyle, GUILayout.Width(130));
            EditorGUILayout.EndHorizontal();

            // Category and usage info
            EditorGUILayout.BeginHorizontal();
            string categoryInfo = textureInfo.category ?? "Unknown";
            if (textureInfo.isSprite) categoryInfo += " | Sprite";
            if (textureInfo.isUI) categoryInfo += " | UI";
            if (textureInfo.isTerrain) categoryInfo += " | Terrain";
            if (textureInfo.isParticle) categoryInfo += " | Particle";
            
            EditorGUILayout.LabelField($"📂 {categoryInfo}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();

            if (textureInfo.processed && textureInfo.compressedSize > 0)
            {
                var savings = textureInfo.originalSize > 0 ? 
                    ((float)(textureInfo.originalSize - textureInfo.compressedSize) / textureInfo.originalSize) * 100f : 0f;
                EditorGUILayout.LabelField($"💰 Saved: {FormatBytes(textureInfo.originalSize - textureInfo.compressedSize)} ({savings:F1}%)", 
                    EditorStyles.miniLabel);
            }

            EditorGUILayout.EndVertical();

            // Enhanced action buttons with modern styling
            EditorGUILayout.BeginVertical(GUILayout.Width(120));

            // Compress button with dynamic styling
            EditorGUI.BeginDisabledGroup(textureInfo.processed);
            var compressButtonStyle = textureInfo.processed ?
                CreateDisabledButtonStyle() :
                CreateActionButtonStyle(new Color(0.8f, 0.3f, 0.2f), new Color(1f, 0.4f, 0.3f));

            if (GUILayout.Button("🔥 Compress", compressButtonStyle, GUILayout.Height(35), GUILayout.MinWidth(100)))
            {
                CompressTexture(textureInfo);
            }
            EditorGUI.EndDisabledGroup();

            // Analyze button
            var analyzeButtonStyle = CreateActionButtonStyle(new Color(0.2f, 0.6f, 0.8f), new Color(0.3f, 0.7f, 0.9f));
            if (GUILayout.Button("📊 Analyze", analyzeButtonStyle, GUILayout.Height(35), GUILayout.MinWidth(100)))
            {
                AnalyzeTexture(textureInfo);
            }

            // Select button
            var selectButtonStyle = CreateActionButtonStyle(new Color(0.3f, 0.7f, 0.3f), new Color(0.4f, 0.8f, 0.4f));
            if (GUILayout.Button("🔍 Select", selectButtonStyle, GUILayout.Height(35), GUILayout.MinWidth(100)))
            {
                Selection.activeObject = textureInfo.texture;
                EditorGUIUtility.PingObject(textureInfo.texture);
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        private void DrawSelectableTextureInfo(TextureInfo textureInfo)
        {
            EditorGUILayout.BeginHorizontal("box");
            
            // Selection checkbox
            textureInfo.selected = EditorGUILayout.Toggle(textureInfo.selected, GUILayout.Width(20));

            // Texture preview (smaller)
            if (textureInfo.texture != null)
            {
                var preview = AssetPreview.GetAssetPreview(textureInfo.texture);
                if (preview != null)
                {
                    GUILayout.Label(preview, GUILayout.Width(32), GUILayout.Height(32));
                }
            }

            EditorGUILayout.BeginVertical();
            
            // Name and basic info
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(textureInfo.texture.name, EditorStyles.boldLabel, GUILayout.Width(150));
            EditorGUILayout.LabelField($"{textureInfo.width}x{textureInfo.height}", GUILayout.Width(80));
            EditorGUILayout.LabelField(FormatBytes(textureInfo.originalSize), GUILayout.Width(80));
            
            // Status
            if (textureInfo.processed)
                GUILayout.Label("✅", GUILayout.Width(20));
            else
                GUILayout.Label("⏳", GUILayout.Width(20));
                
            EditorGUILayout.EndHorizontal();
            
            // Path
            EditorGUILayout.LabelField(textureInfo.path, EditorStyles.miniLabel);
            
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }

        private void DrawFormatDistribution()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🎨 Format Distribution", EditorStyles.boldLabel);
            
            var formatCounts = foundTextures.GroupBy(t => t.currentFormat)
                .OrderByDescending(g => g.Count())
                .Take(10);
            
            foreach (var group in formatCounts)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(group.Key.ToString(), GUILayout.Width(150));
                EditorGUILayout.LabelField($"{group.Count()} textures", GUILayout.Width(100));
                
                var totalSize = group.Sum(t => t.originalSize);
                EditorGUILayout.LabelField(FormatBytes(totalSize));
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawSizeDistribution()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📏 Size Distribution", EditorStyles.boldLabel);
            
            var sizeRanges = new Dictionary<string, int>
            {
                {"32x32 and smaller", 0},
                {"64x64", 0},
                {"128x128", 0},
                {"256x256", 0},
                {"512x512", 0},
                {"1024x1024", 0},
                {"2048x2048", 0},
                {"4096x4096 and larger", 0}
            };
            
            foreach (var texture in foundTextures)
            {
                var maxDim = Mathf.Max(texture.width, texture.height);
                if (maxDim <= 32) sizeRanges["32x32 and smaller"]++;
                else if (maxDim <= 64) sizeRanges["64x64"]++;
                else if (maxDim <= 128) sizeRanges["128x128"]++;
                else if (maxDim <= 256) sizeRanges["256x256"]++;
                else if (maxDim <= 512) sizeRanges["512x512"]++;
                else if (maxDim <= 1024) sizeRanges["1024x1024"]++;
                else if (maxDim <= 2048) sizeRanges["2048x2048"]++;
                else sizeRanges["4096x4096 and larger"]++;
            }
            
            foreach (var kvp in sizeRanges.Where(kvp => kvp.Value > 0))
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(kvp.Key, GUILayout.Width(150));
                EditorGUILayout.LabelField($"{kvp.Value} textures");
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawLargestTextures()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🏆 Largest Textures", EditorStyles.boldLabel);
            
            var largest = foundTextures.OrderByDescending(t => t.originalSize).Take(5);
            
            foreach (var texture in largest)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(texture.texture.name, GUILayout.Width(200));
                EditorGUILayout.LabelField($"{texture.width}x{texture.height}", GUILayout.Width(80));
                EditorGUILayout.LabelField(FormatBytes(texture.originalSize), GUILayout.Width(80));
                
                if (GUILayout.Button("Select", GUILayout.Width(60)))
                {
                    Selection.activeObject = texture.texture;
                    EditorGUIUtility.PingObject(texture.texture);
                }
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawMostCompressibleTextures()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("💎 Most Compressible", EditorStyles.boldLabel);
            
            var compressible = foundTextures
                .Where(t => !t.processed && t.originalSize > 1024 * 100) // > 100KB
                .OrderByDescending(t => EstimateCompressionSavings(t))
                .Take(5);
            
            foreach (var texture in compressible)
            {
                var estimatedSavings = EstimateCompressionSavings(texture);
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(texture.texture.name, GUILayout.Width(200));
                EditorGUILayout.LabelField(FormatBytes(estimatedSavings), GUILayout.Width(80));
                
                if (GUILayout.Button("Compress", GUILayout.Width(80)))
                {
                    CompressTexture(texture);
                }
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawPlatformSettings()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🎯 Platform-Specific Settings", EditorStyles.boldLabel);
            
            selectedPlatform = (BuildTarget)EditorGUILayout.EnumPopup("Platform", selectedPlatform);
            
            if (platformSettings.ContainsKey(selectedPlatform))
            {
                var settings = platformSettings[selectedPlatform];
                
                settings.overridden = EditorGUILayout.Toggle("Override for Platform", settings.overridden);
                
                if (settings.overridden)
                {
                    settings.maxTextureSize = EditorGUILayout.IntPopup("Max Texture Size", settings.maxTextureSize,
                        new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096" },
                        new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096 });
                    
                    // Use the same comprehensive format list as batch operations
                    int currentFormatIndex = System.Array.IndexOf(correspondingFormats, settings.format);
                    if (currentFormatIndex == -1) currentFormatIndex = 0; // Default to first format if not found

                    EditorGUILayout.LabelField("Select Texture Format:");
                    int newFormatIndex = EditorGUILayout.Popup("Format", currentFormatIndex, allTextureFormats);
                    if (newFormatIndex >= 0 && newFormatIndex < correspondingFormats.Length)
                    {
                        settings.format = correspondingFormats[newFormatIndex];
                    }

                    // Show format info panel (same as batch operations)
                    EditorGUILayout.BeginVertical("box");
                    EditorGUILayout.LabelField("📋 Format Information", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"Selected: {settings.format}", EditorStyles.miniLabel);
                    EditorGUILayout.LabelField($"Type: {GetCompressionType(settings.format)}", EditorStyles.miniLabel);
                    EditorGUILayout.LabelField($"Platform: {GetPlatformSupport(settings.format)}", EditorStyles.miniLabel);
                    EditorGUILayout.LabelField($"Quality: {GetQualityInfo(settings.format)}", EditorStyles.miniLabel);
                    EditorGUILayout.EndVertical();
                    settings.compressionQuality = EditorGUILayout.IntSlider("Quality", settings.compressionQuality, 0, 100);
                    settings.crunchedCompression = EditorGUILayout.Toggle("Crunch Compression", settings.crunchedCompression);
                }
            }
            
            EditorGUILayout.EndVertical();
        }

        private void ScanScene()
        {
            StartProcessing("Scanning Scene...");
            
            foundTextures.Clear();
            totalOriginalSize = 0;
            totalCompressedSize = 0;

            HashSet<Texture2D> processedTextures = new HashSet<Texture2D>();

            // Find all GameObjects in the scene
            GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);

            for (int i = 0; i < allObjects.Length; i++)
            {
                var obj = allObjects[i];
                UpdateProgress((float)i / allObjects.Length, $"Scanning {obj.name}...");
                
                // Check SpriteRenderers
                if (includeSprites)
                {
                    SpriteRenderer spriteRenderer = obj.GetComponent<SpriteRenderer>();
                    if (spriteRenderer != null && spriteRenderer.sprite != null)
                    {
                        Texture2D texture = spriteRenderer.sprite.texture;
                        if (texture != null && !processedTextures.Contains(texture))
                        {
                            AddAdvancedTextureInfo(texture, obj, "Sprite", true, false);
                            processedTextures.Add(texture);
                        }
                    }
                }

                // Check UI Images
                if (includeUI)
                {
                    Image image = obj.GetComponent<Image>();
                    if (image != null && image.sprite != null)
                    {
                        Texture2D texture = image.sprite.texture;
                        if (texture != null && !processedTextures.Contains(texture))
                        {
                            AddAdvancedTextureInfo(texture, obj, "UI", false, true);
                            processedTextures.Add(texture);
                        }
                    }
                }

                // Check Materials
                if (includeMaterials)
                {
                    Renderer renderer = obj.GetComponent<Renderer>();
                    if (renderer != null)
                    {
                        foreach (Material material in renderer.sharedMaterials)
                        {
                            if (material != null)
                            {
                                // Check main texture
                                if (material.mainTexture is Texture2D mainTex && !processedTextures.Contains(mainTex))
                                {
                                    AddAdvancedTextureInfo(mainTex, obj, "Material", false, false);
                                    processedTextures.Add(mainTex);
                                }

                                // Check other texture properties
                                var shader = material.shader;
                                if (shader != null)
                                {
                                    for (int j = 0; j < ShaderUtil.GetPropertyCount(shader); j++)
                                    {
                                        if (ShaderUtil.GetPropertyType(shader, j) == ShaderUtil.ShaderPropertyType.TexEnv)
                                        {
                                            string propertyName = ShaderUtil.GetPropertyName(shader, j);
                                            Texture texture = material.GetTexture(propertyName);
                                            if (texture is Texture2D tex2D && !processedTextures.Contains(tex2D))
                                            {
                                                AddAdvancedTextureInfo(tex2D, obj, "Material", false, false);
                                                processedTextures.Add(tex2D);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Check Terrain
                if (includeTerrainTextures)
                {
                    Terrain terrain = obj.GetComponent<Terrain>();
                    if (terrain != null && terrain.terrainData != null)
                    {
                        var terrainData = terrain.terrainData;
                        var alphamapTextures = terrainData.alphamapTextures;
                        
                        foreach (var alphaTexture in alphamapTextures)
                        {
                            if (alphaTexture != null && !processedTextures.Contains(alphaTexture))
                            {
                                AddAdvancedTextureInfo(alphaTexture, obj, "Terrain", false, false, true);
                                processedTextures.Add(alphaTexture);
                            }
                        }
                    }
                }

                // Check Particle Systems
                if (includeParticleTextures)
                {
                    ParticleSystem particles = obj.GetComponent<ParticleSystem>();
                    if (particles != null)
                    {
                        var renderer = particles.GetComponent<ParticleSystemRenderer>();
                        if (renderer != null && renderer.material != null && renderer.material.mainTexture is Texture2D particleTex)
                        {
                            if (!processedTextures.Contains(particleTex))
                            {
                                AddAdvancedTextureInfo(particleTex, obj, "Particle", false, false, false, true);
                                processedTextures.Add(particleTex);
                            }
                        }
                    }
                }
            }

            FinishProcessing();
            Debug.Log($"Found {foundTextures.Count} textures in the scene.");
            Repaint();
        }

        private void ScanProject()
        {
            StartProcessing("Scanning Project Assets...");
            
            foundTextures.Clear();
            totalOriginalSize = 0;
            totalCompressedSize = 0;

            string[] searchFolders = scanSelectedFolders && selectedFolders.Count > 0 ? 
                selectedFolders.ToArray() : new string[] { "Assets" };

            string[] guids = AssetDatabase.FindAssets("t:Texture2D", searchFolders);
            
            for (int i = 0; i < guids.Length; i++)
            {
                string guid = guids[i];
                string path = AssetDatabase.GUIDToAssetPath(guid);
                UpdateProgress((float)i / guids.Length, $"Scanning {Path.GetFileName(path)}...");
                
                Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                if (texture != null)
                {
                    AddAdvancedTextureInfo(texture, null, "Project Asset");
                }
            }

            FinishProcessing();
            Debug.Log($"Found {foundTextures.Count} textures in the project.");
            Repaint();
        }

        private void AddAdvancedTextureInfo(Texture2D texture, GameObject usedBy = null, string category = "Unknown", 
            bool isSprite = false, bool isUI = false, bool isTerrain = false, bool isParticle = false)
        {
            string path = AssetDatabase.GetAssetPath(texture);
            if (string.IsNullOrEmpty(path)) return;

            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer == null) return;

            var fileInfo = new FileInfo(path);
            
            TextureInfo info = new TextureInfo
            {
                texture = texture,
                path = path,
                name = texture != null ? texture.name : "Unknown", // Fix: Set the name field
                originalSize = GetAdvancedTextureMemorySize(texture),
                estimatedSize = EstimateCompressedSize(texture),
                isSprite = isSprite,
                isUI = isUI,
                isTerrain = isTerrain,
                isParticle = isParticle,
                hasAlpha = HasAlphaChannel(texture),
                isReadable = importer.isReadable,
                hasMipmaps = importer.mipmapEnabled,
                originalFormat = importer.textureCompression == TextureImporterCompression.Uncompressed ?
                    TextureImporterFormat.RGBA32 : importer.GetDefaultPlatformTextureSettings().format,
                currentFormat = importer.GetDefaultPlatformTextureSettings().format,
                recommendedFormat = GetRecommendedFormat(texture, isSprite, isUI),
                width = texture.width,
                height = texture.height,
                maxSize = importer.maxTextureSize,
                filterMode = importer.filterMode,
                wrapMode = importer.wrapMode,
                sRGBTexture = importer.sRGBTexture,
                category = category,
                lastModified = fileInfo.Exists ? fileInfo.LastWriteTime : DateTime.MinValue,
                fileSize = FormatBytes(fileInfo.Exists ? fileInfo.Length : 0)
            };

            if (usedBy != null)
                info.usedBy.Add(usedBy);

            info.compressionRatio = info.originalSize > 0 ? (float)info.estimatedSize / info.originalSize : 1f;

            foundTextures.Add(info);
            totalOriginalSize += info.originalSize;
        }

        private void StartProcessing(string status)
        {
            isProcessing = true;
            processingProgress = 0f;
            processingStatus = status;
        }

        private void UpdateProgress(float progress, string status)
        {
            processingProgress = progress;
            processingStatus = status;
            Repaint();
        }

        private void FinishProcessing()
        {
            isProcessing = false;
            processingProgress = 0f;
            processingStatus = "";
        }

        private bool HasAlphaChannel(Texture2D texture)
        {
            if (texture == null) return false;
            
            var path = AssetDatabase.GetAssetPath(texture);
            var importer = AssetImporter.GetAtPath(path) as TextureImporter;
            
            return importer != null && importer.DoesSourceTextureHaveAlpha();
        }

        private TextureImporterFormat GetRecommendedFormat(Texture2D texture, bool isSprite, bool isUI)
        {
            bool hasAlpha = HasAlphaChannel(texture);
            int width = texture.width;
            int height = texture.height;

            // Get texture type for better recommendations
            string path = AssetDatabase.GetAssetPath(texture);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            TextureImporterType textureType = importer?.textureType ?? TextureImporterType.Default;

            return GetOptimalFormatForTexture(hasAlpha, width, height, textureType, isSprite, isUI);
        }

        private TextureImporterFormat GetOptimalFormatForTexture(bool hasAlpha, int width, int height, TextureImporterType textureType, bool isSprite, bool isUI)
        {
            // Determine the best format based on texture properties and target platform
            switch (selectedPlatform)
            {
                case BuildTarget.Android:
                    return GetAndroidOptimalFormat(hasAlpha, width, height, textureType, isSprite, isUI);

                case BuildTarget.iOS:
                    return GetIOSOptimalFormat(hasAlpha, width, height, textureType, isSprite, isUI);

                case BuildTarget.WebGL:
                    return GetWebGLOptimalFormat(hasAlpha, width, height, textureType, isSprite, isUI);

                default: // Standalone platforms
                    return GetStandaloneOptimalFormat(hasAlpha, width, height, textureType, isSprite, isUI);
            }
        }

        private TextureImporterFormat GetAndroidOptimalFormat(bool hasAlpha, int width, int height, TextureImporterType textureType, bool isSprite, bool isUI)
        {
            // Android: Prefer ETC2 for newer devices, ASTC for high-end
            if (textureType == TextureImporterType.NormalMap)
                return TextureImporterFormat.ETC2_RGBA8; // Normal maps need alpha

            if (width >= 1024 || height >= 1024)
            {
                // Large textures: Use ASTC for better compression
                return hasAlpha ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;
            }
            else
            {
                // Smaller textures: Use ETC2
                return hasAlpha ? TextureImporterFormat.ETC2_RGBA8 : TextureImporterFormat.ETC2_RGB4;
            }
        }

        private TextureImporterFormat GetIOSOptimalFormat(bool hasAlpha, int width, int height, TextureImporterType textureType, bool isSprite, bool isUI)
        {
            // iOS: Prefer ASTC for newer devices, PVRTC for compatibility
            if (textureType == TextureImporterType.NormalMap)
                return TextureImporterFormat.ASTC_6x6; // Good balance for normal maps

            if (isSprite || isUI)
            {
                // UI elements: Use higher quality ASTC
                return hasAlpha ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;
            }
            else
            {
                // Regular textures: Balance between quality and size
                if (width >= 512 || height >= 512)
                    return hasAlpha ? TextureImporterFormat.ASTC_8x8 : TextureImporterFormat.ASTC_6x6;
                else
                    return hasAlpha ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;
            }
        }

        private TextureImporterFormat GetWebGLOptimalFormat(bool hasAlpha, int width, int height, TextureImporterType textureType, bool isSprite, bool isUI)
        {
            // WebGL: Prefer DXT with crunch compression for web delivery
            if (textureType == TextureImporterType.NormalMap)
                return TextureImporterFormat.DXT5;

            // Use crunched formats for web to reduce download size
            return hasAlpha ? TextureImporterFormat.DXT5Crunched : TextureImporterFormat.DXT1Crunched;
        }

        private TextureImporterFormat GetStandaloneOptimalFormat(bool hasAlpha, int width, int height, TextureImporterType textureType, bool isSprite, bool isUI)
        {
            // Standalone: Can use higher quality formats
            if (textureType == TextureImporterType.NormalMap)
                return TextureImporterFormat.BC5; // BC5 is optimal for normal maps

            if (width >= 2048 || height >= 2048)
            {
                // Large textures: Use BC7 for best quality
                return hasAlpha ? TextureImporterFormat.BC7 : TextureImporterFormat.DXT1;
            }
            else
            {
                // Standard textures: Use DXT
                return hasAlpha ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
            }
        }

        private string GetCompressionType(TextureImporterFormat format)
        {
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                    return "DXT/S3TC";

                case TextureImporterFormat.BC4:
                case TextureImporterFormat.BC5:
                case TextureImporterFormat.BC6H:
                case TextureImporterFormat.BC7:
                    return "BC (Block Compression)";

                case TextureImporterFormat.ETC_RGB4:
                case TextureImporterFormat.ETC2_RGB4:
                case TextureImporterFormat.ETC2_RGBA8:
                case TextureImporterFormat.ETC2_RGBA8Crunched:
                    return "ETC (Ericsson Texture Compression)";

                case TextureImporterFormat.PVRTC_RGB2:
                case TextureImporterFormat.PVRTC_RGB4:
                case TextureImporterFormat.PVRTC_RGBA2:
                case TextureImporterFormat.PVRTC_RGBA4:
                    return "PVRTC (PowerVR)";

                case TextureImporterFormat.ASTC_4x4:
                case TextureImporterFormat.ASTC_5x5:
                case TextureImporterFormat.ASTC_6x6:
                case TextureImporterFormat.ASTC_8x8:
                case TextureImporterFormat.ASTC_10x10:
                case TextureImporterFormat.ASTC_12x12:
                case TextureImporterFormat.ASTC_HDR_4x4:
                case TextureImporterFormat.ASTC_HDR_5x5:
                case TextureImporterFormat.ASTC_HDR_6x6:
                case TextureImporterFormat.ASTC_HDR_8x8:
                case TextureImporterFormat.ASTC_HDR_10x10:
                case TextureImporterFormat.ASTC_HDR_12x12:
                    return "ASTC (Adaptive Scalable Texture Compression)";

                default:
                    return "Uncompressed";
            }
        }

        private string GetPlatformSupport(TextureImporterFormat format)
        {
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                case TextureImporterFormat.BC4:
                case TextureImporterFormat.BC5:
                case TextureImporterFormat.BC6H:
                case TextureImporterFormat.BC7:
                    return "PC, Xbox, WebGL";

                case TextureImporterFormat.ETC_RGB4:
                case TextureImporterFormat.ETC2_RGB4:
                case TextureImporterFormat.ETC2_RGBA8:
                case TextureImporterFormat.ETC2_RGBA8Crunched:
                    return "Android, WebGL";

                case TextureImporterFormat.PVRTC_RGB2:
                case TextureImporterFormat.PVRTC_RGB4:
                case TextureImporterFormat.PVRTC_RGBA2:
                case TextureImporterFormat.PVRTC_RGBA4:
                    return "iOS (older devices)";

                case TextureImporterFormat.ASTC_4x4:
                case TextureImporterFormat.ASTC_5x5:
                case TextureImporterFormat.ASTC_6x6:
                case TextureImporterFormat.ASTC_8x8:
                case TextureImporterFormat.ASTC_10x10:
                case TextureImporterFormat.ASTC_12x12:
                    return "iOS, Android (modern), PC";

                case TextureImporterFormat.ASTC_HDR_4x4:
                case TextureImporterFormat.ASTC_HDR_5x5:
                case TextureImporterFormat.ASTC_HDR_6x6:
                case TextureImporterFormat.ASTC_HDR_8x8:
                case TextureImporterFormat.ASTC_HDR_10x10:
                case TextureImporterFormat.ASTC_HDR_12x12:
                    return "iOS, Android (HDR capable)";

                default:
                    return "All platforms";
            }
        }

        private string GetQualityInfo(TextureImporterFormat format)
        {
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                    return "Good (4:1 compression, no alpha)";

                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                    return "Good (4:1 compression, with alpha)";

                case TextureImporterFormat.BC7:
                    return "Excellent (high quality, modern)";

                case TextureImporterFormat.BC5:
                    return "Excellent (normal maps)";

                case TextureImporterFormat.ETC2_RGB4:
                    return "Good (mobile optimized)";

                case TextureImporterFormat.ETC2_RGBA8:
                    return "Good (mobile with alpha)";

                case TextureImporterFormat.ASTC_4x4:
                    return "Excellent (high quality)";

                case TextureImporterFormat.ASTC_6x6:
                    return "Very Good (balanced)";

                case TextureImporterFormat.ASTC_8x8:
                    return "Good (smaller size)";

                case TextureImporterFormat.ASTC_12x12:
                    return "Fair (maximum compression)";

                case TextureImporterFormat.RGBA32:
                case TextureImporterFormat.RGB24:
                    return "Perfect (uncompressed)";

                default:
                    return "Variable";
            }
        }

        private TextureImporterFormat GetSmartRecommendation(List<TextureInfo> textures)
        {
            if (textures.Count == 0) return TextureImporterFormat.DXT5;

            // Analyze the selected textures to determine the best format
            bool hasAlphaTextures = textures.Any(t => t.hasAlpha);
            bool hasLargeTextures = textures.Any(t => t.width >= 1024 || t.height >= 1024);
            bool hasSmallTextures = textures.Any(t => t.width <= 256 && t.height <= 256);
            bool hasNormalMaps = textures.Any(t => IsNormalMap(t));
            bool hasUITextures = textures.Any(t => t.isUI);
            bool hasSprites = textures.Any(t => t.isSprite);

            // Get the most common texture type
            var avgWidth = (int)textures.Average(t => t.width);
            var avgHeight = (int)textures.Average(t => t.height);

            // Determine the best format based on analysis
            if (hasNormalMaps)
            {
                // Normal maps have special requirements
                switch (selectedPlatform)
                {
                    case BuildTarget.Android:
                        return TextureImporterFormat.ETC2_RGBA8;
                    case BuildTarget.iOS:
                        return TextureImporterFormat.ASTC_6x6;
                    default:
                        return TextureImporterFormat.BC5; // Best for normal maps on PC
                }
            }

            if (hasUITextures || hasSprites)
            {
                // UI and sprites need good quality
                switch (selectedPlatform)
                {
                    case BuildTarget.Android:
                        return hasAlphaTextures ? TextureImporterFormat.ETC2_RGBA8 : TextureImporterFormat.ETC2_RGB4;
                    case BuildTarget.iOS:
                        return hasAlphaTextures ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;
                    case BuildTarget.WebGL:
                        return hasAlphaTextures ? TextureImporterFormat.DXT5Crunched : TextureImporterFormat.DXT1Crunched;
                    default:
                        return hasAlphaTextures ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                }
            }

            // For regular textures, consider size and platform
            if (hasLargeTextures)
            {
                // Large textures benefit from advanced compression
                switch (selectedPlatform)
                {
                    case BuildTarget.Android:
                        return hasAlphaTextures ? TextureImporterFormat.ASTC_8x8 : TextureImporterFormat.ASTC_6x6;
                    case BuildTarget.iOS:
                        return hasAlphaTextures ? TextureImporterFormat.ASTC_8x8 : TextureImporterFormat.ASTC_6x6;
                    case BuildTarget.WebGL:
                        return hasAlphaTextures ? TextureImporterFormat.DXT5Crunched : TextureImporterFormat.DXT1Crunched;
                    default:
                        return hasAlphaTextures ? TextureImporterFormat.BC7 : TextureImporterFormat.DXT1;
                }
            }

            // Default recommendation for mixed or small textures
            return GetOptimalFormatForTexture(hasAlphaTextures, avgWidth, avgHeight, TextureImporterType.Default, hasSprites, hasUITextures);
        }

        private bool IsNormalMap(TextureInfo textureInfo)
        {
            if (textureInfo.texture == null) return false;

            string path = AssetDatabase.GetAssetPath(textureInfo.texture);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

            return importer != null && importer.textureType == TextureImporterType.NormalMap;
        }

        private long EstimateCompressedSize(Texture2D texture)
        {
            if (texture == null) return 0;
            
            // Rough estimation based on DXT compression
            int width = texture.width;
            int height = texture.height;
            bool hasAlpha = HasAlphaChannel(texture);
            
            // DXT1 = 4 bits per pixel, DXT5 = 8 bits per pixel
            int bitsPerPixel = hasAlpha ? 8 : 4;
            
            return (width * height * bitsPerPixel) / 8;
        }

        private long EstimateCompressionSavings(TextureInfo textureInfo)
        {
            return textureInfo.originalSize - textureInfo.estimatedSize;
        }

        private long CalculatePotentialSavings()
        {
            return foundTextures.Where(t => !t.processed).Sum(t => EstimateCompressionSavings(t));
        }

        private void AnalyzeTextures(List<TextureInfo> textures)
        {
            foreach (var texture in textures)
            {
                AnalyzeTexture(texture);
            }
        }

        private void AnalyzeTexture(TextureInfo textureInfo)
        {
            // Update analysis data
            textureInfo.recommendedFormat = GetRecommendedFormat(textureInfo.texture, textureInfo.isSprite, textureInfo.isUI);
            textureInfo.estimatedSize = EstimateCompressedSize(textureInfo.texture);
            textureInfo.compressionRatio = textureInfo.originalSize > 0 ? (float)textureInfo.estimatedSize / textureInfo.originalSize : 1f;
            
            Debug.Log($"Analyzed {textureInfo.texture.name}: " +
                     $"Current: {textureInfo.currentFormat}, " +
                     $"Recommended: {textureInfo.recommendedFormat}, " +
                     $"Potential savings: {FormatBytes(EstimateCompressionSavings(textureInfo))}");
        }

        private void ApplyBatchOperations(List<TextureInfo> textures)
        {
            if (textures.Count == 0) return;

            StartProcessing("Applying Batch Operations...");

            for (int i = 0; i < textures.Count; i++)
            {
                var textureInfo = textures[i];
                UpdateProgress((float)i / textures.Count, $"Processing {textureInfo.texture.name}...");

                string path = textureInfo.path;
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

                if (importer == null) continue;

                bool changed = false;

                // Auto-apply current UI settings to batch operations
                string currentPlatform = GetCurrentPlatformName();
                var platformSettings = importer.GetPlatformTextureSettings(currentPlatform);
                platformSettings.overridden = true;

                // Apply max size from UI (either from override or general setting)
                int targetMaxSize = batchResize ? batchMaxSize : maxTextureSize;
                if (textureInfo.width > targetMaxSize || textureInfo.height > targetMaxSize)
                {
                    importer.maxTextureSize = targetMaxSize;
                    platformSettings.maxTextureSize = targetMaxSize;
                    changed = true;
                    Debug.Log($"Batch: Applied max size {targetMaxSize} to {textureInfo.texture.name} on {currentPlatform}");
                }

                // Apply format from UI (either from override or general setting)
                TextureImporterFormat targetFormat = batchFormat ? batchTargetFormat : GetOptimalFormatForTexture(textureInfo.hasAlpha, textureInfo.width, textureInfo.height, importer.textureType, textureInfo.isSprite, textureInfo.isUI);
                platformSettings.format = targetFormat;
                changed = true;
                Debug.Log($"Batch: Applied format {targetFormat} to {textureInfo.texture.name} on {currentPlatform}");

                // Apply quality from UI (either from override or general setting)
                int targetQuality = batchQuality ? batchQualityValue : compressionQuality;
                platformSettings.compressionQuality = targetQuality;
                Debug.Log($"Batch: Applied quality {targetQuality} to {textureInfo.texture.name} on {currentPlatform}");

                // Apply the platform settings
                importer.SetPlatformTextureSettings(platformSettings);

                if (changed)
                {
                    importer.SaveAndReimport();
                    textureInfo.processed = true;

                    // Update size info
                    long newSize = GetAdvancedTextureMemorySize(textureInfo.texture);
                    textureInfo.compressedSize = newSize;
                    totalCompressedSize += newSize;
                }
            }

            FinishProcessing();
            AssetDatabase.Refresh();
            Repaint();

            Debug.Log($"✅ Applied batch operations to {textures.Count} textures with current UI settings.");
        }

        private void CreateBackup()
        {
            string backupPath = EditorUtility.SaveFolderPanel("Select Backup Location", "", "TextureBackup");
            if (string.IsNullOrEmpty(backupPath)) return;
            
            // Implementation for creating backup would go here
            Debug.Log($"Backup functionality would save to: {backupPath}");
            EditorUtility.DisplayDialog("Backup", "Backup functionality is not yet implemented.", "OK");
        }

        private void RestoreBackup()
        {
            string backupPath = EditorUtility.OpenFolderPanel("Select Backup Location", "", "");
            if (string.IsNullOrEmpty(backupPath)) return;
            
            // Implementation for restoring backup would go here
            Debug.Log($"Restore functionality would restore from: {backupPath}");
            EditorUtility.DisplayDialog("Restore", "Restore functionality is not yet implemented.", "OK");
        }

        private void CompressAllTextures(List<TextureInfo> textures = null)
        {
            var texturesToCompress = textures ?? foundTextures.Where(t => !t.processed).ToList();
            
            if (texturesToCompress.Count == 0)
            {
                EditorUtility.DisplayDialog("No Textures", "No uncompressed textures found to compress.", "OK");
                return;
            }
            
            StartProcessing("Compressing Textures...");
            
            for (int i = 0; i < texturesToCompress.Count; i++)
            {
                var textureInfo = texturesToCompress[i];
                UpdateProgress((float)i / texturesToCompress.Count, $"Compressing {textureInfo.texture.name}...");
                
                CompressTexture(textureInfo);
            }
            
            FinishProcessing();
            AssetDatabase.Refresh();
            Repaint();
            
            Debug.Log($"Compressed {texturesToCompress.Count} textures.");
        }

        private void CompressTexture(TextureInfo textureInfo)
        {
            string path = textureInfo.path;
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

            if (importer == null) return;

            // First, set the appropriate texture type based on usage
            SetOptimalTextureType(importer, textureInfo);

            // Apply general settings - use UI selections when available
            int targetMaxSize = batchResize ? batchMaxSize : maxTextureSize;
            importer.maxTextureSize = targetMaxSize;
            importer.mipmapEnabled = generateMipmaps;
            importer.filterMode = filterMode;
            importer.wrapMode = wrapMode;
            importer.sRGBTexture = sRGBTexture;
            importer.alphaIsTransparency = alphaIsTransparency;
            importer.streamingMipmaps = streamingMipmaps;
            importer.streamingMipmapsPriority = streamingMipmapsPriority;

            // Apply UI-selected format if batch format is enabled
            if (batchFormat || batchResize || batchQuality)
            {
                // Apply UI settings to the current platform (Android, iOS, etc.)
                string currentPlatform = GetCurrentPlatformName();
                var platformSettings = importer.GetPlatformTextureSettings(currentPlatform);
                platformSettings.overridden = true;

                if (batchFormat)
                {
                    platformSettings.format = batchTargetFormat;
                    Debug.Log($"Applying UI-selected format to {currentPlatform}: {batchTargetFormat}");
                }

                if (batchResize)
                {
                    platformSettings.maxTextureSize = batchMaxSize;
                    Debug.Log($"Applying UI-selected max size to {currentPlatform}: {batchMaxSize}");
                }

                if (batchQuality)
                {
                    platformSettings.compressionQuality = batchQualityValue;
                    Debug.Log($"Applying UI-selected quality to {currentPlatform}: {batchQualityValue}");
                }

                importer.SetPlatformTextureSettings(platformSettings);

                Debug.Log($"✅ Applied UI settings to {textureInfo.texture.name} on {currentPlatform}: " +
                         (batchFormat ? $"Format={batchTargetFormat}, " : "") +
                         (batchResize ? $"MaxSize={batchMaxSize}, " : "") +
                         (batchQuality ? $"Quality={batchQualityValue}" : ""));
            }
            else
            {
                // Apply platform-specific settings with format validation
                ApplyPlatformSpecificSettings(importer, textureInfo);
            }

            importer.SaveAndReimport();

            // Update texture info
            long newSize = GetAdvancedTextureMemorySize(textureInfo.texture);
            textureInfo.compressedSize = newSize;
            textureInfo.processed = true;
            textureInfo.compressionRatio = textureInfo.originalSize > 0 ? (float)newSize / textureInfo.originalSize : 1f;

            totalCompressedSize += newSize;
            totalSavedSize += (textureInfo.originalSize - newSize);

            var savings = textureInfo.originalSize > 0 ?
                ((float)(textureInfo.originalSize - newSize) / textureInfo.originalSize) * 100f : 0f;

            Debug.Log($"Compressed {textureInfo.texture.name}: " +
                     $"{FormatBytes(textureInfo.originalSize)} -> {FormatBytes(newSize)} " +
                     $"({savings:F1}% saved)");

            ShowModernNotification($"✅ Compressed {textureInfo.texture.name}\n💰 Saved {savings:F1}%", Color.green);
        }

        private void SetOptimalTextureType(TextureImporter importer, TextureInfo textureInfo)
        {
            // Auto-detect texture usage if not already set
            if (textureInfo.usage == TextureUsage.Unknown)
            {
                textureInfo.usage = DetectTextureUsage(textureInfo);
            }
            
            // Determine optimal texture type based on usage and content
            switch (textureInfo.usage)
            {
                case TextureUsage.Sprite:
                    importer.textureType = TextureImporterType.Sprite;
                    importer.spriteImportMode = SpriteImportMode.Single;
                    importer.alphaIsTransparency = textureInfo.hasAlpha;
                    break;
                    
                case TextureUsage.UI:
                    importer.textureType = TextureImporterType.Sprite;
                    importer.spriteImportMode = SpriteImportMode.Single;
                    importer.alphaIsTransparency = textureInfo.hasAlpha;
                    break;
                    
                case TextureUsage.NormalMap:
                    importer.textureType = TextureImporterType.NormalMap;
                    importer.convertToNormalmap = false; // Assume it's already a normal map
                    break;
                    
                case TextureUsage.Lightmap:
                    importer.textureType = TextureImporterType.Lightmap;
                    importer.sRGBTexture = false; // Lightmaps are usually linear
                    break;
                    
                case TextureUsage.Cursor:
                    importer.textureType = TextureImporterType.Cursor;
                    break;
                    
                default:
                    // For materials and other uses, use Sprite type for better compression compatibility
                    // Sprite type is more flexible with compression formats than Default
                    importer.textureType = TextureImporterType.Sprite;
                    importer.spriteImportMode = SpriteImportMode.Single;
                    importer.alphaIsTransparency = textureInfo.hasAlpha;
                    break;
            }
            
            Debug.Log($"Set texture type for {textureInfo.name}: {importer.textureType} (detected usage: {textureInfo.usage})");
        }

        private TextureUsage DetectTextureUsage(TextureInfo textureInfo)
        {
            // Null safety checks
            if (textureInfo == null) return TextureUsage.Unknown;

            string path = textureInfo.path?.ToLower() ?? "";
            string name = textureInfo.name?.ToLower() ?? "";
            
            // Detect based on file path and name patterns
            if (path.Contains("sprite") || path.Contains("ui") || name.Contains("sprite") || name.Contains("ui"))
            {
                return TextureUsage.Sprite;
            }
            
            if (name.Contains("normal") || name.Contains("_n") || name.EndsWith("_normal"))
            {
                return TextureUsage.NormalMap;
            }
            
            if (name.Contains("lightmap") || path.Contains("lightmap"))
            {
                return TextureUsage.Lightmap;
            }
            
            if (name.Contains("cursor") || name.Contains("icon"))
            {
                return TextureUsage.Cursor;
            }
            
            // Check if it's used by UI components
            if (IsUsedByUIComponents(textureInfo.texture))
            {
                return TextureUsage.UI;
            }
            
            // Check if it's used as a sprite
            if (IsUsedAsSpriteRenderer(textureInfo.texture))
            {
                return TextureUsage.Sprite;
            }
            
            // Default to Material usage
            return TextureUsage.Material;
        }

        private bool IsUsedByUIComponents(Texture2D texture)
        {
            // Find all Image components in the scene that use this texture
            var images = FindObjectsByType<Image>(FindObjectsSortMode.None);
            foreach (var image in images)
            {
                if (image.sprite != null && image.sprite.texture == texture)
                {
                    return true;
                }
            }
            return false;
        }

        private bool IsUsedAsSpriteRenderer(Texture2D texture)
        {
            // Find all SpriteRenderer components in the scene that use this texture
            var spriteRenderers = FindObjectsByType<SpriteRenderer>(FindObjectsSortMode.None);
            foreach (var sr in spriteRenderers)
            {
                if (sr.sprite != null && sr.sprite.texture == texture)
                {
                    return true;
                }
            }
            return false;
        }

        private string GetCurrentPlatformName()
        {
            // Convert BuildTarget to platform string used by Unity TextureImporter
            switch (selectedPlatform)
            {
                case BuildTarget.Android:
                    return "Android";
                case BuildTarget.iOS:
                    return "iPhone";
                case BuildTarget.WebGL:
                    return "WebGL";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                case BuildTarget.StandaloneOSX:
                case BuildTarget.StandaloneLinux64:
                    return "Standalone";
                default:
                    return "Standalone"; // Default fallback
            }
        }

        private void ApplyPlatformSpecificSettings(TextureImporter importer, TextureInfo textureInfo)
        {
            // Apply settings for different platforms with proper format validation
            ApplyPlatformSettings(importer, textureInfo, "Standalone");
            ApplyPlatformSettings(importer, textureInfo, "Android");
            ApplyPlatformSettings(importer, textureInfo, "iPhone");
            ApplyPlatformSettings(importer, textureInfo, "WebGL");
        }

        private void ApplyPlatformSettings(TextureImporter importer, TextureInfo textureInfo, string platformName)
        {
            var platformSettings = importer.GetPlatformTextureSettings(platformName);
            platformSettings.overridden = true;
            platformSettings.maxTextureSize = maxTextureSize;
            platformSettings.compressionQuality = compressionQuality;

            // Get the optimal format for this platform and texture type
            var optimalFormat = GetOptimalTextureFormat(platformName, textureInfo, importer.textureType);
            platformSettings.format = optimalFormat;

            // Platform-specific optimizations
            switch (platformName)
            {
                case "Android":
                    // Android-specific optimizations for MAXIMUM file size reduction
                    platformSettings.crunchedCompression = true;
                    platformSettings.compressionQuality = 0; // Minimum quality for maximum compression
                    
                    // Use most aggressive compression formats for Android
                    if (textureInfo.hasAlpha)
                    {
                        // For alpha textures, use ETC2 with maximum compression
                        platformSettings.format = TextureImporterFormat.ETC2_RGBA8;
                    }
                    else
                    {
                        // For non-alpha textures, use ETC2 RGB with maximum compression
                        platformSettings.format = TextureImporterFormat.ETC2_RGB4;
                    }
                    
                    // Use UI-selected max size if available, otherwise apply Android optimization
                    int androidMaxSize;
                    if (batchResize)
                    {
                        // Respect user's UI selection
                        androidMaxSize = batchMaxSize;
                        Debug.Log($"Using UI-selected max size for Android: {androidMaxSize}");
                    }
                    else
                    {
                        // Apply default Android optimization only if user hasn't specified a size
                        androidMaxSize = Mathf.Min(512, maxTextureSize); // Even smaller for Android

                        // Further reduce based on texture dimensions
                        if (textureInfo.width > 1024 || textureInfo.height > 1024)
                        {
                            androidMaxSize = 512; // Large textures get heavily downscaled
                        }
                        else if (textureInfo.width > 512 || textureInfo.height > 512)
                        {
                            androidMaxSize = 256; // Medium textures get moderately downscaled
                        }
                        else
                        {
                            androidMaxSize = Mathf.Min(256, Mathf.Max(textureInfo.width, textureInfo.height));
                        }
                        Debug.Log($"Using automatic Android optimization: {androidMaxSize}");
                    }

                    platformSettings.maxTextureSize = androidMaxSize;
                    
                    // Additional Android optimizations
                    platformSettings.allowsAlphaSplitting = true; // Enable alpha splitting for better compression
                    
                    Debug.Log($"Android optimization for {textureInfo.name}: " +
                             $"Format={platformSettings.format}, MaxSize={androidMaxSize}, " +
                             $"Quality={platformSettings.compressionQuality}, Crunch=true");
                    break;
                    
                case "iPhone":
                    // iOS-specific optimizations
                    platformSettings.crunchedCompression = true;

                    // Use UI-selected quality if available
                    if (batchQuality)
                    {
                        platformSettings.compressionQuality = batchQualityValue;
                        Debug.Log($"Using UI-selected quality for iOS: {batchQualityValue}");
                    }
                    else
                    {
                        platformSettings.compressionQuality = Mathf.Max(25, compressionQuality - 15);
                    }

                    // Use UI-selected format if available
                    if (batchFormat)
                    {
                        platformSettings.format = batchTargetFormat;
                        Debug.Log($"Using UI-selected format for iOS: {batchTargetFormat}");
                    }
                    else
                    {
                        // Apply default iOS optimization
                        if (textureInfo.hasAlpha)
                        {
                            platformSettings.format = TextureImporterFormat.ASTC_6x6;
                        }
                        else
                        {
                            platformSettings.format = TextureImporterFormat.ASTC_4x4;
                        }
                    }

                    // Use UI-selected max size if available
                    if (batchResize)
                    {
                        platformSettings.maxTextureSize = batchMaxSize;
                        Debug.Log($"Using UI-selected max size for iOS: {batchMaxSize}");
                    }
                    else
                    {
                        platformSettings.maxTextureSize = Mathf.Min(1024, maxTextureSize);
                    }
                    break;
                    
                case "WebGL":
                    // WebGL-specific optimizations for web delivery
                    platformSettings.crunchedCompression = true;

                    // Use UI-selected quality if available
                    if (batchQuality)
                    {
                        platformSettings.compressionQuality = batchQualityValue;
                        Debug.Log($"Using UI-selected quality for WebGL: {batchQualityValue}");
                    }
                    else
                    {
                        platformSettings.compressionQuality = Mathf.Max(30, compressionQuality - 20);
                    }

                    // Use UI-selected format if available
                    if (batchFormat)
                    {
                        platformSettings.format = batchTargetFormat;
                        Debug.Log($"Using UI-selected format for WebGL: {batchTargetFormat}");
                    }
                    else
                    {
                        // Apply default WebGL optimization
                        if (textureInfo.hasAlpha)
                        {
                            platformSettings.format = TextureImporterFormat.DXT5Crunched;
                        }
                        else
                        {
                            platformSettings.format = TextureImporterFormat.DXT1Crunched;
                        }
                    }

                    // Use UI-selected max size if available
                    if (batchResize)
                    {
                        platformSettings.maxTextureSize = batchMaxSize;
                        Debug.Log($"Using UI-selected max size for WebGL: {batchMaxSize}");
                    }
                    else
                    {
                        platformSettings.maxTextureSize = Mathf.Min(2048, maxTextureSize);
                    }
                    break;
                    
                case "Standalone":
                    // Desktop platforms - balance quality and performance
                    platformSettings.crunchedCompression = useCrunchCompression;

                    // Use UI-selected format if available
                    if (batchFormat)
                    {
                        platformSettings.format = batchTargetFormat;
                        Debug.Log($"Using UI-selected format for Standalone: {batchTargetFormat}");
                    }
                    else
                    {
                        // Apply default Standalone optimization
                        if (textureInfo.hasAlpha)
                        {
                            platformSettings.format = useCrunchCompression ?
                                TextureImporterFormat.DXT5Crunched : TextureImporterFormat.DXT5;
                        }
                        else
                        {
                            platformSettings.format = useCrunchCompression ?
                                TextureImporterFormat.DXT1Crunched : TextureImporterFormat.DXT1;
                        }
                    }

                    // Use UI-selected quality if available
                    if (batchQuality)
                    {
                        platformSettings.compressionQuality = batchQualityValue;
                        Debug.Log($"Using UI-selected quality for Standalone: {batchQualityValue}");
                    }
                    else
                    {
                        platformSettings.compressionQuality = compressionQuality;
                    }

                    // Use UI-selected max size if available
                    if (batchResize)
                    {
                        platformSettings.maxTextureSize = batchMaxSize;
                        Debug.Log($"Using UI-selected max size for Standalone: {batchMaxSize}");
                    }
                    else
                    {
                        platformSettings.maxTextureSize = maxTextureSize; // Desktop can handle larger textures
                    }
                    break;
            }

            // Validate format compatibility with texture type
            ValidateFormatCompatibility(ref platformSettings, importer.textureType, textureInfo);

            importer.SetPlatformTextureSettings(platformSettings);
        }

        private TextureImporterFormat GetOptimalTextureFormat(string platform, TextureInfo textureInfo, TextureImporterType textureType)
        {
            // Return optimal format based on platform, texture content, and type
            switch (platform)
            {
                case "Android":
                    // Android supports ETC2, ASTC, and DXT formats
                    if (textureType == TextureImporterType.NormalMap)
                        return TextureImporterFormat.ETC2_RGBA8; // Normal maps need alpha channel
                    return textureInfo.hasAlpha ? TextureImporterFormat.ETC2_RGBA8 : TextureImporterFormat.ETC2_RGB4;

                case "iPhone":
                    // iOS supports ASTC, PVRTC formats
                    if (textureType == TextureImporterType.NormalMap)
                        return TextureImporterFormat.ASTC_6x6; // Good balance for normal maps
                    return textureInfo.hasAlpha ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;

                case "WebGL":
                    // WebGL supports DXT, ETC2, ASTC (limited)
                    if (textureType == TextureImporterType.NormalMap)
                        return TextureImporterFormat.DXT5;
                    return textureInfo.hasAlpha ? TextureImporterFormat.DXT5Crunched : TextureImporterFormat.DXT1Crunched;

                default: // Standalone (Windows, Mac, Linux)
                    if (textureType == TextureImporterType.NormalMap)
                    {
                        return TextureImporterFormat.DXT5; // DXT5 is standard for normal maps
                    }
                    return textureInfo.hasAlpha ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
            }
        }

        private void ValidateFormatCompatibility(ref TextureImporterPlatformSettings platformSettings, TextureImporterType textureType, TextureInfo textureInfo)
        {
            // Ensure format is compatible with texture type
            switch (textureType)
            {
                case TextureImporterType.Sprite:
                    // Sprites support most formats, but ensure alpha handling is correct
                    if (textureInfo.hasAlpha && !FormatSupportsAlpha(platformSettings.format))
                    {
                        platformSettings.format = GetAlphaSupportedFormat(platformSettings.format);
                    }
                    break;
                    
                case TextureImporterType.NormalMap:
                    // Normal maps need specific formats
                    if (!IsNormalMapCompatibleFormat(platformSettings.format))
                    {
                        platformSettings.format = TextureImporterFormat.DXT5; // Safe fallback for normal maps
                    }
                    break;
                    
                case TextureImporterType.Default:
                    // Default type has compatibility issues with DXT formats
                    // Use uncompressed formats for Default type to avoid errors
                    if (platformSettings.name == "DefaultTexturePlatform" || platformSettings.name == "Standalone")
                    {
                        platformSettings.format = textureInfo.hasAlpha ? TextureImporterFormat.RGBA32 : TextureImporterFormat.RGB24;
                    }
                    else
                    {
                        // For mobile platforms, use platform-specific formats
                        switch (platformSettings.name)
                        {
                            case "Android":
                                platformSettings.format = textureInfo.hasAlpha ? TextureImporterFormat.ETC2_RGBA8 : TextureImporterFormat.ETC2_RGB4;
                                break;
                            case "iPhone":
                                platformSettings.format = textureInfo.hasAlpha ? TextureImporterFormat.ASTC_6x6 : TextureImporterFormat.ASTC_4x4;
                                break;
                            case "WebGL":
                                platformSettings.format = textureInfo.hasAlpha ? TextureImporterFormat.DXT5 : TextureImporterFormat.DXT1;
                                break;
                            default:
                                platformSettings.format = textureInfo.hasAlpha ? TextureImporterFormat.RGBA32 : TextureImporterFormat.RGB24;
                                break;
                        }
                    }
                    break;
                    
                case TextureImporterType.Lightmap:
                    // Lightmaps have specific requirements
                    platformSettings.format = TextureImporterFormat.RGB24; // Safe format for lightmaps
                    break;
            }
        }

        private bool FormatSupportsAlpha(TextureImporterFormat format)
        {
            switch (format)
            {
                // DXT/BC formats with alpha
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                case TextureImporterFormat.BC7:

                // ETC2 formats with alpha
                case TextureImporterFormat.ETC2_RGBA8:
                case TextureImporterFormat.ETC2_RGBA8Crunched:

                // ASTC formats (all support alpha)
                case TextureImporterFormat.ASTC_4x4:
                case TextureImporterFormat.ASTC_5x5:
                case TextureImporterFormat.ASTC_6x6:
                case TextureImporterFormat.ASTC_8x8:
                case TextureImporterFormat.ASTC_10x10:
                case TextureImporterFormat.ASTC_12x12:
                case TextureImporterFormat.ASTC_HDR_4x4:
                case TextureImporterFormat.ASTC_HDR_5x5:
                case TextureImporterFormat.ASTC_HDR_6x6:
                case TextureImporterFormat.ASTC_HDR_8x8:
                case TextureImporterFormat.ASTC_HDR_10x10:
                case TextureImporterFormat.ASTC_HDR_12x12:

                // PVRTC formats with alpha
                case TextureImporterFormat.PVRTC_RGBA4:
                case TextureImporterFormat.PVRTC_RGBA2:

                // Uncompressed formats with alpha
                case TextureImporterFormat.RGBA32:
                case TextureImporterFormat.RGBA16:
                case TextureImporterFormat.ARGB32:
                case TextureImporterFormat.ARGB16:
                case TextureImporterFormat.Alpha8:
                case TextureImporterFormat.RGBAHalf:
                case TextureImporterFormat.RGBAFloat:
                    return true;
                default:
                    return false;
            }
        }

        private TextureImporterFormat GetAlphaSupportedFormat(TextureImporterFormat originalFormat)
        {
            // Convert non-alpha format to alpha-supporting equivalent
            switch (originalFormat)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                    return TextureImporterFormat.DXT5;
                    
                case TextureImporterFormat.ETC2_RGB4:
                    return TextureImporterFormat.ETC2_RGBA8;
                    
                case TextureImporterFormat.RGB24:
                    return TextureImporterFormat.RGBA32;
                    
                case TextureImporterFormat.RGB16:
                    return TextureImporterFormat.RGBA16;
                    
                default:
                    return TextureImporterFormat.RGBA32; // Safe fallback
            }
        }

        private bool IsNormalMapCompatibleFormat(TextureImporterFormat format)
        {
            switch (format)
            {
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.BC5:
                case TextureImporterFormat.ETC2_RGBA8:
                case TextureImporterFormat.ASTC_4x4:
                case TextureImporterFormat.ASTC_5x5:
                case TextureImporterFormat.ASTC_6x6:
                case TextureImporterFormat.RGBA32:
                case TextureImporterFormat.ARGB32:
                    return true;
                default:
                    return false;
            }
        }

        private bool IsDefaultTypeCompatibleFormat(TextureImporterFormat format)
        {
            // Most formats work with Default type, but some compressed formats might have issues
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT1Crunched:
                case TextureImporterFormat.DXT5Crunched:
                    // These formats can cause issues with Default type in some Unity versions
                    return false;
                default:
                    return true;
            }
        }

        private long GetAdvancedTextureMemorySize(Texture2D texture)
        {
            if (texture == null) return 0;
            
            try
            {
                // Try to get actual memory usage
                var serializedObject = new SerializedObject(texture);
                var property = serializedObject.FindProperty("m_Width");
                
                if (property != null)
                {
                    // More accurate calculation based on format
                    string path = AssetDatabase.GetAssetPath(texture);
                    TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                    
                    if (importer != null)
                    {
                        var format = importer.GetDefaultPlatformTextureSettings().format;
                        return CalculateTextureSizeByFormat(texture.width, texture.height, format, importer.mipmapEnabled);
                    }
                }
            }
            catch
            {
                // Fallback to simple calculation
            }
            
            // Fallback calculation
            return texture.width * texture.height * 4; // Assuming RGBA32
        }

        private long CalculateTextureSizeByFormat(int width, int height, TextureImporterFormat format, bool hasMipmaps)
        {
            long baseSize = 0;
            
            switch (format)
            {
                case TextureImporterFormat.DXT1:
                case TextureImporterFormat.DXT1Crunched:
                    baseSize = (width * height) / 2; // 4 bits per pixel
                    break;
                case TextureImporterFormat.DXT5:
                case TextureImporterFormat.DXT5Crunched:
                    baseSize = width * height; // 8 bits per pixel
                    break;
                case TextureImporterFormat.RGBA32:
                case TextureImporterFormat.ARGB32:
                    baseSize = width * height * 4; // 32 bits per pixel
                    break;
                case TextureImporterFormat.RGB24:
                    baseSize = width * height * 3; // 24 bits per pixel
                    break;
                case TextureImporterFormat.RGBA16:
                case TextureImporterFormat.ARGB16:
                    baseSize = width * height * 2; // 16 bits per pixel
                    break;
                default:
                    baseSize = width * height * 4; // Default to 32 bits
                    break;
            }
            
            // Add mipmap overhead (approximately 33% more)
            if (hasMipmaps)
            {
                baseSize = (long)(baseSize * 1.33f);
            }
            
            return baseSize;
        }

        private string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024f:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024f * 1024f):F1} MB";
            return $"{bytes / (1024f * 1024f * 1024f):F1} GB";
        }

        private void DrawAIAssistantTab()
        {
            EditorGUILayout.LabelField("🤖 AI-Powered Optimization Assistant", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            // Left panel - AI Settings
            EditorGUILayout.BeginVertical("box", GUILayout.Width(350));
            EditorGUILayout.LabelField("🧠 AI Configuration", EditorStyles.boldLabel);
            
            enableAIRecommendations = EditorGUILayout.Toggle("Enable AI Recommendations", enableAIRecommendations);
            enableMachineLearning = EditorGUILayout.Toggle("Enable Machine Learning", enableMachineLearning);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Optimization Priority");
            aiOptimizationPriority = (TextureAnalyzer.OptimizationPriority)EditorGUILayout.EnumPopup(aiOptimizationPriority);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Optimization Profile");
            if (availableProfiles != null && availableProfiles.Count > 0)
            {
                var profileNames = availableProfiles.Select(p => p.name).ToArray();
                var selectedIndex = availableProfiles.IndexOf(selectedProfile);
                var newIndex = EditorGUILayout.Popup(selectedIndex, profileNames);
                if (newIndex >= 0 && newIndex < availableProfiles.Count)
                {
                    selectedProfile = availableProfiles[newIndex];
                }
            }
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("🔍 Analyze All Textures", GUILayout.Height(40), GUILayout.MinWidth(200)))
            {
                AnalyzeAllTexturesWithAI();
            }

            if (GUILayout.Button("🎯 Apply AI Recommendations", GUILayout.Height(40), GUILayout.MinWidth(220)))
            {
                ApplyAIRecommendations();
            }
            
            EditorGUILayout.Space();
            
            // Machine Learning Stats
            if (enableMachineLearning)
            {
                EditorGUILayout.LabelField("📊 ML Statistics", EditorStyles.boldLabel);
                var analytics = TextureOptimizationProfiles.GetOptimizationAnalytics();
                
                if (analytics.ContainsKey("totalOptimizations"))
                {
                    EditorGUILayout.LabelField($"Total Optimizations: {analytics["totalOptimizations"]}");
                    EditorGUILayout.LabelField($"Avg Quality Score: {(float)analytics["averageQualityScore"]:F2}");
                    EditorGUILayout.LabelField($"Avg Compression: {(float)analytics["averageCompressionRatio"]:P1}");
                }
            }
            
            EditorGUILayout.EndVertical();
            
            // Right panel - AI Recommendations
            EditorGUILayout.BeginVertical();
            DrawAIRecommendations();
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        private void DrawPreviewTab()
        {
            EditorGUILayout.LabelField("👁️ Real-Time Preview & Comparison", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            enableRealTimePreview = EditorGUILayout.Toggle("Enable Real-Time Preview", enableRealTimePreview);
            
            if (!enableRealTimePreview)
            {
                EditorGUILayout.HelpBox("Real-time preview is disabled. Enable it to see texture compression previews.", MessageType.Info);
                return;
            }
            
            EditorGUILayout.Space();
            
            // Preview controls
            EditorGUILayout.BeginHorizontal("box");
            EditorGUILayout.LabelField("View Mode:");
            comparisonView.mode = (TexturePreviewSystem.ComparisonView.ViewMode)EditorGUILayout.EnumPopup(comparisonView.mode);
            
            if (comparisonView.mode == TexturePreviewSystem.ComparisonView.ViewMode.SplitView)
            {
                EditorGUILayout.LabelField("Split:");
                comparisonView.splitPosition = EditorGUILayout.Slider(comparisonView.splitPosition, 0f, 1f);
            }
            
            comparisonView.showGrid = EditorGUILayout.Toggle("Grid", comparisonView.showGrid);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Texture selection for preview
            var filteredTextures = GetFilteredTextures();
            if (filteredTextures.Count > 0)
            {
                EditorGUILayout.LabelField("Select texture to preview:");
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                
                foreach (var textureInfo in filteredTextures.Take(10)) // Limit for performance
                {
                    EditorGUILayout.BeginHorizontal("box");
                    
                    if (GUILayout.Button(textureInfo.texture.name, GUILayout.Height(35), GUILayout.MinWidth(150)))
                    {
                        ShowTexturePreview(textureInfo);
                    }
                    
                    EditorGUILayout.LabelField($"{textureInfo.width}x{textureInfo.height}", GUILayout.Width(80));
                    EditorGUILayout.LabelField(FormatBytes(textureInfo.originalSize), GUILayout.Width(80));
                    
                    EditorGUILayout.EndHorizontal();
                }
                
                EditorGUILayout.EndScrollView();
            }
            else
            {
                EditorGUILayout.HelpBox("No textures available for preview. Scan textures first.", MessageType.Info);
            }
        }

        private void DrawAIRecommendations()
        {
            EditorGUILayout.LabelField("🎯 AI Recommendations", EditorStyles.boldLabel);
            
            if (!enableAIRecommendations)
            {
                EditorGUILayout.HelpBox("AI recommendations are disabled.", MessageType.Info);
                return;
            }
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            var texturesWithAnalysis = foundTextures.Where(t => analysisCache.ContainsKey(t.texture)).Take(maxMLRecommendations);
            
            foreach (var textureInfo in texturesWithAnalysis)
            {
                var analysis = analysisCache[textureInfo.texture];
                DrawAIRecommendationCard(textureInfo, analysis);
            }
            
            if (!texturesWithAnalysis.Any())
            {
                EditorGUILayout.HelpBox("No AI analysis available. Click 'Analyze All Textures' to generate recommendations.", MessageType.Info);
            }
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawAIRecommendationCard(TextureInfo textureInfo, TextureAnalyzer.AnalysisResult analysis)
        {
            EditorGUILayout.BeginVertical("box");
            
            // Header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(textureInfo.texture.name, EditorStyles.boldLabel);
            
            // Confidence indicator
            var confidenceColor = analysis.qualityScore > 0.8f ? "green" : analysis.qualityScore > 0.6f ? "yellow" : "red";
            GUILayout.Label($"<color={confidenceColor}>●</color>", new GUIStyle(EditorStyles.label) { richText = true }, GUILayout.Width(20));
            
            EditorGUILayout.EndHorizontal();
            
            // Content type and complexity
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Type: {analysis.contentType}", GUILayout.Width(150));
            EditorGUILayout.LabelField($"Complexity: {analysis.complexityScore:F2}", GUILayout.Width(120));
            EditorGUILayout.LabelField($"Quality: {analysis.qualityScore:F2}", GUILayout.Width(100));
            EditorGUILayout.EndHorizontal();
            
            // Recommendations
            EditorGUILayout.LabelField("AI Recommendations:", EditorStyles.miniLabel);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Format: {analysis.recommendedFormat}", GUILayout.Width(200));
            EditorGUILayout.LabelField($"Size: {analysis.recommendedSize}px", GUILayout.Width(100));
            EditorGUILayout.LabelField($"Efficiency: {analysis.compressionEfficiency:P0}", GUILayout.Width(100));
            EditorGUILayout.EndHorizontal();
            
            // Analysis notes
            if (!string.IsNullOrEmpty(analysis.analysisNotes))
            {
                EditorGUILayout.LabelField("Notes:", EditorStyles.miniLabel);
                var notesStyle = new GUIStyle(EditorStyles.helpBox) { fontSize = 10 };
                EditorGUILayout.LabelField(analysis.analysisNotes, notesStyle);
            }
            
            // Action buttons
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Apply Recommendation", GUILayout.Height(32), GUILayout.MinWidth(160)))
            {
                ApplyAIRecommendation(textureInfo, analysis);
            }

            if (GUILayout.Button("Preview", GUILayout.Height(32), GUILayout.MinWidth(80)))
            {
                ShowTexturePreview(textureInfo);
            }

            if (GUILayout.Button("Details", GUILayout.Height(32), GUILayout.MinWidth(80)))
            {
                ShowDetailedAnalysis(textureInfo, analysis);
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        // Modern GUI Styling System
        private GUIStyle modernBackgroundStyle;
        private GUIStyle modernContentStyle;
        private GUIStyle modernHeaderStyle;
        private GUIStyle modernTabStyle;
        private GUIStyle modernTabActiveStyle;
        private GUIStyle modernButtonStyle;
        private GUIStyle modernBoxStyle;
        private GUIStyle modernProgressStyle;
        private GUIStyle modernCardStyle;
        private GUIStyle modernTitleStyle;
        private GUIStyle modernSubtitleStyle;
        private GUIStyle modernIconStyle;

        private void InitializeModernStyles()
        {
            if (modernBackgroundStyle == null)
            {
                // Modern background with subtle gradient
                modernBackgroundStyle = new GUIStyle(GUI.skin.box)
                {
                    normal = { background = CreateGradientTexture(new Color(0.15f, 0.15f, 0.15f), new Color(0.12f, 0.12f, 0.12f)) },
                    border = new RectOffset(0, 0, 0, 0),
                    padding = new RectOffset(10, 10, 10, 10)
                };

                // Modern content area
                modernContentStyle = new GUIStyle(GUI.skin.box)
                {
                    normal = { background = CreateSolidTexture(new Color(0.18f, 0.18f, 0.18f, 0.95f)) },
                    border = new RectOffset(1, 1, 1, 1),
                    padding = new RectOffset(15, 15, 15, 15),
                    margin = new RectOffset(5, 5, 5, 5)
                };

                // Modern header style
                modernHeaderStyle = new GUIStyle(EditorStyles.largeLabel)
                {
                    fontSize = 24,
                    fontStyle = FontStyle.Bold,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = new Color(0.9f, 0.9f, 0.9f) },
                    padding = new RectOffset(0, 0, 20, 20)
                };

                // Modern tab styles - Enhanced for better visibility
                modernTabStyle = new GUIStyle(GUI.skin.button)
                {
                    fontSize = 12,
                    fontStyle = FontStyle.Bold,
                    alignment = TextAnchor.MiddleCenter,
                    wordWrap = true,
                    normal = {
                        background = CreateSolidTexture(new Color(0.3f, 0.3f, 0.3f)),
                        textColor = new Color(0.9f, 0.9f, 0.9f)
                    },
                    hover = {
                        background = CreateSolidTexture(new Color(0.45f, 0.45f, 0.45f)),
                        textColor = Color.white
                    },
                    padding = new RectOffset(8, 8, 15, 15),
                    margin = new RectOffset(2, 2, 2, 2)
                };

                modernTabActiveStyle = new GUIStyle(modernTabStyle)
                {
                    fontSize = 13,
                    fontStyle = FontStyle.Bold,
                    wordWrap = true,
                    normal = {
                        background = CreateGradientTexture(new Color(0.2f, 0.7f, 1f), new Color(0.1f, 0.5f, 0.9f)),
                        textColor = Color.white
                    },
                    hover = {
                        background = CreateGradientTexture(new Color(0.3f, 0.8f, 1f), new Color(0.2f, 0.6f, 1f)),
                        textColor = Color.white
                    },
                    padding = new RectOffset(8, 8, 16, 16)
                };

                // Modern button style
                modernButtonStyle = new GUIStyle(GUI.skin.button)
                {
                    fontSize = 12,
                    fontStyle = FontStyle.Bold,
                    alignment = TextAnchor.MiddleCenter,
                    normal = {
                        background = CreateGradientTexture(new Color(0.3f, 0.3f, 0.3f), new Color(0.2f, 0.2f, 0.2f)),
                        textColor = new Color(0.9f, 0.9f, 0.9f)
                    },
                    hover = {
                        background = CreateGradientTexture(new Color(0.4f, 0.4f, 0.4f), new Color(0.3f, 0.3f, 0.3f)),
                        textColor = Color.white
                    },
                    active = {
                        background = CreateGradientTexture(new Color(0.2f, 0.2f, 0.2f), new Color(0.1f, 0.1f, 0.1f)),
                        textColor = new Color(0.8f, 0.8f, 0.8f)
                    },
                    border = new RectOffset(2, 2, 2, 2),
                    padding = new RectOffset(10, 10, 6, 6),
                    margin = new RectOffset(2, 2, 2, 2)
                };

                // Modern box style
                modernBoxStyle = new GUIStyle(GUI.skin.box)
                {
                    normal = {
                        background = CreateSolidTexture(new Color(0.22f, 0.22f, 0.22f, 0.9f)),
                        textColor = new Color(0.9f, 0.9f, 0.9f)
                    },
                    border = new RectOffset(1, 1, 1, 1),
                    padding = new RectOffset(12, 12, 12, 12),
                    margin = new RectOffset(4, 4, 4, 4)
                };

                // Modern card style
                modernCardStyle = new GUIStyle(GUI.skin.box)
                {
                    normal = {
                        background = CreateSolidTexture(new Color(0.25f, 0.25f, 0.25f, 0.95f)),
                        textColor = new Color(0.95f, 0.95f, 0.95f)
                    },
                    border = new RectOffset(2, 2, 2, 2),
                    padding = new RectOffset(15, 15, 15, 15),
                    margin = new RectOffset(5, 5, 5, 5)
                };

                // Modern title style
                modernTitleStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    fontStyle = FontStyle.Bold,
                    normal = { textColor = new Color(0.95f, 0.95f, 0.95f) },
                    padding = new RectOffset(0, 0, 5, 5)
                };

                // Modern subtitle style
                modernSubtitleStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 11,
                    fontStyle = FontStyle.Normal,
                    normal = { textColor = new Color(0.7f, 0.7f, 0.7f) },
                    padding = new RectOffset(0, 0, 2, 2)
                };

                // Modern icon style
                modernIconStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = new Color(0.8f, 0.8f, 0.8f) }
                };
            }
        }

        private Texture2D CreateSolidTexture(Color color)
        {
            var texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }

        private Texture2D CreateGradientTexture(Color topColor, Color bottomColor)
        {
            var texture = new Texture2D(1, 32);
            for (int i = 0; i < 32; i++)
            {
                float t = (float)i / 31f;
                Color color = Color.Lerp(topColor, bottomColor, t);
                texture.SetPixel(0, i, color);
            }
            texture.Apply();
            return texture;
        }

        private void DrawModernHeader()
        {
            // Header background with gradient
            var headerRect = EditorGUILayout.GetControlRect(false, 80);
            var gradientTexture = CreateGradientTexture(new Color(0.1f, 0.3f, 0.6f), new Color(0.05f, 0.15f, 0.3f));
            GUI.DrawTexture(headerRect, gradientTexture);

            // Header content
            GUILayout.BeginArea(headerRect);
            EditorGUILayout.BeginVertical();

            GUILayout.FlexibleSpace();

            // Main title with modern styling
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();

            var titleStyle = new GUIStyle(modernHeaderStyle)
            {
                normal = { textColor = Color.white }
            };

            EditorGUILayout.LabelField("🎨 Advanced Texture Compressor Pro", titleStyle);
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            // Subtitle with stats
            if (foundTextures.Count > 0)
            {
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();

                var statsStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = new Color(0.8f, 0.9f, 1f) },
                    alignment = TextAnchor.MiddleCenter
                };

                var savings = totalCompressedSize > 0 && totalOriginalSize > 0 ?
                    ((float)(totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100f : 0f;

                string statsText = $"📊 {foundTextures.Count} textures • 💾 {FormatBytes(totalOriginalSize)}";
                if (totalCompressedSize > 0)
                {
                    statsText += $" • 💰 {savings:F1}% saved";
                }

                EditorGUILayout.LabelField(statsText, statsStyle);
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndVertical();
            GUILayout.EndArea();

            EditorGUILayout.Space(10);
        }

        private void DrawModernProgressBar()
        {
            EditorGUILayout.BeginVertical(modernBoxStyle);

            // Progress status with modern styling
            var statusStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                normal = { textColor = new Color(0.2f, 0.8f, 1f) },
                fontSize = 12
            };

            EditorGUILayout.LabelField($"⚡ {processingStatus}", statusStyle);

            // Enhanced progress bar
            var progressRect = EditorGUILayout.GetControlRect(false, 20);

            // Background
            var bgColor = new Color(0.1f, 0.1f, 0.1f);
            EditorGUI.DrawRect(progressRect, bgColor);

            // Progress fill with gradient
            var fillRect = new Rect(progressRect.x, progressRect.y, progressRect.width * processingProgress, progressRect.height);
            var progressGradient = CreateGradientTexture(new Color(0.2f, 0.8f, 1f), new Color(0.1f, 0.6f, 0.8f));
            GUI.DrawTexture(fillRect, progressGradient);

            // Progress text overlay
            var textStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = Color.white },
                fontStyle = FontStyle.Bold
            };

            GUI.Label(progressRect, $"{processingProgress * 100:F0}%", textStyle);

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(5);
        }

        private void DrawModernTabSystem()
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();

            // Custom tab rendering with modern styling
            for (int i = 0; i < tabNames.Length; i++)
            {
                var tabStyle = selectedTab == i ? modernTabActiveStyle : modernTabStyle;

                // Add icons to tabs
                string tabText = GetTabIcon(i) + " " + tabNames[i];

                if (GUILayout.Button(tabText, tabStyle, GUILayout.MinWidth(120), GUILayout.Height(50)))
                {
                    selectedTab = i;
                }
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);
        }

        private string GetTabIcon(int tabIndex)
        {
            switch (tabIndex)
            {
                case 0: return "🔍"; // Scene Scan
                case 1: return "🗂️"; // Project Scan
                case 2: return "⚡"; // Batch Operations
                case 3: return "📊"; // Analytics
                case 4: return "🤖"; // AI Assistant
                case 5: return "👁️"; // Preview
                case 6: return "⚡"; // Performance
                case 7: return "🔄"; // Workflows
                case 8: return "☁️"; // Cloud
                case 9: return "⚙️"; // Settings
                default: return "📋";
            }
        }

        private GUIStyle CreateActionButtonStyle(Color normalColor, Color hoverColor)
        {
            return new GUIStyle(GUI.skin.button)
            {
                fontSize = 11,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                normal = {
                    background = CreateGradientTexture(normalColor, normalColor * 0.8f),
                    textColor = Color.white
                },
                hover = {
                    background = CreateGradientTexture(hoverColor, hoverColor * 0.8f),
                    textColor = Color.white
                },
                active = {
                    background = CreateGradientTexture(normalColor * 0.7f, normalColor * 0.5f),
                    textColor = new Color(0.9f, 0.9f, 0.9f)
                },
                border = new RectOffset(2, 2, 2, 2),
                padding = new RectOffset(8, 8, 4, 4),
                margin = new RectOffset(1, 1, 1, 1)
            };
        }

        private GUIStyle CreateDisabledButtonStyle()
        {
            return new GUIStyle(GUI.skin.button)
            {
                fontSize = 11,
                fontStyle = FontStyle.Normal,
                alignment = TextAnchor.MiddleCenter,
                normal = {
                    background = CreateSolidTexture(new Color(0.15f, 0.15f, 0.15f)),
                    textColor = new Color(0.4f, 0.4f, 0.4f)
                },
                border = new RectOffset(2, 2, 2, 2),
                padding = new RectOffset(8, 8, 4, 4),
                margin = new RectOffset(1, 1, 1, 1)
            };
        }

        private GUIStyle CreateModernToggleStyle(Color activeColor)
        {
            return new GUIStyle(GUI.skin.toggle)
            {
                fontSize = 11,
                fontStyle = FontStyle.Normal,
                normal = { textColor = new Color(0.8f, 0.8f, 0.8f) },
                onNormal = { textColor = activeColor },
                hover = { textColor = new Color(0.9f, 0.9f, 0.9f) },
                onHover = { textColor = activeColor * 1.2f },
                padding = new RectOffset(20, 4, 4, 4)
            };
        }

        // Modern notification system
        private struct ModernNotification
        {
            public string message;
            public Color color;
            public float duration;
            public float startTime;
            public bool isVisible;
        }

        private ModernNotification currentNotification;

        private void ShowModernNotification(string message, Color color, float duration = 3f)
        {
            currentNotification = new ModernNotification
            {
                message = message,
                color = color,
                duration = duration,
                startTime = Time.realtimeSinceStartup,
                isVisible = true
            };
            Repaint();
        }

        private void DrawModernNotification()
        {
            if (!currentNotification.isVisible) return;

            float elapsed = Time.realtimeSinceStartup - currentNotification.startTime;
            if (elapsed > currentNotification.duration)
            {
                currentNotification.isVisible = false;
                return;
            }

            // Fade animation
            float alpha = 1f;
            if (elapsed > currentNotification.duration - 0.5f)
            {
                alpha = (currentNotification.duration - elapsed) / 0.5f;
            }

            var notificationRect = new Rect(position.width - 320, 10, 300, 60);

            // Background with transparency
            var bgColor = new Color(0.1f, 0.1f, 0.1f, 0.9f * alpha);
            EditorGUI.DrawRect(notificationRect, bgColor);

            // Border
            var borderColor = new Color(currentNotification.color.r, currentNotification.color.g, currentNotification.color.b, alpha);
            var borderRect = new Rect(notificationRect.x - 1, notificationRect.y - 1, notificationRect.width + 2, notificationRect.height + 2);
            EditorGUI.DrawRect(borderRect, borderColor);

            // Text
            var textStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                alignment = TextAnchor.MiddleCenter,
                normal = { textColor = new Color(1f, 1f, 1f, alpha) },
                fontSize = 12,
                wordWrap = true
            };

            GUI.Label(notificationRect, currentNotification.message, textStyle);

            // Schedule repaint for animation
            if (currentNotification.isVisible)
            {
                Repaint();
            }
        }

        // Advanced AI Methods
        private void AnalyzeAllTexturesWithAI()
        {
            if (foundTextures.Count == 0)
            {
                EditorUtility.DisplayDialog("No Textures", "No textures found to analyze. Please scan first.", "OK");
                return;
            }

            StartProcessing("AI Analysis in Progress...");
            analysisCache.Clear();

            for (int i = 0; i < foundTextures.Count; i++)
            {
                var textureInfo = foundTextures[i];
                UpdateProgress((float)i / foundTextures.Count, $"Analyzing {textureInfo.texture.name}...");

                var analysis = TextureAnalyzer.AnalyzeTexture(textureInfo.texture, aiOptimizationPriority);
                if (analysis != null)
                {
                    analysisCache[textureInfo.texture] = analysis;
                    
                    // Update texture info with AI recommendations
                    textureInfo.recommendedFormat = analysis.recommendedFormat;
                    textureInfo.category = analysis.contentType.ToString();
                }
            }

            FinishProcessing();
            Debug.Log($"AI analysis completed for {analysisCache.Count} textures.");
            Repaint();
        }

        private void ApplyAIRecommendations()
        {
            var texturesWithRecommendations = foundTextures.Where(t => analysisCache.ContainsKey(t.texture)).ToList();
            
            if (texturesWithRecommendations.Count == 0)
            {
                EditorUtility.DisplayDialog("No Recommendations", "No AI recommendations available. Run analysis first.", "OK");
                return;
            }

            StartProcessing("Applying AI Recommendations...");

            for (int i = 0; i < texturesWithRecommendations.Count; i++)
            {
                var textureInfo = texturesWithRecommendations[i];
                var analysis = analysisCache[textureInfo.texture];
                
                UpdateProgress((float)i / texturesWithRecommendations.Count, $"Optimizing {textureInfo.texture.name}...");
                
                ApplyAIRecommendation(textureInfo, analysis);
            }

            FinishProcessing();
            AssetDatabase.Refresh();
            Repaint();
        }

        private void ApplyAIRecommendation(TextureInfo textureInfo, TextureAnalyzer.AnalysisResult analysis)
        {
            var path = textureInfo.path;
            var importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer == null) return;

            // Get AI-powered recommendation
            var recommendation = TextureOptimizationProfiles.GetAIRecommendation(textureInfo.texture, analysis, selectedPlatform);
            
            // Apply settings
            importer.maxTextureSize = recommendation.maxSize;
            importer.mipmapEnabled = recommendation.generateMipmaps;
            importer.filterMode = recommendation.filterMode;
            importer.wrapMode = recommendation.wrapMode;

            var platformSettings = importer.GetDefaultPlatformTextureSettings();
            platformSettings.format = recommendation.preferredFormat;
            platformSettings.maxTextureSize = recommendation.maxSize;
            platformSettings.compressionQuality = recommendation.qualityLevel;
            platformSettings.crunchedCompression = recommendation.useCrunchCompression;

            importer.SetPlatformTextureSettings(platformSettings);
            importer.SaveAndReimport();

            // Record for machine learning
            if (enableMachineLearning)
            {
                var qualityScore = analysis.qualityScore;
                var compressionRatio = analysis.compressionEfficiency;
                
                TextureOptimizationProfiles.RecordOptimizationAttempt(
                    textureInfo.texture, 
                    recommendation.preferredFormat, 
                    recommendation.maxSize, 
                    recommendation.qualityLevel,
                    qualityScore, 
                    compressionRatio
                );
            }

            // Update texture info
            textureInfo.processed = true;
            textureInfo.currentFormat = recommendation.preferredFormat;
            
            Debug.Log($"Applied AI recommendation to {textureInfo.texture.name}: {recommendation.preferredFormat} @ {recommendation.maxSize}px");
        }

        private void ShowTexturePreview(TextureInfo textureInfo)
        {
            if (!enableRealTimePreview) return;

            var analysis = analysisCache.ContainsKey(textureInfo.texture) ? analysisCache[textureInfo.texture] : null;
            if (analysis == null) return;

            var previewData = TexturePreviewSystem.GeneratePreview(
                textureInfo.texture, 
                analysis.recommendedFormat, 
                analysis.recommendedSize
            );

            if (previewData != null)
            {
                previewCache[textureInfo.texture] = previewData;
                
                // Switch to preview tab
                selectedTab = 5;
                Repaint();
            }
        }

        private void ShowDetailedAnalysis(TextureInfo textureInfo, TextureAnalyzer.AnalysisResult analysis)
        {
            var content = $"Detailed Analysis for {textureInfo.texture.name}\n\n";
            content += $"Content Type: {analysis.contentType}\n";
            content += $"Complexity Score: {analysis.complexityScore:F3}\n";
            content += $"Quality Score: {analysis.qualityScore:F3}\n";
            content += $"Noise Level: {analysis.noiseLevel:F3}\n";
            content += $"Color Variance: {analysis.colorVariance:F3}\n";
            content += $"Compression Efficiency: {analysis.compressionEfficiency:P1}\n";
            content += $"Has Transparency: {analysis.hasTransparency}\n";
            content += $"Is Monochrome: {analysis.isMonochrome}\n";
            content += $"Power of Two: {analysis.isPowerOfTwo}\n\n";
            content += $"Recommendations:\n";
            content += $"Format: {analysis.recommendedFormat}\n";
            content += $"Size: {analysis.recommendedSize}px\n\n";
            content += $"Analysis Notes:\n{analysis.analysisNotes}";

            EditorUtility.DisplayDialog("AI Analysis Details", content, "OK");
        }

        private void DrawPerformanceTab()
        {
            EditorGUILayout.LabelField("⚡ Performance Dashboard", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // Performance controls
            EditorGUILayout.BeginHorizontal("box");
            
            if (GUILayout.Button("📊 Generate Report", GUILayout.Height(35), GUILayout.MinWidth(150)))
            {
                var report = TexturePerformanceMonitor.GeneratePerformanceReport();
                EditorUtility.DisplayDialog("Performance Report", report, "OK");
            }

            if (GUILayout.Button("📁 Export CSV", GUILayout.Height(35), GUILayout.MinWidth(120)))
            {
                var path = EditorUtility.SaveFilePanel("Export Performance Data", "", "texture_performance", "csv");
                if (!string.IsNullOrEmpty(path))
                {
                    TexturePerformanceMonitor.ExportToCSV(path);
                }
            }

            if (GUILayout.Button("🗑️ Clear Data", GUILayout.Height(35), GUILayout.MinWidth(120)))
            {
                if (EditorUtility.DisplayDialog("Clear Performance Data", "Are you sure you want to clear all performance data?", "Yes", "No"))
                {
                    TexturePerformanceMonitor.ClearPerformanceData();
                }
            }
            
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            // System Information
            DrawSystemInformation();
            EditorGUILayout.Space();
            
            // Performance Analytics
            DrawPerformanceAnalytics();
            EditorGUILayout.Space();
            
            // Recent Sessions
            DrawRecentSessions();
            EditorGUILayout.Space();
            
            // Operation Performance
            DrawOperationPerformance();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawSystemInformation()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("💻 System Information", EditorStyles.boldLabel);
            
            var systemInfo = TexturePerformanceMonitor.GetSystemInfo();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"CPU Cores: {systemInfo["processorCount"]}", GUILayout.Width(150));
            EditorGUILayout.LabelField($"System RAM: {systemInfo["systemMemorySize"]} MB", GUILayout.Width(200));
            EditorGUILayout.LabelField($"Graphics RAM: {systemInfo["graphicsMemorySize"]} MB");
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Unity: {systemInfo["unityVersion"]}", GUILayout.Width(150));
            EditorGUILayout.LabelField($"Max Texture Size: {systemInfo["maxTextureSize"]}", GUILayout.Width(200));
            EditorGUILayout.LabelField($"Current Memory: {FormatBytes((long)systemInfo["currentMemoryUsage"])}");
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.LabelField($"OS: {systemInfo["operatingSystem"]}", EditorStyles.miniLabel);
            
            EditorGUILayout.EndVertical();
        }

        private void DrawPerformanceAnalytics()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📈 Performance Analytics", EditorStyles.boldLabel);
            
            var analytics = TexturePerformanceMonitor.GetPerformanceAnalytics();
            
            if (analytics.ContainsKey("totalSessions") && (int)analytics["totalSessions"] > 0)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Total Sessions: {analytics["totalSessions"]}", GUILayout.Width(150));
                EditorGUILayout.LabelField($"Textures Processed: {analytics["totalTexturesProcessed"]}", GUILayout.Width(200));
                EditorGUILayout.LabelField($"Success Rate: {(float)analytics["successRate"]:P1}");
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Total Time: {(long)analytics["totalTimeTaken"]:N0}ms", GUILayout.Width(150));
                EditorGUILayout.LabelField($"Avg Time/Texture: {(float)analytics["averageTimePerTexture"]:F1}ms", GUILayout.Width(200));
                EditorGUILayout.LabelField($"Space Saved: {FormatBytes((long)analytics["totalSpaceSaved"])}");
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Avg Compression: {(float)analytics["averageCompressionRatio"]:P1}", GUILayout.Width(150));
                
                if (analytics.ContainsKey("performanceTrend"))
                {
                    var trend = (float)analytics["performanceTrend"];
                    var trendText = trend > 0 ? $"↗️ +{trend:P1}" : $"↘️ {trend:P1}";
                    var trendColor = trend > 0 ? "green" : "red";
                    EditorGUILayout.LabelField($"<color={trendColor}>{trendText}</color>", 
                        new GUIStyle(EditorStyles.label) { richText = true }, GUILayout.Width(100));
                }
                
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("No performance data available. Start using the tool to see analytics.", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawRecentSessions()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📋 Recent Sessions", EditorStyles.boldLabel);
            
            var sessions = TexturePerformanceMonitor.GetSessions();
            var recentSessions = sessions.TakeLast(5).Reverse().ToList();
            
            if (recentSessions.Count > 0)
            {
                foreach (var session in recentSessions)
                {
                    EditorGUILayout.BeginHorizontal("box");
                    
                    EditorGUILayout.LabelField($"{session.startTime:MM/dd HH:mm}", GUILayout.Width(80));
                    EditorGUILayout.LabelField($"{session.texturesProcessed} textures", GUILayout.Width(100));
                    EditorGUILayout.LabelField($"{session.totalTimeTaken:N0}ms", GUILayout.Width(80));
                    EditorGUILayout.LabelField($"{session.averageCompressionRatio:P1}", GUILayout.Width(60));
                    
                    var statusIcon = session.wasSuccessful ? "✅" : "❌";
                    EditorGUILayout.LabelField(statusIcon, GUILayout.Width(20));
                    
                    if (!string.IsNullOrEmpty(session.sessionNotes))
                    {
                        EditorGUILayout.LabelField(session.sessionNotes, EditorStyles.miniLabel);
                    }
                    
                    EditorGUILayout.EndHorizontal();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No sessions recorded yet.", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawOperationPerformance()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("⚡ Operation Performance", EditorStyles.boldLabel);
            
            var metrics = TexturePerformanceMonitor.GetAllMetrics();
            
            if (metrics.Count > 0)
            {
                foreach (var metric in metrics.Values.OrderByDescending(m => m.totalExecutionTime))
                {
                    EditorGUILayout.BeginHorizontal("box");
                    
                    EditorGUILayout.LabelField(metric.operationName, EditorStyles.boldLabel, GUILayout.Width(150));
                    EditorGUILayout.LabelField($"Runs: {metric.executionCount}", GUILayout.Width(80));
                    EditorGUILayout.LabelField($"Avg: {metric.averageExecutionTime}ms", GUILayout.Width(100));
                    EditorGUILayout.LabelField($"Total: {metric.totalExecutionTime:N0}ms", GUILayout.Width(120));
                    
                    if (metric.texturesProcessed > 0)
                    {
                        EditorGUILayout.LabelField($"Textures: {metric.texturesProcessed}", GUILayout.Width(100));
                    }
                    
                    if (metric.totalMemoryUsed > 0)
                    {
                        EditorGUILayout.LabelField($"Memory: {FormatBytes(metric.totalMemoryUsed)}");
                    }
                    
                    EditorGUILayout.EndHorizontal();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No operation metrics available yet.", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }

        // Enhanced methods with performance monitoring
        private void ScanSceneWithPerformanceMonitoring()
        {
            using (var profiler = TexturePerformanceMonitor.ProfileOperation("Scene Scan"))
            {
                TexturePerformanceMonitor.StartSession("Scene Scan");
                ScanScene();
                TexturePerformanceMonitor.EndSession();
            }
        }

        private void ScanProjectWithPerformanceMonitoring()
        {
            using (var profiler = TexturePerformanceMonitor.ProfileOperation("Project Scan"))
            {
                TexturePerformanceMonitor.StartSession("Project Scan");
                ScanProject();
                TexturePerformanceMonitor.EndSession();
            }
        }

        private void CompressAllTexturesWithPerformanceMonitoring(List<TextureInfo> textures = null)
        {
            using (var profiler = TexturePerformanceMonitor.ProfileOperation("Batch Compression"))
            {
                TexturePerformanceMonitor.StartSession($"Batch Compression - {textures?.Count ?? foundTextures.Count} textures");
                CompressAllTextures(textures);
                TexturePerformanceMonitor.EndSession();
            }
        }

        private void DrawWorkflowsTab()
        {
            EditorGUILayout.LabelField("🔄 Workflow Orchestration", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            // Left panel - Workflow Templates
            EditorGUILayout.BeginVertical("box", GUILayout.Width(400));
            EditorGUILayout.LabelField("📋 Workflow Templates", EditorStyles.boldLabel);
            
            var workflows = TextureWorkflowOrchestrator.GetWorkflowTemplates();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            foreach (var workflow in workflows)
            {
                EditorGUILayout.BeginVertical("box");
                
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(workflow.name, EditorStyles.boldLabel);
                
                if (GUILayout.Button("▶️", GUILayout.Width(30)))
                {
                    ExecuteWorkflowAsync(workflow.name);
                }
                
                if (GUILayout.Button("⚙️", GUILayout.Width(30)))
                {
                    // Edit workflow (placeholder)
                    Debug.Log($"Editing workflow: {workflow.name}");
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.LabelField(workflow.description, EditorStyles.miniLabel);
                EditorGUILayout.LabelField($"Steps: {workflow.steps.Count} | Priority: {workflow.priority}", EditorStyles.miniLabel);
                
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("➕ Create New Workflow", GUILayout.Height(35), GUILayout.MinWidth(180)))
            {
                CreateNewWorkflow();
            }
            
            EditorGUILayout.EndVertical();
            
            // Right panel - Execution Status & Scheduling
            EditorGUILayout.BeginVertical();
            
            // Current Execution
            DrawCurrentWorkflowExecution();
            EditorGUILayout.Space();
            
            // Scheduled Tasks
            DrawScheduledTasks();
            EditorGUILayout.Space();
            
            // Automation Rules
            DrawAutomationRules();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        private void DrawCloudTab()
        {
            EditorGUILayout.LabelField("☁️ Cloud Integration", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            var config = TextureCloudIntegration.GetConfiguration();
            
            EditorGUILayout.BeginHorizontal();
            
            // Left panel - Configuration
            EditorGUILayout.BeginVertical("box", GUILayout.Width(400));
            EditorGUILayout.LabelField("⚙️ Cloud Configuration", EditorStyles.boldLabel);
            
            config.enableSync = EditorGUILayout.Toggle("Enable Cloud Sync", config.enableSync);
            config.enableCollaboration = EditorGUILayout.Toggle("Enable Collaboration", config.enableCollaboration);
            config.enableAnalytics = EditorGUILayout.Toggle("Enable Analytics", config.enableAnalytics);
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("Cloud Provider");
            var providers = new string[] { "aws", "gcp", "azure" };
            var currentIndex = Array.IndexOf(providers, config.activeProvider);
            var newIndex = EditorGUILayout.Popup(currentIndex, providers);
            if (newIndex >= 0) config.activeProvider = providers[newIndex];
            
            EditorGUILayout.Space();
            
            config.apiKey = EditorGUILayout.PasswordField("API Key", config.apiKey);
            config.secretKey = EditorGUILayout.PasswordField("Secret Key", config.secretKey);
            config.region = EditorGUILayout.TextField("Region", config.region);
            config.bucketName = EditorGUILayout.TextField("Bucket/Container", config.bucketName);
            
            EditorGUILayout.Space();
            
            config.syncIntervalMinutes = EditorGUILayout.IntSlider("Sync Interval (min)", config.syncIntervalMinutes, 5, 120);
            config.compressBeforeUpload = EditorGUILayout.Toggle("Compress Data", config.compressBeforeUpload);
            config.encryptData = EditorGUILayout.Toggle("Encrypt Data", config.encryptData);
            
            EditorGUILayout.Space();
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔗 Test Connection", GUILayout.Height(35), GUILayout.MinWidth(140)))
            {
                TestCloudConnectionAsync();
            }

            if (GUILayout.Button("💾 Save Config", GUILayout.Height(35), GUILayout.MinWidth(120)))
            {
                TextureCloudIntegration.ConfigureCloud(config);
                Debug.Log("Cloud configuration saved");
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            if (config.enableSync)
            {
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("🔄 Sync Now", GUILayout.Height(40), GUILayout.MinWidth(120)))
                {
                    SyncWithCloudAsync();
                }

                if (GUILayout.Button("📤 Upload Data", GUILayout.Height(40), GUILayout.MinWidth(130)))
                {
                    UploadOptimizationDataAsync();
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
            
            // Right panel - Cloud Status & Analytics
            EditorGUILayout.BeginVertical();
            
            DrawCloudStatus();
            EditorGUILayout.Space();
            
            DrawCloudAnalytics();
            EditorGUILayout.Space();
            
            DrawCloudFiles();
            
            EditorGUILayout.EndVertical();
            
            EditorGUILayout.EndHorizontal();
        }

        private void DrawCurrentWorkflowExecution()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🚀 Current Execution", EditorStyles.boldLabel);
            
            var currentExecution = TextureWorkflowOrchestrator.GetCurrentExecution();
            
            if (currentExecution != null && currentExecution.isRunning)
            {
                EditorGUILayout.LabelField($"Workflow: {currentExecution.template.name}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Step: {currentExecution.currentStepIndex + 1}/{currentExecution.template.steps.Count}");
                
                if (currentExecution.currentStepIndex < currentExecution.template.steps.Count)
                {
                    var currentStep = currentExecution.template.steps[currentExecution.currentStepIndex];
                    EditorGUILayout.LabelField($"Current: {currentStep.stepName}");
                }
                
                var progress = (float)currentExecution.currentStepIndex / currentExecution.template.steps.Count;
                EditorGUI.ProgressBar(EditorGUILayout.GetControlRect(), progress, $"{progress:P0}");
                
                var elapsed = DateTime.Now - currentExecution.startTime;
                EditorGUILayout.LabelField($"Elapsed: {elapsed:mm\\:ss}");
            }
            else if (currentExecution != null && !currentExecution.isRunning)
            {
                var statusIcon = currentExecution.wasSuccessful ? "✅" : "❌";
                EditorGUILayout.LabelField($"{statusIcon} Last execution: {currentExecution.template.name}");
                EditorGUILayout.LabelField($"Duration: {(currentExecution.endTime - currentExecution.startTime):mm\\:ss}");
                
                if (!currentExecution.wasSuccessful && !string.IsNullOrEmpty(currentExecution.errorMessage))
                {
                    EditorGUILayout.HelpBox(currentExecution.errorMessage, MessageType.Error);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No workflow currently executing", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawScheduledTasks()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("⏰ Scheduled Tasks", EditorStyles.boldLabel);
            
            var scheduledTasks = TextureWorkflowOrchestrator.GetScheduledTasks();
            
            if (scheduledTasks.Count > 0)
            {
                foreach (var task in scheduledTasks.Where(t => t.isActive).Take(5))
                {
                    EditorGUILayout.BeginHorizontal("box");
                    
                    EditorGUILayout.LabelField(task.workflowName, GUILayout.Width(150));
                    EditorGUILayout.LabelField($"Next: {task.nextRun:MM/dd HH:mm}", GUILayout.Width(100));
                    
                    if (task.isRecurring)
                    {
                        EditorGUILayout.LabelField($"Every {task.interval.TotalMinutes:F0}m", GUILayout.Width(80));
                    }
                    
                    if (GUILayout.Button("⏸️", GUILayout.Width(25)))
                    {
                        task.isActive = false;
                    }
                    
                    EditorGUILayout.EndHorizontal();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No scheduled tasks", MessageType.Info);
            }
            
            if (GUILayout.Button("➕ Schedule Workflow", GUILayout.Height(35), GUILayout.MinWidth(170)))
            {
                // Schedule workflow dialog (placeholder)
                Debug.Log("Schedule workflow dialog");
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawAutomationRules()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("🤖 Automation Rules", EditorStyles.boldLabel);
            
            var automationRules = TextureWorkflowOrchestrator.GetAutomationRules();
            
            if (automationRules.Count > 0)
            {
                foreach (var rule in automationRules.Where(r => r.isActive).Take(3))
                {
                    EditorGUILayout.BeginHorizontal("box");
                    
                    EditorGUILayout.LabelField(rule.name, GUILayout.Width(120));
                    EditorGUILayout.LabelField(rule.trigger.ToString(), GUILayout.Width(100));
                    EditorGUILayout.LabelField($"→ {rule.workflowToExecute}", GUILayout.Width(120));
                    
                    if (GUILayout.Button("⏸️", GUILayout.Width(25)))
                    {
                        rule.isActive = false;
                    }
                    
                    EditorGUILayout.EndHorizontal();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No automation rules", MessageType.Info);
            }
            
            if (GUILayout.Button("➕ Add Automation Rule", GUILayout.Height(35), GUILayout.MinWidth(180)))
            {
                // Add automation rule dialog (placeholder)
                Debug.Log("Add automation rule dialog");
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawCloudStatus()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📊 Cloud Status", EditorStyles.boldLabel);
            
            var config = TextureCloudIntegration.GetConfiguration();
            
            if (config.enableSync)
            {
                EditorGUILayout.LabelField($"Provider: {config.activeProvider.ToUpper()}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Region: {config.region}");
                EditorGUILayout.LabelField($"Sync Interval: {config.syncIntervalMinutes} minutes");
                
                // Connection status (placeholder)
                var statusColor = "green";
                var statusText = "Connected";
                EditorGUILayout.LabelField($"<color={statusColor}>● {statusText}</color>", 
                    new GUIStyle(EditorStyles.label) { richText = true });
            }
            else
            {
                EditorGUILayout.HelpBox("Cloud sync is disabled", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawCloudAnalytics()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📈 Cloud Analytics", EditorStyles.boldLabel);
            
            // Placeholder analytics data
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Storage Used: 500 MB", GUILayout.Width(150));
            EditorGUILayout.LabelField("Files: 150", GUILayout.Width(80));
            EditorGUILayout.LabelField("Uploads: 890");
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Downloads: 1,250", GUILayout.Width(150));
            EditorGUILayout.LabelField("Cost: $12.50", GUILayout.Width(80));
            EditorGUILayout.LabelField("Savings: 65%");
            EditorGUILayout.EndHorizontal();
            
            if (GUILayout.Button("📊 View Detailed Analytics", GUILayout.Height(35), GUILayout.MinWidth(200)))
            {
                GetCloudAnalyticsAsync();
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawCloudFiles()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📁 Cloud Files", EditorStyles.boldLabel);
            
            // Placeholder file list
            var files = new[]
            {
                "optimization_data.json (1.2 KB)",
                "monthly_report.html (2.5 KB)",
                "texture_backup.zip (10 MB)"
            };
            
            foreach (var file in files)
            {
                EditorGUILayout.BeginHorizontal("box");
                EditorGUILayout.LabelField(file);
                
                if (GUILayout.Button("📥", GUILayout.Width(25)))
                {
                    Debug.Log($"Downloading: {file}");
                }
                
                if (GUILayout.Button("🗑️", GUILayout.Width(25)))
                {
                    Debug.Log($"Deleting: {file}");
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            if (GUILayout.Button("🔄 Refresh File List", GUILayout.Height(35), GUILayout.MinWidth(160)))
            {
                ListCloudFilesAsync();
            }
            
            EditorGUILayout.EndVertical();
        }

        // Async method wrappers for UI
        private async void ExecuteWorkflowAsync(string workflowName)
        {
            Debug.Log($"🚀 Executing workflow: {workflowName}");
            var success = await TextureWorkflowOrchestrator.ExecuteWorkflowAsync(workflowName);
            Debug.Log(success ? "✅ Workflow completed" : "❌ Workflow failed");
            Repaint();
        }

        private async void TestCloudConnectionAsync()
        {
            Debug.Log("🔗 Testing cloud connection...");
            var success = await TextureCloudIntegration.TestConnectionAsync();
            var message = success ? "✅ Connection successful" : "❌ Connection failed";
            EditorUtility.DisplayDialog("Cloud Connection", message, "OK");
        }

        private async void SyncWithCloudAsync()
        {
            Debug.Log("🔄 Starting cloud sync...");
            var success = await TextureCloudIntegration.SyncWithCloudAsync();
            var message = success ? "✅ Sync completed" : "❌ Sync failed";
            EditorUtility.DisplayDialog("Cloud Sync", message, "OK");
        }

        private async void UploadOptimizationDataAsync()
        {
            Debug.Log("📤 Uploading optimization data...");
            var data = TextureOptimizationProfiles.GetOptimizationAnalytics();
            var success = await TextureCloudIntegration.UploadOptimizationDataAsync(data);
            var message = success ? "✅ Upload completed" : "❌ Upload failed";
            EditorUtility.DisplayDialog("Cloud Upload", message, "OK");
        }

        private async void GetCloudAnalyticsAsync()
        {
            Debug.Log("📊 Fetching cloud analytics...");
            var analytics = await TextureCloudIntegration.GetCloudAnalyticsAsync();
            if (analytics != null)
            {
                var report = $"Storage: {FormatBytes(analytics.totalStorageUsed)}\n";
                report += $"Files: {analytics.totalFiles}\n";
                report += $"Downloads: {analytics.totalDownloads}\n";
                report += $"Uploads: {analytics.totalUploads}";
                EditorUtility.DisplayDialog("Cloud Analytics", report, "OK");
            }
        }

        private async void ListCloudFilesAsync()
        {
            Debug.Log("📁 Listing cloud files...");
            var files = await TextureCloudIntegration.ListCloudFilesAsync();
            Debug.Log($"Found {files.Count} files in cloud storage");
        }

        private void CreateNewWorkflow()
        {
            var workflowName = EditorUtility.SaveFilePanel("Create Workflow", "", "new_workflow", "");
            if (!string.IsNullOrEmpty(workflowName))
            {
                var workflow = TextureWorkflowOrchestrator.CreateWorkflowTemplate(
                    Path.GetFileNameWithoutExtension(workflowName),
                    "Custom workflow created by user"
                );
                Debug.Log($"✅ Created workflow: {workflow.name}");
            }
        }
    }
}