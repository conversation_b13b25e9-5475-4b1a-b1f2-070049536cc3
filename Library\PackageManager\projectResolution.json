{"context": {"projectPath": "D:/My Project/Test Project/Packages", "unityVersion": "6000.0.30f1"}, "inputs": ["D:\\My Project\\Test Project\\Packages\\manifest.json", "D:\\My Project\\Test Project\\Packages\\packages-lock.json"], "outputs": {"com.unity.collab-proxy@2.8.2": {"name": "com.unity.collab-proxy", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.collab-proxy", "fingerprint": "c854d1f7d97fbe1905f3e3591ded6fe77d96e654", "editorCompatibility": "2021.3.0f1", "version": "2.8.2", "source": "registry"}, "com.unity.feature.development@1.0.2": {"name": "com.unity.feature.development", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.feature.development", "fingerprint": "767aadbc6eb72681a4ca807c8fa248e0230a0cef", "version": "1.0.2", "source": "builtin"}, "com.unity.inputsystem@1.12.0": {"name": "com.unity.inputsystem", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.inputsystem", "fingerprint": "920b46832575a5beecb201e0a4570b4984b7ec34", "editorCompatibility": "2019.4.0a1", "version": "1.12.0", "source": "registry"}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.multiplayer.center", "fingerprint": "f502d8ac613fa076192423e73892fbd89eb4049b", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin"}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.timeline", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry"}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.ugui", "fingerprint": "fd5da3b4bec8d043b01c30177bd62059c5fa36d0", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin"}, "com.unity.visualscripting@1.9.5": {"name": "com.unity.visualscripting", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.visualscripting", "fingerprint": "1b53f46e931bea668e53f1feb0ac9138170c9455", "editorCompatibility": "2021.3.0a1", "version": "1.9.5", "source": "registry"}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.director", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.video", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin"}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin"}, "com.unity.ide.visualstudio@2.0.22": {"name": "com.unity.ide.visualstudio", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.ide.visualstudio", "fingerprint": "8140e851d83e922ca2021b04a89519de94ebe38c", "editorCompatibility": "2019.4.25f1", "version": "2.0.22", "source": "registry"}, "com.unity.ide.rider@3.0.31": {"name": "com.unity.ide.rider", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.ide.rider", "fingerprint": "7921be93db40ec070fcb01ed82d1c3df1bbdddcd", "editorCompatibility": "2019.2.6f1", "version": "3.0.31", "source": "registry"}, "com.unity.editorcoroutines@1.0.0": {"name": "com.unity.editorcoroutines", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.editorcoroutines", "fingerprint": "7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca", "editorCompatibility": "2018.1.0a1", "version": "1.0.0", "source": "registry"}, "com.unity.performance.profile-analyzer@1.2.2": {"name": "com.unity.performance.profile-analyzer", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.performance.profile-analyzer", "fingerprint": "fe7acfd2717a98ded6ddc33be6b2ebe4459a2add", "editorCompatibility": "2020.3.0a1", "version": "1.2.2", "source": "registry"}, "com.unity.test-framework@1.4.5": {"name": "com.unity.test-framework", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.test-framework", "fingerprint": "0a21eb82d95cd331643a1e0ce4e8e9a5f18954c8", "editorCompatibility": "2019.4.0a10", "version": "1.4.5", "source": "registry"}, "com.unity.testtools.codecoverage@1.2.6": {"name": "com.unity.testtools.codecoverage", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.testtools.codecoverage", "fingerprint": "205a02cbcb39584f20b51c49b853047aceb3a3a7", "editorCompatibility": "2019.3.0a1", "version": "1.2.6", "source": "registry"}, "com.unity.settings-manager@2.0.1": {"name": "com.unity.settings-manager", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.settings-manager", "fingerprint": "56a930affa1e4462d3595b5c19a1c2a4bf2015d2", "editorCompatibility": "2018.4.0a1", "version": "2.0.1", "source": "registry"}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "resolvedPath": "D:\\My Project\\Test Project\\Library\\PackageCache\\com.unity.ext.nunit", "fingerprint": "60ef35ffd3cd5e2f5c8887a4a4ca6148854cd092", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "registry"}}}