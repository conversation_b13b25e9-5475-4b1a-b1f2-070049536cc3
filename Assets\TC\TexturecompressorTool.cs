using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Audio compression format options that match Unity's AudioCompressionFormat
/// </summary>
public enum AudioCompressionFormat
{
    PCM = 0,
    ADPCM = 1,
    Vorbis = 2,
    MP3 = 3
}

/// <summary>
/// Tool for automatically compressing textures, sprites, meshes, and audio clips used in the scene hierarchy.
/// This component can be attached to a GameObject to provide runtime compression functionality.
/// </summary>
public class TexturecompressorTool : MonoBehaviour
{
    [System.Serializable]
    public class TextureInfo
    {
        public Texture2D texture;
        public string path;
        public int originalSize;
        public int compressedSize;
        public bool processed;
        public bool isSprite;
        public bool isUI;
    }

    [System.Serializable]
    public class ModelInfo
    {
        public GameObject modelPrefab; // Reference to the full model prefab/FBX
        public string path;
        public int originalVertexCount;
        public int compressedVertexCount;
        public float originalSize;
        public float compressedSize;
        public bool processed;
        public int compressionLevel = 0; // 0=Off, 1=Low, 2=Medium, 3=High
        public bool optimizeMesh = true;
        public bool generateColliders = false;
        public bool weldVertices = true;
        public bool preserveBorders = true;
        public bool preserveUVSeams = true;
        public bool preserveSurfaceDetail = true;
        public bool importBlendshapes = false;
        public bool generateLightmapUVs = false;
    }

    [System.Serializable]
    public class AudioInfo
    {
        public AudioClip audioClip;
        public string path;
        public int originalSize;
        public int compressedSize;
        public bool processed;
        public bool is3D;
        public bool isMusic;
    }

    [Header("Texture Compression Settings")]
    [Tooltip("Maximum texture size for compressed textures")]
    public int maxTextureSize = 1024;

    [Tooltip("Compression quality (0-100)")]
    [Range(0, 100)]
    public int compressionQuality = 50;

    [Tooltip("Use crunch compression when available")]
    public bool useCrunchCompression = true;

    [Header("Mesh Compression Settings")]
    [Tooltip("Mesh compression level (0=Off, 1=Low, 2=Medium, 3=High)")]
    [Range(0, 3)]
    public int meshCompressionLevel = 1;

    [Tooltip("Optimize mesh")]
    public bool optimizeMesh = true;

    [Tooltip("Generate colliders")]
    public bool generateColliders = false;

    [Tooltip("Weld vertices")]
    public bool weldVertices = true;

    [Header("Audio Compression Settings")]
    [Tooltip("Audio compression format (PCM, ADPCM, Vorbis, MP3)")]
    public AudioCompressionFormat audioCompressionFormat = AudioCompressionFormat.Vorbis;

    [Tooltip("Audio quality (0-100)")]
    [Range(0, 100)]
    public int audioQuality = 70;

    [Tooltip("Force audio to mono")]
    public bool forceToMono = false;

    [Tooltip("Normalize audio")]
    public bool normalizeAudio = true;

    [Tooltip("Load audio in background")]
    public bool loadInBackground = true;

    [Tooltip("Preload audio data")]
    public bool preloadAudioData = true;

    [Header("Scan Settings")]
    [Tooltip("Include sprites from SpriteRenderers")]
    public bool includeSprites = true;

    [Tooltip("Include textures from UI elements")]
    public bool includeUI = true;

    [Tooltip("Include materials and their textures")]
    public bool includeMaterials = true;

    [Tooltip("Include FBX models used in the scene")]
    public bool includeModels = true;

    [Tooltip("Include audio clips used in the scene")]
    public bool includeAudio = true;

    [Header("Texture Results")]
    [SerializeField] private List<TextureInfo> foundTextures = new List<TextureInfo>();
    [SerializeField] private int totalOriginalSize = 0;
    [SerializeField] private int totalCompressedSize = 0;

    [Header("Model Results")]
    [SerializeField] private List<ModelInfo> foundModels = new List<ModelInfo>();
    [SerializeField] private float totalOriginalModelSize = 0;
    [SerializeField] private float totalCompressedModelSize = 0;

    [Header("Audio Results")]
    [SerializeField] private List<AudioInfo> foundAudioClips = new List<AudioInfo>();
    [SerializeField] private int totalOriginalAudioSize = 0;
    [SerializeField] private int totalCompressedAudioSize = 0;

    /// <summary>
    /// Scans the scene for textures, sprites, meshes, and audio clips used in the hierarchy
    /// </summary>
    public void ScanScene()
    {
        foundTextures.Clear();
        totalOriginalSize = 0;
        totalCompressedSize = 0;

        foundModels.Clear();
        totalOriginalModelSize = 0;
        totalCompressedModelSize = 0;

        foundAudioClips.Clear();
        totalOriginalAudioSize = 0;
        totalCompressedAudioSize = 0;

        // This is just a placeholder for the runtime component
        // The actual scanning is implemented in the Editor script
        Debug.Log("Scanning scene for textures, sprites, meshes, and audio clips...");
    }

    /// <summary>
    /// Compresses all found textures, meshes, and audio clips using the current settings
    /// </summary>
    public void CompressAll()
    {
        // This is just a placeholder for the runtime component
        // The actual compression is implemented in the Editor script
        Debug.Log("Compressing all textures, meshes, and audio clips...");
    }

    #region Texture Methods

    /// <summary>
    /// Gets the list of found textures
    /// </summary>
    public List<TextureInfo> GetFoundTextures()
    {
        return foundTextures;
    }

    /// <summary>
    /// Adds a texture to the list of found textures
    /// </summary>
    public void AddTexture(TextureInfo textureInfo)
    {
        if (!foundTextures.Exists(t => t.texture == textureInfo.texture))
        {
            foundTextures.Add(textureInfo);
            totalOriginalSize += textureInfo.originalSize;
        }
    }

    /// <summary>
    /// Updates the compressed size of a texture
    /// </summary>
    public void UpdateCompressedSize(Texture2D texture, int compressedSize)
    {
        var textureInfo = foundTextures.Find(t => t.texture == texture);
        if (textureInfo != null)
        {
            // First, remove any previous compressed size if the texture was already processed
            if (textureInfo.processed)
            {
                totalCompressedSize -= textureInfo.compressedSize;
            }

            // Update the texture info
            textureInfo.compressedSize = compressedSize;
            textureInfo.processed = true;

            // Add the new compressed size to the total
            totalCompressedSize += compressedSize;
        }
    }

    /// <summary>
    /// Gets the total original size of all found textures
    /// </summary>
    public int GetTotalOriginalSize()
    {
        return totalOriginalSize;
    }

    /// <summary>
    /// Gets the total compressed size of all processed textures
    /// </summary>
    public int GetTotalCompressedSize()
    {
        return totalCompressedSize;
    }

    #endregion

    #region Model Methods

    /// <summary>
    /// Gets the list of found models
    /// </summary>
    public List<ModelInfo> GetFoundModels()
    {
        return foundModels;
    }

    /// <summary>
    /// Adds a model to the list of found models
    /// </summary>
    public void AddModel(ModelInfo modelInfo)
    {
        if (!foundModels.Exists(m => m.path == modelInfo.path))
        {
            foundModels.Add(modelInfo);
            totalOriginalModelSize += modelInfo.originalSize;
        }
    }

    /// <summary>
    /// Updates the compressed size of a model
    /// </summary>
    public void UpdateCompressedModelSize(string modelPath, float compressedSize, int compressedVertexCount)
    {
        var modelInfo = foundModels.Find(m => m.path == modelPath);
        if (modelInfo != null)
        {
            // First, remove any previous compressed size if the model was already processed
            if (modelInfo.processed)
            {
                totalCompressedModelSize -= modelInfo.compressedSize;
            }

            // Update the model info
            modelInfo.compressedSize = compressedSize;
            modelInfo.compressedVertexCount = compressedVertexCount;
            modelInfo.processed = true;

            // Add the new compressed size to the total
            totalCompressedModelSize += compressedSize;
        }
    }

    /// <summary>
    /// Gets the total original size of all found models
    /// </summary>
    public float GetTotalOriginalModelSize()
    {
        return totalOriginalModelSize;
    }

    /// <summary>
    /// Gets the total compressed size of all processed models
    /// </summary>
    public float GetTotalCompressedModelSize()
    {
        return totalCompressedModelSize;
    }

    #endregion

    #region Audio Methods

    /// <summary>
    /// Gets the list of found audio clips
    /// </summary>
    public List<AudioInfo> GetFoundAudioClips()
    {
        return foundAudioClips;
    }

    /// <summary>
    /// Adds an audio clip to the list of found audio clips
    /// </summary>
    public void AddAudioClip(AudioInfo audioInfo)
    {
        if (!foundAudioClips.Exists(a => a.audioClip == audioInfo.audioClip))
        {
            foundAudioClips.Add(audioInfo);
            totalOriginalAudioSize += audioInfo.originalSize;
        }
    }

    /// <summary>
    /// Updates the compressed size of an audio clip
    /// </summary>
    public void UpdateCompressedAudioSize(AudioClip audioClip, int compressedSize)
    {
        var audioInfo = foundAudioClips.Find(a => a.audioClip == audioClip);
        if (audioInfo != null)
        {
            // First, remove any previous compressed size if the audio clip was already processed
            if (audioInfo.processed)
            {
                totalCompressedAudioSize -= audioInfo.compressedSize;
            }

            // Update the audio info
            audioInfo.compressedSize = compressedSize;
            audioInfo.processed = true;

            // Add the new compressed size to the total
            totalCompressedAudioSize += compressedSize;
        }
    }

    /// <summary>
    /// Gets the total original size of all found audio clips
    /// </summary>
    public int GetTotalOriginalAudioSize()
    {
        return totalOriginalAudioSize;
    }

    /// <summary>
    /// Gets the total compressed size of all processed audio clips
    /// </summary>
    public int GetTotalCompressedAudioSize()
    {
        return totalCompressedAudioSize;
    }

    #endregion
}
