using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Test script to verify all texture formats are working correctly
/// </summary>
public class TextureFormatTester : EditorWindow
{
    private Vector2 scrollPosition;
    private Texture2D testTexture;
    private List<FormatTestResult> testResults = new List<FormatTestResult>();
    
    [System.Serializable]
    public class FormatTestResult
    {
        public TextureImporterFormat format;
        public string formatName;
        public bool isSupported;
        public long originalSize;
        public long compressedSize;
        public float compressionRatio;
        public string errorMessage;
        public bool hasAlpha;
        public string platformSupport;
    }
    
    [MenuItem("Tools/Texture Compressor/Format Tester")]
    public static void ShowWindow()
    {
        GetWindow<TextureFormatTester>("Texture Format Tester");
    }
    
    private void OnGUI()
    {
        EditorGUILayout.LabelField("🧪 Texture Format Tester", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Test texture selection
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Test Texture:", GUILayout.Width(100));
        testTexture = (Texture2D)EditorGUILayout.ObjectField(testTexture, typeof(Texture2D), false);
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        // Test buttons
        EditorGUILayout.BeginHorizontal();
        
        EditorGUI.BeginDisabledGroup(testTexture == null);
        if (GUILayout.Button("🔍 Test All Formats", GUILayout.Height(30)))
        {
            TestAllFormats();
        }
        EditorGUI.EndDisabledGroup();
        
        if (GUILayout.Button("📋 Export Results", GUILayout.Height(30)))
        {
            ExportResults();
        }
        
        if (GUILayout.Button("🗑️ Clear Results", GUILayout.Height(30)))
        {
            testResults.Clear();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        // Results
        if (testResults.Count > 0)
        {
            EditorGUILayout.LabelField($"📊 Test Results ({testResults.Count} formats tested)", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var result in testResults.OrderBy(r => r.compressionRatio))
            {
                DrawFormatResult(result);
            }
            
            EditorGUILayout.EndScrollView();
        }
    }
    
    private void DrawFormatResult(FormatTestResult result)
    {
        EditorGUILayout.BeginVertical("box");
        
        // Header
        EditorGUILayout.BeginHorizontal();
        
        string statusIcon = result.isSupported ? "✅" : "❌";
        EditorGUILayout.LabelField($"{statusIcon} {result.formatName}", EditorStyles.boldLabel);
        
        if (result.isSupported && result.compressedSize > 0)
        {
            EditorGUILayout.LabelField($"Ratio: {result.compressionRatio:F2}x", GUILayout.Width(80));
        }
        
        EditorGUILayout.EndHorizontal();
        
        // Details
        if (result.isSupported)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Original: {FormatBytes(result.originalSize)}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"Compressed: {FormatBytes(result.compressedSize)}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Alpha: {(result.hasAlpha ? "Yes" : "No")}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"Platform: {result.platformSupport}", EditorStyles.miniLabel);
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.LabelField($"Error: {result.errorMessage}", EditorStyles.miniLabel);
        }
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }
    
    private void TestAllFormats()
    {
        if (testTexture == null) return;
        
        testResults.Clear();
        
        // Get all available texture formats
        var allFormats = System.Enum.GetValues(typeof(TextureImporterFormat)).Cast<TextureImporterFormat>().ToArray();
        
        string texturePath = AssetDatabase.GetAssetPath(testTexture);
        TextureImporter originalImporter = AssetImporter.GetAtPath(texturePath) as TextureImporter;
        
        if (originalImporter == null)
        {
            EditorUtility.DisplayDialog("Error", "Could not get texture importer for the selected texture.", "OK");
            return;
        }
        
        // Store original settings
        var originalFormat = originalImporter.GetDefaultPlatformTextureSettings().format;
        var originalSize = GetTextureMemorySize(testTexture);
        
        EditorUtility.DisplayProgressBar("Testing Formats", "Preparing...", 0f);
        
        try
        {
            for (int i = 0; i < allFormats.Length; i++)
            {
                var format = allFormats[i];
                
                EditorUtility.DisplayProgressBar("Testing Formats", $"Testing {format}...", (float)i / allFormats.Length);
                
                var result = TestFormat(format, originalImporter, originalSize);
                testResults.Add(result);
            }
        }
        finally
        {
            // Restore original format
            var platformSettings = originalImporter.GetDefaultPlatformTextureSettings();
            platformSettings.format = originalFormat;
            originalImporter.SetPlatformTextureSettings(platformSettings);
            originalImporter.SaveAndReimport();
            
            EditorUtility.ClearProgressBar();
        }
        
        Debug.Log($"Format testing completed. Tested {testResults.Count} formats.");
    }
    
    private FormatTestResult TestFormat(TextureImporterFormat format, TextureImporter importer, long originalSize)
    {
        var result = new FormatTestResult
        {
            format = format,
            formatName = format.ToString(),
            originalSize = originalSize,
            hasAlpha = importer.DoesSourceTextureHaveAlpha()
        };
        
        try
        {
            // Try to apply the format
            var platformSettings = importer.GetDefaultPlatformTextureSettings();
            platformSettings.format = format;
            importer.SetPlatformTextureSettings(platformSettings);
            importer.SaveAndReimport();
            
            // Check if it was applied successfully
            var appliedSettings = importer.GetDefaultPlatformTextureSettings();
            if (appliedSettings.format == format)
            {
                result.isSupported = true;
                result.compressedSize = GetTextureMemorySize(testTexture);
                result.compressionRatio = result.originalSize > 0 ? (float)result.originalSize / result.compressedSize : 1f;
                result.platformSupport = GetPlatformSupportInfo(format);
            }
            else
            {
                result.isSupported = false;
                result.errorMessage = "Format not supported on current platform";
            }
        }
        catch (System.Exception e)
        {
            result.isSupported = false;
            result.errorMessage = e.Message;
        }
        
        return result;
    }
    
    private long GetTextureMemorySize(Texture2D texture)
    {
        if (texture == null) return 0;
        
        // Use Unity's built-in profiler to get accurate memory usage
        return UnityEngine.Profiling.Profiler.GetRuntimeMemorySizeLong(texture);
    }
    
    private string GetPlatformSupportInfo(TextureImporterFormat format)
    {
        // This would be the same as the method in TextureCompressorWindow
        switch (format)
        {
            case TextureImporterFormat.DXT1:
            case TextureImporterFormat.DXT5:
                return "PC, Xbox, WebGL";
            case TextureImporterFormat.ETC2_RGB4:
            case TextureImporterFormat.ETC2_RGBA8:
                return "Android, WebGL";
            case TextureImporterFormat.ASTC_4x4:
            case TextureImporterFormat.ASTC_6x6:
                return "iOS, Android (modern)";
            default:
                return "Various";
        }
    }
    
    private void ExportResults()
    {
        if (testResults.Count == 0)
        {
            EditorUtility.DisplayDialog("No Results", "No test results to export.", "OK");
            return;
        }
        
        string path = EditorUtility.SaveFilePanel("Export Format Test Results", "", "texture_format_test_results", "csv");
        if (string.IsNullOrEmpty(path)) return;
        
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("Format,Supported,Original Size,Compressed Size,Compression Ratio,Has Alpha,Platform Support,Error");
        
        foreach (var result in testResults)
        {
            csv.AppendLine($"{result.formatName},{result.isSupported},{result.originalSize},{result.compressedSize},{result.compressionRatio:F2},{result.hasAlpha},{result.platformSupport},{result.errorMessage}");
        }
        
        System.IO.File.WriteAllText(path, csv.ToString());
        EditorUtility.DisplayDialog("Export Complete", $"Results exported to {path}", "OK");
    }
    
    private string FormatBytes(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024f:F1} KB";
        if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024f * 1024f):F1} MB";
        return $"{bytes / (1024f * 1024f * 1024f):F1} GB";
    }
}
