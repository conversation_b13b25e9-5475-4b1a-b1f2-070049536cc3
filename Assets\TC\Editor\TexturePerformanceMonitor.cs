using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Diagnostics;
using System;
using System.Linq;

namespace TextureCompressorTool
{
    /// <summary>
    /// Advanced performance monitoring and optimization tracking system
    /// </summary>
    public static class TexturePerformanceMonitor
    {
        private static Dictionary<string, PerformanceMetrics> performanceHistory = new Dictionary<string, PerformanceMetrics>();
        private static List<OptimizationSession> sessions = new List<OptimizationSession>();
        private static Stopwatch currentSessionTimer = new Stopwatch();
        private static OptimizationSession currentSession;

        [System.Serializable]
        public class PerformanceMetrics
        {
            public string operationName;
            public long totalExecutionTime; // milliseconds
            public int executionCount;
            public long averageExecutionTime;
            public long minExecutionTime = long.MaxValue;
            public long maxExecutionTime = 0;
            public long totalMemoryUsed; // bytes
            public int texturesProcessed;
            public DateTime lastExecution;
            public List<long> recentExecutionTimes = new List<long>();
        }

        [System.Serializable]
        public class OptimizationSession
        {
            public DateTime startTime;
            public DateTime endTime;
            public int texturesProcessed;
            public long totalOriginalSize;
            public long totalCompressedSize;
            public long totalTimeTaken; // milliseconds
            public float averageCompressionRatio;
            public Dictionary<TextureImporterFormat, int> formatsUsed = new Dictionary<TextureImporterFormat, int>();
            public List<string> operationsPerformed = new List<string>();
            public string sessionNotes;
            public bool wasSuccessful = true;
        }

        public class PerformanceProfiler : IDisposable
        {
            private string operationName;
            private Stopwatch stopwatch;
            private long initialMemory;

            public PerformanceProfiler(string operation)
            {
                operationName = operation;
                stopwatch = Stopwatch.StartNew();
                initialMemory = GC.GetTotalMemory(false);
            }

            public void Dispose()
            {
                stopwatch.Stop();
                var finalMemory = GC.GetTotalMemory(false);
                var memoryUsed = Math.Max(0, finalMemory - initialMemory);
                
                RecordPerformance(operationName, stopwatch.ElapsedMilliseconds, memoryUsed);
            }
        }

        /// <summary>
        /// Start a new optimization session
        /// </summary>
        public static void StartSession(string notes = "")
        {
            currentSession = new OptimizationSession
            {
                startTime = DateTime.Now,
                sessionNotes = notes
            };
            
            currentSessionTimer.Restart();
            UnityEngine.Debug.Log($"Performance monitoring session started: {notes}");
        }

        /// <summary>
        /// End the current optimization session
        /// </summary>
        public static void EndSession()
        {
            if (currentSession == null) return;

            currentSessionTimer.Stop();
            currentSession.endTime = DateTime.Now;
            currentSession.totalTimeTaken = currentSessionTimer.ElapsedMilliseconds;
            
            if (currentSession.texturesProcessed > 0)
            {
                currentSession.averageCompressionRatio = currentSession.totalCompressedSize > 0 ? 
                    (float)currentSession.totalCompressedSize / currentSession.totalOriginalSize : 1f;
            }

            sessions.Add(currentSession);
            
            UnityEngine.Debug.Log($"Performance session completed: {currentSession.texturesProcessed} textures processed in {currentSession.totalTimeTaken}ms");
            
            currentSession = null;
            SavePerformanceData();
        }

        /// <summary>
        /// Record texture processing in current session
        /// </summary>
        public static void RecordTextureProcessing(Texture2D texture, long originalSize, long compressedSize, TextureImporterFormat format)
        {
            if (currentSession == null) return;

            currentSession.texturesProcessed++;
            currentSession.totalOriginalSize += originalSize;
            currentSession.totalCompressedSize += compressedSize;

            if (!currentSession.formatsUsed.ContainsKey(format))
                currentSession.formatsUsed[format] = 0;
            currentSession.formatsUsed[format]++;
        }

        /// <summary>
        /// Record operation in current session
        /// </summary>
        public static void RecordOperation(string operation)
        {
            if (currentSession == null) return;
            currentSession.operationsPerformed.Add($"{DateTime.Now:HH:mm:ss} - {operation}");
        }

        /// <summary>
        /// Create a performance profiler for an operation
        /// </summary>
        public static PerformanceProfiler ProfileOperation(string operationName)
        {
            return new PerformanceProfiler(operationName);
        }

        /// <summary>
        /// Record performance metrics for an operation
        /// </summary>
        public static void RecordPerformance(string operationName, long executionTimeMs, long memoryUsed = 0, int texturesProcessed = 0)
        {
            if (!performanceHistory.ContainsKey(operationName))
            {
                performanceHistory[operationName] = new PerformanceMetrics
                {
                    operationName = operationName
                };
            }

            var metrics = performanceHistory[operationName];
            metrics.executionCount++;
            metrics.totalExecutionTime += executionTimeMs;
            metrics.averageExecutionTime = metrics.totalExecutionTime / metrics.executionCount;
            metrics.minExecutionTime = Math.Min(metrics.minExecutionTime, executionTimeMs);
            metrics.maxExecutionTime = Math.Max(metrics.maxExecutionTime, executionTimeMs);
            metrics.totalMemoryUsed += memoryUsed;
            metrics.texturesProcessed += texturesProcessed;
            metrics.lastExecution = DateTime.Now;

            // Keep recent execution times for trend analysis
            metrics.recentExecutionTimes.Add(executionTimeMs);
            if (metrics.recentExecutionTimes.Count > 10)
            {
                metrics.recentExecutionTimes.RemoveAt(0);
            }
        }

        /// <summary>
        /// Get performance metrics for an operation
        /// </summary>
        public static PerformanceMetrics GetMetrics(string operationName)
        {
            return performanceHistory.ContainsKey(operationName) ? performanceHistory[operationName] : null;
        }

        /// <summary>
        /// Get all performance metrics
        /// </summary>
        public static Dictionary<string, PerformanceMetrics> GetAllMetrics()
        {
            return new Dictionary<string, PerformanceMetrics>(performanceHistory);
        }

        /// <summary>
        /// Get optimization sessions
        /// </summary>
        public static List<OptimizationSession> GetSessions()
        {
            return new List<OptimizationSession>(sessions);
        }

        /// <summary>
        /// Get performance analytics
        /// </summary>
        public static Dictionary<string, object> GetPerformanceAnalytics()
        {
            var analytics = new Dictionary<string, object>();

            if (sessions.Count > 0)
            {
                var totalSessions = sessions.Count;
                var totalTexturesProcessed = sessions.Sum(s => s.texturesProcessed);
                var totalTimeTaken = sessions.Sum(s => s.totalTimeTaken);
                var totalOriginalSize = sessions.Sum(s => s.totalOriginalSize);
                var totalCompressedSize = sessions.Sum(s => s.totalCompressedSize);
                var averageCompressionRatio = totalOriginalSize > 0 ? (float)totalCompressedSize / totalOriginalSize : 1f;
                var successfulSessions = sessions.Count(s => s.wasSuccessful);

                analytics["totalSessions"] = totalSessions;
                analytics["totalTexturesProcessed"] = totalTexturesProcessed;
                analytics["totalTimeTaken"] = totalTimeTaken;
                analytics["averageTexturesPerSession"] = totalSessions > 0 ? (float)totalTexturesProcessed / totalSessions : 0f;
                analytics["averageTimePerTexture"] = totalTexturesProcessed > 0 ? (float)totalTimeTaken / totalTexturesProcessed : 0f;
                analytics["totalSpaceSaved"] = totalOriginalSize - totalCompressedSize;
                analytics["averageCompressionRatio"] = averageCompressionRatio;
                analytics["successRate"] = totalSessions > 0 ? (float)successfulSessions / totalSessions : 0f;

                // Most used formats
                var allFormats = new Dictionary<TextureImporterFormat, int>();
                foreach (var session in sessions)
                {
                    foreach (var format in session.formatsUsed)
                    {
                        if (!allFormats.ContainsKey(format.Key))
                            allFormats[format.Key] = 0;
                        allFormats[format.Key] += format.Value;
                    }
                }
                analytics["formatUsage"] = allFormats;

                // Performance trends
                if (sessions.Count >= 5)
                {
                    var recentSessions = sessions.TakeLast(5).ToList();
                    var oldSessions = sessions.Take(Math.Min(5, sessions.Count - 5)).ToList();
                    
                    if (oldSessions.Count > 0)
                    {
                        var recentAvgTime = recentSessions.Average(s => s.texturesProcessed > 0 ? (float)s.totalTimeTaken / s.texturesProcessed : 0f);
                        var oldAvgTime = oldSessions.Average(s => s.texturesProcessed > 0 ? (float)s.totalTimeTaken / s.texturesProcessed : 0f);
                        
                        analytics["performanceTrend"] = oldAvgTime > 0 ? (oldAvgTime - recentAvgTime) / oldAvgTime : 0f;
                    }
                }
            }

            // Operation performance
            var operationStats = new Dictionary<string, object>();
            foreach (var metric in performanceHistory.Values)
            {
                operationStats[metric.operationName] = new Dictionary<string, object>
                {
                    ["executionCount"] = metric.executionCount,
                    ["averageTime"] = metric.averageExecutionTime,
                    ["totalTime"] = metric.totalExecutionTime,
                    ["texturesProcessed"] = metric.texturesProcessed,
                    ["memoryUsed"] = metric.totalMemoryUsed
                };
            }
            analytics["operationPerformance"] = operationStats;

            return analytics;
        }

        /// <summary>
        /// Generate performance report
        /// </summary>
        public static string GeneratePerformanceReport()
        {
            var report = "=== TEXTURE COMPRESSOR PERFORMANCE REPORT ===\n\n";
            var analytics = GetPerformanceAnalytics();

            if (analytics.ContainsKey("totalSessions"))
            {
                report += $"📊 SESSION SUMMARY\n";
                report += $"Total Sessions: {analytics["totalSessions"]}\n";
                report += $"Total Textures Processed: {analytics["totalTexturesProcessed"]}\n";
                report += $"Total Time: {(long)analytics["totalTimeTaken"]:N0}ms\n";
                report += $"Average Textures/Session: {(float)analytics["averageTexturesPerSession"]:F1}\n";
                report += $"Average Time/Texture: {(float)analytics["averageTimePerTexture"]:F1}ms\n";
                report += $"Success Rate: {(float)analytics["successRate"]:P1}\n";
                report += $"Space Saved: {FormatBytes((long)analytics["totalSpaceSaved"])}\n";
                report += $"Compression Ratio: {(float)analytics["averageCompressionRatio"]:P1}\n\n";

                if (analytics.ContainsKey("performanceTrend"))
                {
                    var trend = (float)analytics["performanceTrend"];
                    var trendText = trend > 0 ? $"improved by {trend:P1}" : $"decreased by {Math.Abs(trend):P1}";
                    report += $"📈 PERFORMANCE TREND\n";
                    report += $"Recent performance has {trendText}\n\n";
                }
            }

            // Operation performance
            if (analytics.ContainsKey("operationPerformance"))
            {
                report += $"⚡ OPERATION PERFORMANCE\n";
                var operations = (Dictionary<string, object>)analytics["operationPerformance"];
                
                foreach (var op in operations)
                {
                    var stats = (Dictionary<string, object>)op.Value;
                    report += $"{op.Key}:\n";
                    report += $"  Executions: {stats["executionCount"]}\n";
                    report += $"  Avg Time: {stats["averageTime"]}ms\n";
                    report += $"  Total Time: {stats["totalTime"]}ms\n";
                    report += $"  Textures: {stats["texturesProcessed"]}\n";
                    report += $"  Memory: {FormatBytes((long)stats["memoryUsed"])}\n\n";
                }
            }

            // Format usage
            if (analytics.ContainsKey("formatUsage"))
            {
                report += $"🎨 FORMAT USAGE\n";
                var formats = (Dictionary<TextureImporterFormat, int>)analytics["formatUsage"];
                var sortedFormats = formats.OrderByDescending(f => f.Value);
                
                foreach (var format in sortedFormats)
                {
                    report += $"{format.Key}: {format.Value} textures\n";
                }
                report += "\n";
            }

            // Recent sessions
            if (sessions.Count > 0)
            {
                report += $"📋 RECENT SESSIONS\n";
                var recentSessions = sessions.TakeLast(5).Reverse();
                
                foreach (var session in recentSessions)
                {
                    report += $"{session.startTime:yyyy-MM-dd HH:mm} - ";
                    report += $"{session.texturesProcessed} textures, ";
                    report += $"{session.totalTimeTaken}ms, ";
                    report += $"{(session.averageCompressionRatio):P1} compression\n";
                }
            }

            return report;
        }

        /// <summary>
        /// Clear all performance data
        /// </summary>
        public static void ClearPerformanceData()
        {
            performanceHistory.Clear();
            sessions.Clear();
            currentSession = null;
            currentSessionTimer.Reset();
            SavePerformanceData();
        }

        /// <summary>
        /// Export performance data to CSV
        /// </summary>
        public static void ExportToCSV(string filePath)
        {
            try
            {
                var csv = "Session,StartTime,EndTime,TexturesProcessed,OriginalSize,CompressedSize,TimeTaken,CompressionRatio,Success\n";
                
                foreach (var session in sessions)
                {
                    csv += $"{sessions.IndexOf(session) + 1},";
                    csv += $"{session.startTime:yyyy-MM-dd HH:mm:ss},";
                    csv += $"{session.endTime:yyyy-MM-dd HH:mm:ss},";
                    csv += $"{session.texturesProcessed},";
                    csv += $"{session.totalOriginalSize},";
                    csv += $"{session.totalCompressedSize},";
                    csv += $"{session.totalTimeTaken},";
                    csv += $"{session.averageCompressionRatio:F3},";
                    csv += $"{session.wasSuccessful}\n";
                }
                
                System.IO.File.WriteAllText(filePath, csv);
                UnityEngine.Debug.Log($"Performance data exported to: {filePath}");
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError($"Failed to export performance data: {e.Message}");
            }
        }

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024f:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024f * 1024f):F1} MB";
            return $"{bytes / (1024f * 1024f * 1024f):F1} GB";
        }

        private static void SavePerformanceData()
        {
            // Implementation would save to persistent storage
            // For now, just log the save operation
            UnityEngine.Debug.Log("Performance data saved");
        }

        /// <summary>
        /// Get system performance info
        /// </summary>
        public static Dictionary<string, object> GetSystemInfo()
        {
            return new Dictionary<string, object>
            {
                ["processorCount"] = SystemInfo.processorCount,
                ["systemMemorySize"] = SystemInfo.systemMemorySize,
                ["graphicsMemorySize"] = SystemInfo.graphicsMemorySize,
                ["operatingSystem"] = SystemInfo.operatingSystem,
                ["unityVersion"] = Application.unityVersion,
                ["currentMemoryUsage"] = GC.GetTotalMemory(false),
                ["maxTextureSize"] = SystemInfo.maxTextureSize
            };
        }
    }
}