Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Test Project
-logFile
Logs/AssetImportWorker0.log
-srvPort
51799
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Test Project
D:/My Project/Test Project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [1484]  Target information:

Player connection [1484]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2148232598 [EditorId] 2148232598 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [1484]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2148232598 [EditorId] 2148232598 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [1484] Host joined multi-casting on [***********:54997]...
Player connection [1484] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Test Project/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56696
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006810 seconds.
- Loaded All Assemblies, in  0.760 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 448 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.125 seconds
Domain Reload Profiling: 1882ms
	BeginReloadAssembly (269ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (87ms)
	LoadAllAssembliesAndSetupDomain (300ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (294ms)
			TypeCache.Refresh (290ms)
				TypeCache.ScanAssembly (264ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1126ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1039ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (607ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (236ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.040 seconds
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.159 seconds
Domain Reload Profiling: 5195ms
	BeginReloadAssembly (2945ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (939ms)
		LoadAssemblies (3294ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (469ms)
			TypeCache.Refresh (376ms)
				TypeCache.ScanAssembly (318ms)
			BuildScriptInfoCaches (81ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1160ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (947ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (710ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3704 unused Assets / (1.0 MB). Loaded Objects now: 4190.
Memory consumption went from 83.0 MB to 82.0 MB.
Total: 8.402000 ms (FindLiveObjects: 0.683000 ms CreateObjectMapping: 0.397000 ms MarkObjects: 5.857800 ms  DeleteObjects: 1.461000 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.121 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.930 seconds
Domain Reload Profiling: 2049ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (641ms)
		LoadAssemblies (509ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (931ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (722ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (498ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.9 MB). Loaded Objects now: 4191.
Memory consumption went from 82.1 MB to 81.2 MB.
Total: 9.061500 ms (FindLiveObjects: 0.553500 ms CreateObjectMapping: 0.380600 ms MarkObjects: 6.672400 ms  DeleteObjects: 1.453800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 8822.831819 seconds.
  path: Assets/Texture compressor
  artifactKey: Guid(8d0cf41b11402dd4ab13df43f9660edf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture compressor using Guid(8d0cf41b11402dd4ab13df43f9660edf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '764cebce5e99bad811197663f119515c') in 0.0125737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3696 unused Assets / (0.9 MB). Loaded Objects now: 4194.
Memory consumption went from 82.8 MB to 81.9 MB.
Total: 15.815500 ms (FindLiveObjects: 1.031000 ms CreateObjectMapping: 0.745000 ms MarkObjects: 12.311600 ms  DeleteObjects: 1.726100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3695 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.8 MB to 81.9 MB.
Total: 8.565200 ms (FindLiveObjects: 0.705600 ms CreateObjectMapping: 0.443200 ms MarkObjects: 5.938900 ms  DeleteObjects: 1.476400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.072 seconds
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.945 seconds
Domain Reload Profiling: 2018ms
	BeginReloadAssembly (337ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (587ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (289ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (265ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (742ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.8 MB). Loaded Objects now: 4193.
Memory consumption went from 82.5 MB to 81.8 MB.
Total: 8.432400 ms (FindLiveObjects: 0.583200 ms CreateObjectMapping: 0.356500 ms MarkObjects: 6.229200 ms  DeleteObjects: 1.262300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3697 unused Assets / (1.1 MB). Loaded Objects now: 4195.
Memory consumption went from 82.7 MB to 81.7 MB.
Total: 15.705900 ms (FindLiveObjects: 0.569800 ms CreateObjectMapping: 0.367900 ms MarkObjects: 11.799400 ms  DeleteObjects: 2.966900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 101.754447 seconds.
  path: Assets/TC
  artifactKey: Guid(d24e9103f35e6df449d2705f94d06ede) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TC using Guid(d24e9103f35e6df449d2705f94d06ede) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0eef4bc40bb19ef0d919ab7df2e336a9') in 0.0031273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3695 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.7 MB to 81.9 MB.
Total: 10.902000 ms (FindLiveObjects: 0.769700 ms CreateObjectMapping: 0.497100 ms MarkObjects: 8.034800 ms  DeleteObjects: 1.598900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.076 seconds
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.979 seconds
Domain Reload Profiling: 2068ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (582ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (990ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (746ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (187ms)
			ProcessInitializeOnLoadAttributes (481ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3702 unused Assets / (0.9 MB). Loaded Objects now: 4193.
Memory consumption went from 82.5 MB to 81.7 MB.
Total: 9.600400 ms (FindLiveObjects: 1.133300 ms CreateObjectMapping: 0.445700 ms MarkObjects: 6.641400 ms  DeleteObjects: 1.378300 ms)

Prepare: number of updated asset objects reloaded= 0
